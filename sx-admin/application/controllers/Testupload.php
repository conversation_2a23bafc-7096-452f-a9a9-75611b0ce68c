<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Testupload extends CI_Controller
{
	public function index()
	{
		log_message('debug', 'Test Upload Index debug log at ' . date('c'));
		log_message('error', 'Test Upload Index error log from controller!');
		log_message('debug', 'Test Upload Index debug log from controller!');
		$this->load->helper('file');
		// write_file(APPPATH . 'logs/testfile.txt', "Hello, world!\n", 'a+');
		$this->load->view('testupload_form');
	}

	public function do_upload()
	{
		log_message('debug', 'File Upload debug log at ' . date('c'));
		log_message('error', 'File Upload error log from controller!');
		log_message('debug', 'File Upload debug log from controller!');

		// log_message('debug', 'PHP running as: ' . get_current_user());
		$upload_path = './uploads/organizations';
		if (!is_dir($upload_path)) {
			mkdir($upload_path, 0775, true);
		}

		$config['upload_path']   = $upload_path;
		$config['allowed_types'] = 'gif|jpg|png|jpeg|pdf|txt';
		$config['max_size']      = 2048; // 2 MB

		$this->load->library('upload', $config);

		if (!$this->upload->do_upload('userfile')) {
			$error = $this->upload->display_errors();
			log_message('error', 'Test Upload Failed: ' . $error);
			$this->session->set_flashdata('error', $error);
			redirect('testupload');
		} else {
			$data = $this->upload->data();
			log_message('debug', 'Test Upload Success: ' . json_encode($data));
			$this->session->set_flashdata('success', 'Upload Success! File: ' . $data['file_name']);
			redirect('testupload');
		}
	}
}
