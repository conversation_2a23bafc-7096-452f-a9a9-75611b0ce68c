<?php

class User extends CI_Controller
{

	protected $users;
	protected $theme;
	protected $privilege;
	protected $structure;
	protected $party_type;
	protected $organization;
	protected $business_unit;
	protected $user_privilege;
	protected $privilege_type;
	protected $module_features;
	protected $privilege_modules;
	protected $user_organizations;
	protected $user_privilege_access;

	public function __construct()
	{
		parent::__construct();
		if (!$this->session->userdata('userid')) {
			return redirect('/');
		}
		$this->users = $this->SxUsers;
		$this->theme = $this->SxTheme;
		$this->organization = $this->SxOrganization;
		$this->structure = $this->SxStructureMaster;
		$this->privilege = $this->SxPrevilleges;
		$this->party_type = $this->SxPartyType;
		$this->privilege_type = $this->SxPrevilegeType;
		$this->user_privilege = $this->SxUserPrevilege;
		$this->privilege_modules = $this->SxPrevillegeModules;
		$this->module_features = $this->SxModuleFeatures;
		$this->user_privilege_access = $this->SxUserPrevilegeAccessRule;
		$this->user_organizations = $this->SxUserOrganizations;
	}

	public function index()
	{
		$user_type = get_user_type();
		if ($user_type == 'appadmin') {
			/*$users = $this->users->findAll();*/
			$users = $this->users
			->join('sx_organization', 'sx_organization.id = sx_users.default_org_id')
            ->select('sx_users.*,sx_organization.org_id')
			->findAll();
			/*$users = $this->users
			->join('sx_user_organizations', 'sx_user_organizations.user_id = sx_users.id')
			->join('sx_organization', 'sx_organization.id = sx_user_organizations.org_id')
            ->select('sx_users.*,sx_organization.*,sx_organization.org_id as new_org_id')
			->findAll();*/
		} else {
			if (!check_super_user()) {
				return show_403('master');
			}
			$organization_users = $this->user_organizations->where(['org_id' => get_current_org_id()])->findAll();
			$user_ids = [];
			foreach ($organization_users as $org_user) {
				$user_ids[] = $org_user['user_id'];
			}
			if (count($user_ids)) {
				$users = $this->users->whereIn('id', $user_ids)->findAll();
			} else {
				$users = [];
			}
		}
		foreach ($users as $key => $user) {
			$user_default_org = $this->user_organizations
			->join('sx_organization', 'sx_user_organizations.org_id = sx_organization.id')->where(["sx_user_organizations.org_id" => $user['default_org_id'], 'sx_user_organizations.user_id' => $user['id']])->select('sx_user_organizations.*,sx_organization.org_id as new_org_id')->row_array();
			$privilege_names = [];
			$privilege_ids = [];
			if (isset($user_default_org['roles']) && $user_default_org['roles']) {
				$privileges = $this->privilege->whereIn('id', json_decode($user_default_org['roles']))->findAll();
				foreach ($privileges as $privilege) {
					$privilege_names[] = $privilege['previllege_name'];
					$privilege_ids[] = $privilege['previllege_id'];
				}
			}
			if(!isset($user['org_id'])){
				$users[$key]['org_id'] = $user['default_org_id'];
				if(!empty($user_default_org)){
					$users[$key]['org_id'] = $user_default_org['new_org_id'];
				}
			}
			$users[$key]['privliege_names'] = implode(', ', $privilege_names);
			$users[$key]['privilege_ids'] = implode(', ', $privilege_ids);
		}
		return $this->settemplate->master('users/index', compact('users'));
	}

	public function add()
	{
		$user_type = get_user_type();
		$theme = $this->theme->getActiveRecords();
		$organization = $this->organization->getActiveRecords();
		$languages = $this->SxLanguages->getActiveRecords();
		$structures = [];
		if($user_type == "orgadmin"){
			$structures = $this->structure->where(["org_id" => get_current_org_id(), "status" => 1])->getActiveRecords();
		}
		return $this->settemplate->master('users/add', compact('theme', 'organization', 'languages', 'user_type','structures'));
	}

	public function storeold()
	{
		$theme = $this->theme->getActiveRecords();
		$organization = $this->organization->getActiveRecords();
		$config = [
			[
				"field" => "employee_id",
				"label" => 'Employee Id',
				"rules" => 'required|is_unique[sx_users.employee_id]'
			],
			[
				"field" => "employee_name",
				"label" => 'Employee Name',
				"rules" => 'required|is_unique[sx_users.employee_name]'
			],
			[
				"field" => "username",
				"label" => 'Username',
				"rules" => 'required|is_unique[sx_users.username]'
			],
			[
				"field" => "password",
				"label" => 'Password',
				"rules" => 'required'
			],
			[
				"field" => "effective_from",
				"label" => 'Effective From',
				"rules" => 'required'
			],
			[
				"field" => "effective_to",
				"label" => 'Effective To',
				"rules" => 'required'
			],
			[
				"field" => "contact_num",
				"label" => 'Contact Number',
				"rules" => 'required|numeric'
			],
			[
				"field" => "theme_id",
				"label" => 'Theme Id',
				"rules" => 'required'
			],
			[
				"field" => "currency",
				"label" => 'Currency',
				"rules" => 'required'
			],
			[
				"field" => "privilege_id[]",
				"label" => 'Privilege Id',
				"rules" => 'required'
			],
			[
				"field" => "org_id",
				"label" => 'Organization Id',
				"rules" => 'required'
			],
		];
		$this->form_validation->set_rules($config);
		if ($this->form_validation->run() == FALSE) {
			return $this->settemplate->master('users/add', compact('theme', 'organization'));
		}
		$insert = [
			"employee_id" => $this->input->post('employee_id'),
			"employee_name" => $this->input->post('employee_name'),
			"username" => $this->input->post('username'),
			"password" => md5($this->input->post('password')),
			"effective_fromdate" => date('Y-m-d', strtotime($this->input->post('effective_from'))),
			"effective_enddate" => date('Y-m-d', strtotime($this->input->post('effective_to'))),
			"contact_num" => $this->input->post('contact_num'),
			"theme_id" => $this->input->post('theme_id'),
			"currency" => $this->input->post('currency'),
			"org_id" => $this->input->post('org_id'),
		];
		if ($this->users->insert($insert)) {
			$user = $this->users->lastInsertId();
			$insert = [];
			foreach ($this->input->post('privilege_id[]') as $privilege) {
				$privilege = $this->privilege->where(['id' => $privilege])->row_array();
				$insert[] = [
					'org_id' => $this->input->post('org_id'),
					'previllege_id' => $privilege['id'],
					'previlege_type' => $privilege['previlege_type'],
					'party_type' => $privilege['party_type'],
					'business_entity' => $privilege['business_entity'],
					'structure_id' => $privilege['structure_id'],
					'user_id' => $user
				];
			}
			if (count($insert)) {
				if ($this->user_privilege->insertBatch($insert)) {
					$this->session->set_flashdata('success', 'User Added Successfully');
					return  redirect('/users');
				}
			}
			$this->session->set_flashdata('error', 'Something went wrong. Please try again later.');
			return  redirect('/users');
		}
		$this->session->set_flashdata('error', 'Something went wrong. Please try again later.');
		return  redirect('/users');
	}


	public function store()
	{
		$config = [
			[
				"field" => "employee_id",
				"label" => 'Employee Id',
				"rules" => 'required|is_unique[sx_users.employee_id]'
			],
			[
				"field" => "employee_name",
				"label" => 'Employee Name',
				"rules" => 'required'
			],
			[
				"field" => "username",
				"label" => 'Username',
				"rules" => 'required|is_unique[sx_users.username]'
			],
			[
				"field" => "password",
				"label" => 'Password',
				"rules" => 'required'
			],
			[
				"field" => "confirm_password",
				"label" => 'Confirm Password',
				"rules" => 'required|matches[password]'
			],
			[
				"field" => "effective_from",
				"label" => 'Effective From',
				"rules" => 'required'
			],
			[
				"field" => "effective_to",
				"label" => 'Effective To',
				"rules" => 'required'
			],
			[
				"field" => "contact_num",
				"label" => 'Contact Number',
				"rules" => 'required|numeric'
			],
			[
				"field" => "theme_id",
				"label" => 'Theme Id',
				"rules" => 'required'
			],
			[
				"field" => "currency",
				"label" => 'Currency',
				"rules" => 'required'
			],
			[
				"field" => "date_format",
				"label" => 'Date Format',
				"rules" => 'required'
			],
			[
				"field" => "number_format",
				"label" => 'Number Format',
				"rules" => 'required'
			],
			[
				"field" => "languages[]",
				"label" => 'Languages',
				"rules" => 'required'
			],
			[
				"field" => "associations[]",
				"label" => 'associations',
				"rules" => 'required'
			],
		];
		$this->form_validation->set_rules($config);
		if ($this->form_validation->run() == FALSE) {
			$errors = $this->form_validation->error_array();
			return $this->output->set_status_header(406)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['errors' => $errors, 'token' => $this->security->get_csrf_hash()]));
		} else {

			$post = $this->input->post(NULL, TRUE);
			$associations = $this->input->post('associations');
			$privilegeIds = $this->extractPrivilegeIds($associations);

			if (empty($privilegeIds)) {
				throw new Exception('No valid privilege IDs provided');
			}
			$orgId = $associations[0]['org_id'];
			foreach ($privilegeIds as $privilegeId) {
				$privilegeId = is_array($privilegeId) ? (int)($privilegeId[0] ?? 0) : (int)$privilegeId;
				if ($privilegeId === 0) {
					continue;
				}
				$this->processPrivilege($privilegeId, $post, $orgId);
			}
			$post['org_id'] = $orgId;
			$post['emailid'] = $post['username'];
			$post['effective_from'] = date('Y-m-d', strtotime($post['effective_from']));
			$post['effective_to'] = date('Y-m-d', strtotime($post['effective_to']));
			$apiResponse = $this->callRegisterApi($post);
			if(isset($apiResponse['status']) && $apiResponse['status'] == 'success') {
				$user = $apiResponse['user'] ?? 0;
				$insert = [];
				if($user > 0){
					foreach ($associations as $association) {
						$created_by = 0;
						if (strtolower($this->input->post('user_type')) == 'user') {
							$created_by = (int)$this->input->post('user_id',true);
						}
						$insert[] = [
							'org_id' => $association['org_id'],
							'entity_value_id' => $association['entity_value_id'],
							'entity_id' => $association['entity_id'],
							'structure_id' => $association['structure_id'],
							'roles' => json_encode($association['privilege_id'], JSON_UNESCAPED_SLASHES),
							'user_id' => $user,
							"created_by" => $created_by
						];
					}
					if (!empty($insert)) {
						if ($this->user_organizations->insertBatch($insert)) {
							return $this->output->set_status_header(200)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['message' => "User Added Successfully"]));
						} else {
							$this->users->db->where('id', $user)->delete($this->users->table);
							return $this->output->set_status_header(500)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['error' => "Somwthing Went Wrong. Please Try Again Later!", 'token' => $this->security->get_csrf_hash()]));
						}
					} else {
						$this->users->db->where('id', $user)->delete($this->users->table);
						return $this->output->set_status_header(500)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['error' => "Somwthing Went Wrong. Please Try Again Later!", 'token' => $this->security->get_csrf_hash()]));
					}
				}else{
					$this->users->db->where('id', $user)->delete($this->users->table);
						return $this->output->set_status_header(500)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['error' => "Somwthing Went Wrong. Please Try Again Later!", 'token' => $this->security->get_csrf_hash()]));
				}
			}else {
				return $this->output->set_status_header(500)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['error' => "Somwthing Went Wrong. Please Try Again Later!", 'token' => $this->security->get_csrf_hash()]));
			}
		}
	}

	private function extractPrivilegeIds($associations): array
	{
		$privilegeIds = [];
		
		if (is_array($associations) && !empty($associations)) {
			$privilegeIds = array_filter(
				array_column($associations, 'privilege_id'),
				fn($id) => !empty($id)
			);
		}
		
		return $privilegeIds;
	}

	private function processPrivilege(int $privilegeId, array $post, int $orgId): void
	{
		$privilege = $this->getPrivilegeData($privilegeId);
		
		if (!$privilege) {
			throw new Exception("Invalid privilege ID: {$privilegeId}");
		}

		$typeName = $this->getTypeName($privilege['previlege_type']);
		// $partyName = $this->getPartyName($privilege['party_type']);
		
		// if ($typeName !== 'party' || in_array($partyName, ['customer', 'carrier'])) {
		if ($typeName !== 'party') {
			return;
		}

		$partnerId = $this->createPartner($post, $privilege['party_type'], $orgId);
		$this->updatePartnerCode($partnerId, $post);
	}

	private function getPrivilegeData(int $privilegeId): ?array
	{
		$query = $this->db->query("SELECT previlege_type, party_type FROM sx_previlleges WHERE id = ?",[$privilegeId]);
		return $query->num_rows() > 0 ? $query->row_array() : null;
	}

	private function getTypeName(int $typeId): ?string
	{
		$query = $this->db->query("SELECT type_name FROM sx_previlege_types WHERE id = ?",[$typeId]);
		return $query->num_rows() > 0 ? strtolower($query->row_array()['type_name']) : null;
	}

	private function getPartyName(int $partyType): ?string
	{
		if (!$partyType) {
			return null;
		}
		
		$query = $this->db->query("SELECT type_name FROM sx_party_types WHERE id = ?",[$partyType]);
		return $query->num_rows() > 0 ? strtolower($query->row_array()['type_name']) : null;
	}

	private function createPartner(array $post, int $partyType, int $orgId): int
	{
		$partnerData = [
			'party_type' => $partyType,
			'name' => $post['username'],
			'email' => $post['employee_name'] . '@gmail.com',
			'mobile' => $post['contact_num'],
			'user_id' => 0,
			'code' => 0,
			'company_code' => '',
			'branch_code' => '',
			'department_code' => '',
			'customer_code' => 0,
			'location_id' => '',
			'city' => '',
			'country' => '',
			'state' => '',
			'division_name' => '',
			'street' => '',
			'pincode' => 0,
			'street_2' => '',
			'street_3' => '',
			'building' => 0,
			'house_number' => 0,
			'party_types' => $partyType,
			'extension' => 0,
			'latitude' => 0,
			'longitude' => 0,
			'homepage' => '',
			'category_type' => 0,
			'category_id' => 0,
			'time_zone_city' => 0,
			'time_zone_code' => 0,
			'time_zone_name' => 0,
			'acon_debitor_code' => 0,
			'password' => password_hash($post['password'], PASSWORD_DEFAULT),
			'status' => 1,
			'created_at' => date('Y-m-d H:i:s'),
			'offering_type' => 0,
			'carrier_grade' => 0,
			'org_id' => $orgId,
			'entity' => 0,
			'location_reference_name' => '',
			'logo' => ''
		];
		return $this->common->insertTableData('sx_party_members', $partnerData);
	}

	private function updatePartnerCode(int $partnerId, array $post): void
	{
		$ccode = mb_substr($this->session->userdata('org_name'), 0, 2);
		$id = str_pad($partnerId, 6, '0', STR_PAD_LEFT);
		$custCode = $ccode . $id;

		$this->db->query("UPDATE sx_party_members SET code = ?, customer_code = ?, party_id = ? WHERE id = ?",[$custCode, $custCode, $custCode, $partnerId]);
	}

	private function callRegisterApi(array $data): array
    {
        $apiUrl = 'http://backend/api/register';

        $curl = curl_init();
		curl_setopt_array($curl, array(
			CURLOPT_URL => $apiUrl,
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_MAXREDIRS => 10,
			CURLOPT_TIMEOUT => 0,
			CURLOPT_CUSTOMREQUEST => 'POST',
			CURLOPT_POSTFIELDS =>json_encode($data),
			CURLOPT_HTTPHEADER => array(
				'Content-Type: application/json'
			)
		));

		$response = curl_exec($curl);
		
        if (curl_errno($curl)) {
			$responseData['message'] = 'API call failed: ' . curl_error($curl);
        }
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        $responseData = json_decode($response, true);

        if ($httpCode != 201 || $responseData['status'] != 'success') {
            $responseData['message'] = 'API response invalid: ' . ($responseData['message'] ?? 'Unknown error');
        }
        return $responseData;
    }

	
	private function successResponse(string $message): array
	{
		return [
			'status' => 'success',
			'message' => $message
		];
	}

	private function errorResponse(string $message): array
	{
		return [
			'status' => 'error',
			'message' => $message
		];
	}

	public function show($id)
	{
		$user = $this->users->where(['employee_id' => $id])->row_array();
		if (!$user) {
			return show_404();
		}
		$user_type = get_user_type();
		$theme = $this->theme->getActiveRecords();
		$organizations = $this->organization->getActiveRecords();
		$languages = $this->SxLanguages->getActiveRecords();
		// $user_privileges = $this->user_privilege->where(['user_id' => $user['id']])->select('id')->getActiveRecords();
		$user_organizations = $this->SxUserOrganizations->where(['user_id' => $user['id']])->getActiveRecords();
		return $this->settemplate->master('users/view', compact('user', 'theme', 'organizations', 'languages', 'user_organizations', 'user_type'));
	}

	public function edit($id)
	{
		$user = $this->users->where(['employee_id' => $id])->row_array();
		if (!$user) {
			return show_404();
		}
		$user_type = get_user_type();
		$theme = $this->theme->getActiveRecords();
		$organizations = $this->organization->getActiveRecords();
		$languages = $this->SxLanguages->getActiveRecords();
		// $user_privileges = $this->user_privilege->where(['user_id' => $user['id']])->select('id')->getActiveRecords();
		$user_organizations = $this->SxUserOrganizations->where(['user_id' => $user['id']])->getActiveRecords();
		return $this->settemplate->master('users/edit', compact('user', 'theme', 'organizations', 'languages', 'user_organizations', 'user_type'));
	}

	public function status($id)
	{
		$user = $this->users->where(['employee_id' => $id])->row_array();
		if (!$user) {
			return show_404();
		}
		if ($this->users->update($user['id'], ['status' => $user['status'] ? 0 : 1])) {
			$this->session->set_flashdata('success', 'Status Changed Successfully');
			return  redirect('/users');
		}
		$this->session->set_flashdata('error', 'Something went wrong. Please try again later.');
		return  redirect('/users');
	}

	public function delete($id)
	{
		$user = $this->users->where(['employee_id' => $id])->row_array();
		if (!$user) {
			return show_404();
		}
		if ($this->users->delete(['employee_id' => $id])) {
			$this->session->set_flashdata('success', 'User Deleted Successfully');
			return  redirect('/users');
		}
		$this->session->set_flashdata('error', 'Something went wrong. Please try again later.');
		return  redirect('/users');
	}

	public function updateold($id)
	{
		$user = $this->users->where(['employee_id' => $id])->row_array();
		if (!$user) {
			return show_404();
		}
		$theme = $this->theme->getActiveRecords();
		$organization = $this->organization->getActiveRecords();
		$user_privilege = $this->user_privilege->where(['user_id' => $user['id']])->row_array();
		$config = [
			[
				"field" => "employee_id",
				"label" => 'Employee Id',
				"rules" => 'required'
			],
			[
				"field" => "employee_name",
				"label" => 'Employee Name',
				"rules" => 'required'
			],
			[
				"field" => "username",
				"label" => 'Username',
				"rules" => 'required'
			],
			[
				"field" => "effective_from",
				"label" => 'Effective From',
				"rules" => 'required'
			],
			[
				"field" => "effective_to",
				"label" => 'Effective To',
				"rules" => 'required'
			],
			[
				"field" => "contact_num",
				"label" => 'Contact Number',
				"rules" => 'required|numeric'
			],
			[
				"field" => "theme_id",
				"label" => 'Theme Id',
				"rules" => 'required'
			],
			[
				"field" => "currency",
				"label" => 'Currency',
				"rules" => 'required'
			],
			[
				"field" => "privilege_id[]",
				"label" => 'Privilege Id',
				"rules" => 'required'
			],
			[
				"field" => "org_id",
				"label" => 'Organization Id',
				"rules" => 'required'
			],
		];
		$this->form_validation->set_rules($config);
		if ($this->form_validation->run() == FALSE) {
			return $this->settemplate->master('users/edit', compact('user', 'theme', 'organization', 'user_privilege'));
		}
		$unique = $this->users
			->whereNotIn('id', [$user['id']])
			->groupStart()
			->where(['employee_id' => $this->input->post('employee_id')])
			->orGroupStart()
			->where(['employee_name' => $this->input->post('employee_name')])
			->groupEnd()
			->groupEnd()->row_array();
		if ($unique) {
			$config = [];
			if ($unique['employee_name'] == $this->input->post('employee_name')) {
				$config[] = [
					"field" => "employee_id",
					"label" => 'Employee Id',
					"rules" => 'required|is_unique[sx_users.employee_id]'
				];
			}
			if ($unique['employee_id'] == $this->input->post('employee_id')) {
				$config[] = [
					"field" => "employee_name",
					"label" => 'Employee Name',
					"rules" => 'required|is_unique[sx_users.employee_name]'
				];
			}
			$this->form_validation->set_rules($config);
			if ($this->form_validation->run() == FALSE) {
				return $this->settemplate->master('users/edit', compact('user', 'theme', 'organization', 'user_privilege'));
			}
		}
		$insert = [
			"employee_id" => $this->input->post('employee_id'),
			"employee_name" => $this->input->post('employee_name'),
			"username" => $this->input->post('username'),
			"effective_fromdate" => date('Y-m-d', strtotime($this->input->post('effective_from'))),
			"effective_enddate" => date('Y-m-d', strtotime($this->input->post('effective_to'))),
			"contact_num" => $this->input->post('contact_num'),
			"theme_id" => $this->input->post('theme_id'),
			"currency" => $this->input->post('currency'),
			"org_id" => $this->input->post('org_id'),
		];
		if ($password = $this->input->post('password')) {
			$insert["password"] = md5($password);
		}
		if ($this->users->update($user['id'], $insert)) {
			$insert = [];
			$update = [];
			$this->user_privilege->setAllStatusInactive($user['id']);
			foreach ($this->input->post('privilege_id') as $privilegeid) {
				$user_privilege = $this->user_privilege->where(['id' => $privilegeid, 'user_id' => $user['id']])->row_array();
				$privilege = $this->privilege->where(['id' => $privilegeid])->row_array();
				$commonData = [
					'org_id' => $this->input->post('org_id'),
					'previllege_id' => $privilege['id'],
					'previlege_type' => $privilege['previlege_type'],
					'party_type' => $privilege['party_type'],
					'business_entity' => $privilege['business_entity'],
					'structure_id' => $privilege['structure_id'],
					'user_id' => $user['id'],
				];
				if ($user_privilege) {
					$commonData['id'] = $user_privilege['id'];
					$commonData['status'] = 1;
					$update[] = $commonData;
				} else {
					$insert[] = $commonData;
				}
			}
			if (count($insert)) {
				$this->user_privilege->insertBatch($insert);
			}
			if (count($update)) {
				$this->user_privilege->updateBatch($update, 'id');
			}
			$this->session->set_flashdata('success', 'User Updated Successfully');
			return  redirect('/users');
			// $this->session->set_flashdata('error', 'Something went wrong. Please try again later.');
			// return $this->settemplate->master('users/edit', compact('user', 'theme', 'organization', 'user_privilege'));
		}
		$this->session->set_flashdata('error', 'Something went wrong. Please try again later.');
		return  redirect('/users');
	}

	public function update($id)
	{
		$user = $this->users->where(['employee_id' => $id])->row_array();
		if (!$user) {
			return show_404();
		}
		$config = [
			[
				"field" => "employee_id",
				"label" => 'Employee Id',
				"rules" => 'required'
			],
			[
				"field" => "employee_name",
				"label" => 'Employee Name',
				"rules" => 'required'
			],
			[
				"field" => "username",
				"label" => 'Username',
				"rules" => 'required'
			],
			[
				"field" => "effective_from",
				"label" => 'Effective From',
				"rules" => 'required'
			],
			[
				"field" => "effective_to",
				"label" => 'Effective To',
				"rules" => 'required'
			],
			[
				"field" => "contact_num",
				"label" => 'Contact Number',
				"rules" => 'required|numeric'
			],
			[
				"field" => "theme_id",
				"label" => 'Theme Id',
				"rules" => 'required'
			],
			[
				"field" => "currency",
				"label" => 'Currency',
				"rules" => 'required'
			],
			[
				"field" => "date_format",
				"label" => 'Date Format',
				"rules" => 'required'
			],
			[
				"field" => "number_format",
				"label" => 'Number Format',
				"rules" => 'required'
			],
			[
				"field" => "languages[]",
				"label" => 'Languages',
				"rules" => 'required'
			],
			[
				"field" => "associations[]",
				"label" => 'associations',
				"rules" => 'required'
			],
		];
		$this->form_validation->set_rules($config);
		if ($this->form_validation->run() == FALSE) {
			$errors = $this->form_validation->error_array();
			return $this->output->set_status_header(406)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['errors' => $errors, 'token' => $this->security->get_csrf_hash()]));
		}
		$unique = $this->users
			->whereNotIn('id', [$user['id']])
			->where(['employee_id' => $this->input->post('employee_id')])->row_array();
		if ($unique) {
			$config = [];
			if ($unique['employee_name'] == $this->input->post('employee_name')) {
				$config[] = [
					"field" => "employee_id",
					"label" => 'Employee Id',
					"rules" => 'required|is_unique[sx_users.employee_id]'
				];
			}
			$this->form_validation->set_rules($config);
			if ($this->form_validation->run() == FALSE) {
				$errors = $this->form_validation->error_array();
				return $this->output->set_status_header(406)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['errors' => $errors, 'token' => $this->security->get_csrf_hash()]));
			}
		}
		$insert = [
			"employee_id" => $this->input->post('employee_id'),
			"employee_name" => $this->input->post('employee_name'),
			"username" => $this->input->post('username'),
			"effective_fromdate" => date('Y-m-d', strtotime($this->input->post('effective_from'))),
			"effective_enddate" => date('Y-m-d', strtotime($this->input->post('effective_to'))),
			"contact_num" => $this->input->post('contact_num'),
			"theme_id" => $this->input->post('theme_id'),
			"currency" => $this->input->post('currency'),
			"number_format" => $this->input->post('number_format'),
			"languages" => json_encode($this->input->post('languages'), JSON_UNESCAPED_SLASHES),
			"date_format" => $this->input->post('date_format'),
			"default_org_id" => $this->input->post('associations')[0]['org_id'],
		];
		if ($password = $this->input->post('password')) {
			$insert["password"] = md5($password);
		}
		if (strtolower($this->input->post('user_type')) == 'user') {
			$insert['created_by'] = (int)$this->input->post('user_id');
		}
		if ($this->users->update($user['id'], $insert)) {
			$insert = [];
			$update = [];
			$this->user_organizations->setAllStatusInactive($user['id']);
			foreach ($this->input->post('associations') as $association) {
				$created_by = 0;
				if (strtolower($this->input->post('user_type')) == 'user') {
					$created_by = (int)$this->input->post('user_id');
				}
				$user_organization = $this->user_organizations->where([
					'org_id' => $association['org_id'], 'user_id' => $user['id'], 'entity_value_id' => $association['entity_value_id'], 'entity_id' => $association['entity_id'], 'structure_id' => $association['structure_id']
				])->row_array();

				$commonData = [
					'org_id' => $association['org_id'],
					'entity_value_id' => $association['entity_value_id'],
					'entity_id' => $association['entity_id'],
					'structure_id' => $association['structure_id'],
					'roles' => json_encode($association['privilege_id'], JSON_UNESCAPED_SLASHES),
					'user_id' => $user['id'],
					"created_by" => $created_by
				];
				if ($user_organization) {
					$commonData['id'] = $user_organization['id'];
					$commonData['status'] = 1;
					$update[] = $commonData;
				} else {
					$insert[] = $commonData;
				}
			}
			if (count($insert)) {
				$this->user_organizations->insertBatch($insert);
			}
			if (count($update)) {
				$this->user_organizations->updateBatch($update, 'id');
			}
			return $this->output->set_status_header(200)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['message' => "User Updated Successfully", 'update' => $update]));
		} else {
			return $this->output->set_status_header(500)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['error' => "Somwthing Went Wrong. Please Try Again Later!", 'token' => $this->security->get_csrf_hash()]));
		}
	}
}
