<?php

class Privileges extends CI_Controller
{
	protected $previlleges;
	protected $organization;
	protected $party_type;
	protected $privilege_type;
	protected $business_unit;
	protected $module_features;
	protected $privilege_modules;
	protected $organization_modules;
	protected $privilege_access_rules;

	public function __construct()
	{
		parent::__construct();
		if (!$this->session->userdata('userid')) {
			return redirect('/');
		}
		$this->previlleges = $this->SxPrevilleges;
		$this->organization = $this->SxOrganization;
		$this->party_type = $this->SxPartyType;
		$this->privilege_type = $this->SxPrevilegeType;
		$this->privilege_modules = $this->SxPrevillegeModules;
		$this->organization_modules = $this->SxOrganizationModule;
		$this->module_features = $this->SxModuleFeatures;
		$this->privilege_access_rules = $this->SxPrevilegeAccessRules;
	}

	public function index()
	{
		$previlleges = $this->previlleges
		->join('sx_organization', 'sx_organization.id = sx_previlleges.org_id')
		->join('sx_previlege_types AS pt', 'pt.id = sx_previlleges.previlege_type')
        ->select('*,sx_organization.org_id as new_org_id, pt.type_name AS previlege_type_name')
		->findAll();
		return $this->settemplate->master('privileges/index', compact('previlleges'));
	}

	public function add()
	{
		$organizations = $this->organization->getActiveRecords();
		$party_types = $this->party_type->getActiveRecords();
		$privilege_types = $this->privilege_type->getActiveRecords();
		return $this->settemplate->master('privileges/add', compact('organizations', 'party_types', 'privilege_types'));
	}

	public function store()
	{
		$organizations = $this->organization->getActiveRecords();
		$party_types = $this->party_type->getActiveRecords();
		$privilege_types = $this->privilege_type->getActiveRecords();
		$config = [
			[
				"field" => "previllege_id",
				"label" => 'Privilege Id',
				"rules" => 'required|is_unique[sx_previlleges.previllege_id]'
			],
			[
				"field" => "previllege_name",
				"label" => 'Privilege Name',
				"rules" => 'required'
			],
			[
				"field" => "structure_id",
				"label" => 'Structure Id',
				"rules" => 'required'
			],
			[
				"field" => "org_id",
				"label" => 'Organization Id',
				"rules" => 'required'
			],
			[
				"field" => "previlege_type",
				"label" => 'Privilege Type',
				"rules" => 'required'
			],
			[
				"field" => "business_entity",
				"label" => 'Business Entity',
				"rules" => 'required'
			],
			[
				"field" => "org_modules[]",
				"label" => 'Organization Modules',
				"rules" => 'required'
			],
		];
		if (strtolower($this->SxPrevilegeType->where(["id" => $this->input->post('previlege_type')])->row_array()['type_name']) != 'user') {
			$config[] = [
				"field" => "party_type",
				"label" => 'Party Type',
				"rules" => 'required'
			];
		}
		$this->form_validation->set_rules($config);
		if ($this->form_validation->run() == FALSE) {
			return $this->settemplate->master('privileges/add', compact('organizations', 'party_types', 'privilege_types'));
		}
		$insert = ["previllege_id" => $this->input->post('previllege_id'), "previllege_name" => $this->input->post('previllege_name'), "previllege_description" => $this->input->post('previllege_description') ?: $this->input->post('previllege_name'), "previlege_type" => $this->input->post('previlege_type'), "party_type" => $this->input->post('party_type'), "business_entity" => $this->input->post('business_entity'), "org_id" => $this->input->post('org_id'), "structure_id" => $this->input->post('structure_id')];
		/* $condition = $this->previlleges->where(["previlege_type" => $insert['previlege_type'], "party_type" => $insert['party_type'], "business_entity" => $insert['business_entity'], "org_id" => $insert['org_id'], "structure_id" => $insert['structure_id']])->row_array();
        if ($condition) {
            $this->session->set_flashdata('error', 'Please change the atleast one of the following list because with this combination already exists. Organization, Privilege Type, Party Type, Structure, Business Entity');
            return $this->settemplate->master('privileges/add', compact('organizations', 'party_types', 'privilege_types'));
        } */
		if ($this->previlleges->insert($insert)) {
			$previllege_id = $this->previlleges->lastInsertId();
			$modules_array = [];
			$modules = $this->input->post('org_modules');
			foreach ($modules as $key => $module) {
				$modules_array[] = [
					"previllege_id" => $previllege_id,
					"module_id" => $module
				];
			}
			if (count($modules)) {
				$this->privilege_modules->insertBatch($modules_array);
			}
			$this->session->set_flashdata('success', 'Previllege Added Successfully');
			return  redirect('privileges/edit/' . $insert['previllege_id'] . '?tab=access');
		}
		$this->session->set_flashdata('error', 'Something went wrong. Please try again.');
		return  redirect('privileges');
	}

	public function status($id)
	{
		$previllege = $this->previlleges->where(['previllege_id' => $id])->row_array();
		if (!$previllege) {
			return show_404();
		}
		if ($this->previlleges->update($previllege['id'], ['status' => $previllege['status'] ? 0 : 1])) {
			$this->session->set_flashdata('success', 'Status Changed Successfully');
			return  redirect('privileges');
		}
		$this->session->set_flashdata('error', 'Something went wrong. Please try again.');
		return  redirect('privileges');
	}

	public function edit($id)
	{
		$previllege = $this->previlleges->where(['previllege_id' => $id])->row_array();
		if (!$previllege) {
			return show_404();
		}
		$organizations = $this->organization->getActiveRecords();
		$party_types = $this->party_type->getActiveRecords();
		$privilege_types = $this->privilege_type->getActiveRecords();
		$tab = @$this->input->get('tab') ?: 'privilege';
		$selected_modules = $this->privilege_modules->join('sx_organization_modules as sxom', 'sx_previllege_modules.module_id = sxom.id')->join('sx_modules as sxm', 'sxm.id  = sxom.module_id')->where(['sx_previllege_modules.previllege_id' => $previllege['id'], 'sxom.status' => 1, 'sxm.status' => 1])->select('*, sx_previllege_modules.id as privilege_module_id,  sxom.id as org_module_id, sxm.id as oirginal_module_id')->getActiveRecords();

		/*$selectedAccessRules = $this->privilege_access_rules
			->join('sx_previlege_access_rules as sxpar', 'sxpar.previllege_id', '=', 'sx_previllege_modules.previllege_id')
			->join('sx_module_features as sxmf', 'sxmf.id', '=', 'sxpar.feature_id')
			->where([
				'sxpar.previllege_id' => $previllege['id'],
				'sxpar.status' => 1,
				'sxmf.status' => 1,
				'sx_previlege_access_rules.status' => 1,
			])
			->select('sxmf.feature_name', 'sxpar.*')
			->getActiveRecords();*/

		return $this->settemplate->master('privileges/edit', compact('organizations', 'party_types', 'privilege_types', 'previllege', 'tab', 'selected_modules'));
	}

	public function delete($id)
	{
		$previllege = $this->previlleges->where(['previllege_id' => $id])->row_array();
		if (!$previllege) {
			return show_404();
		}
		if ($this->previlleges->delete(['previllege_id' => $id])) {
			$this->session->set_flashdata('success', 'Privilege Deleted Successfully');
			return  redirect('privileges');
		}
		$this->session->set_flashdata('error', 'Something went wrong. Please try again.');
		return  redirect('privileges');
	}

	public function update($id)
	{
		$previllege = $this->previlleges->where(['previllege_id' => $id])->row_array();
		if (!$previllege) {
			return show_404();
		}
		$organizations = $this->organization->getActiveRecords();
		$party_types = $this->party_type->getActiveRecords();
		$privilege_types = $this->privilege_type->getActiveRecords();
		$tab = @$this->input->get('tab') ?: 'privilege';
		$selected_modules = $this->privilege_modules->join('sx_organization_modules as sxom', 'sx_previllege_modules.module_id = sxom.id')->join('sx_modules as sxm', 'sxm.id  = sxom.module_id')->where(['sx_previllege_modules.previllege_id' => $previllege['id'], 'sxm.status' => 1, 'sxom.status' => 1])->select('*, sx_previllege_modules.id as privilege_module_id,  sxom.id as org_module_id, sxm.id as oirginal_module_id')->getActiveRecords();
		$config = [
			[
				"field" => "previllege_id",
				"label" => 'Privilege Id',
				"rules" => 'required'
			],
			[
				"field" => "previllege_name",
				"label" => 'Privilege Name',
				"rules" => 'required'
			],
			[
				"field" => "structure_id",
				"label" => 'Structure Id',
				"rules" => 'required'
			],
			[
				"field" => "org_id",
				"label" => 'Organization Id',
				"rules" => 'required'
			],
			[
				"field" => "previlege_type",
				"label" => 'Privilege Type',
				"rules" => 'required'
			],
			[
				"field" => "business_entity",
				"label" => 'Business Entity',
				"rules" => 'required'
			],
			[
				"field" => "org_modules[]",
				"label" => 'Organization Modules',
				"rules" => 'required'
			],
		];
		if (strtolower($this->SxPrevilegeType->where(["id" => $this->input->post('previlege_type')])->row_array()['type_name']) != strtolower('user')) {
			$config[] = [
				"field" => "party_type",
				"label" => 'Party Type',
				"rules" => 'required'
			];
		}
		$this->form_validation->set_rules($config);
		if ($this->form_validation->run() == FALSE) {
			return $this->settemplate->master('privileges/edit', compact('organizations', 'party_types', 'privilege_types', 'previllege', 'tab', 'selected_modules'));
		}
		$unique = $this->previlleges
			->whereNotIn('id', [$previllege['id']])
			// ->groupStart()
			->where(['previllege_id' => $this->input->post('previllege_id')])
			// ->orGroupStart()
			// ->where(['previllege_name' => $this->input->post('previllege_name')])
			// ->groupEnd()
			// ->groupEnd()
			->row_array();
		if ($unique) {
			$config = [];
			if ($unique['previllege_id'] == $this->input->post('previllege_id')) {
				$config[] = [
					"field" => "previllege_id",
					"label" => 'Privilege Id',
					"rules" => 'required|is_unique[sx_previlleges.previllege_id]'
				];
			}
			/* if ($unique['previllege_name'] == $this->input->post('previllege_name')) {
                $config[] = [
                    "field" => "previllege_name",
                    "label" => 'Privilege Name',
                    "rules" => 'required|is_unique[sx_previlleges.previllege_name]'
                ];
            } */
			$this->form_validation->set_rules($config);
			if ($this->form_validation->run() == FALSE) {
				return $this->settemplate->master('privileges/edit', compact('organizations', 'party_types', 'privilege_types', 'previllege', 'tab', 'selected_modules'));
			}
		}
		$insert = ["previllege_id" => $this->input->post('previllege_id'), "previllege_name" => $this->input->post('previllege_name'), "previllege_description" => $this->input->post('previllege_description') ?: $this->input->post('previllege_name'), "previlege_type" => $this->input->post('previlege_type'), "party_type" => $this->input->post('party_type'), "business_entity" => $this->input->post('business_entity'), "org_id" => $this->input->post('org_id'), "structure_id" => $this->input->post('structure_id')];
		if ($this->previlleges->update($previllege['id'], $insert)) {
			// $this->privilege_modules->where(['previllege_id' => $previllege['id']])->set(['status' => 0])->update();
			$this->privilege_modules->setAllStatusInactive($previllege['id']);
			foreach ($this->input->post('org_modules') as $module) {
				if ($exitmodule = $this->privilege_modules->where(['previllege_id' => $previllege['id'], 'module_id' => $module])->row_array()) {
					$data = ['status' => 1];
					$this->privilege_modules->update($exitmodule['id'], $data);
				} else {
					$this->privilege_modules->insert(['previllege_id' => $previllege['id'], 'module_id' => $module]);
				}
			}
			$this->session->set_flashdata('success', 'Previllege Updated Successfully');
			return  redirect('privileges/edit/' . $insert['previllege_id'] . '?tab=access');
		}
		$this->session->set_flashdata('error', 'Something went wrong. Please try again.');
		return  redirect('privileges');
	}

	public function getModules($id)
	{
		$privilege = $this->previlleges->where(['id' => $id])->row_array();
		if (!$privilege) {
			return $this->output->set_status_header(404)->set_content_type('application/json', 'utf-8')->set_output(json_encode(["message" => 'Privilege Not Found']));
		}
		$modules = $this->organization_modules->join('sx_modules', 'sx_modules.id = sx_organization_modules.module_id')->where(["sx_organization_modules.org_id" => $privilege['org_id'], "sx_modules.status" => 1])->select('*,sx_organization_modules.id as id')->getActiveRecords();
		if (count($modules) > 0) {
			$privilege_modules = $this->privilege_modules->where(['previllege_id' => $privilege['id'], "status" => 1])->getActiveRecords();
			$checkedmodules = array();
			foreach ($privilege_modules as $module) {
				$checkedmodules[] = $module['module_id'];
			}
			$edit = true;
			return $this->load->view('organization/modules', compact('modules', 'edit', 'checkedmodules'));
		}
	}

	public function getFeatures()
	{
		$where = [
			"module_id" => (int)$this->input->post('module_id'),
			"previllege_id" => (int)$this->input->post('previllege_id'),
			"previllege_module_id" => (int)$this->input->post('previllege_module_id'),
		];
		$get_current_org_id = $this->previlleges->where(['id' => $where['previllege_id']])->row_array()['org_id'];
		$result = $this->module_features
			->join('sx_previlege_access_rules as sxpar', 'sxpar.feature_id = sx_module_features.id', 'left')
			->join('sx_previllege_modules as sxpm', 'sxpm.id = sxpar.previllege_module_id', 'left')
			->where(['sx_module_features.module_id' => $where['module_id'], "sxpm.id" => $where['previllege_module_id'], 'sxpm.previllege_id' => $where['previllege_id'], 'sxpar.status' => 1, 'sxpm.status' => 1])
			->select('sx_module_features.*, sxpar.id as sxpar_id, sx_module_features.id as feature_id')
			->getActiveRecords();
		$current_table_obj = $this->SxOrgModuleFeatures;
		$current_table = $current_table_obj->table;
		$join_table = $this->module_features->table;
		if (count($result) == 0) {
			$result = $this->SxOrgModuleFeatures->join("$join_table as mf", "mf.id = $current_table.feature_id")->where(["$current_table.module_id" => $where['module_id'],  "$current_table.org_id" => $get_current_org_id])->select("*, mf.id as feature_id")->getActiveRecords();
			// $result = $this->module_features
			//     ->where(['sx_module_features.module_id' => $where['module_id']])
			//     ->select('*, sx_module_features.id as feature_id')
			//     ->getActiveRecords();
		} else {
			$feature_ids = [];
			foreach ($result as $data) {
				$feature_ids[] = $data['feature_id'];
			}
			// $newfeatures = $this->module_features->join('sx_previlege_access_rules as sxpar', 'sxpar.feature_id = sx_module_features.id', 'left')->where(['sx_module_features.module_id' => $where['module_id'], 'sxpar.status' => 1])->whereNotIn('sx_module_features.id', $feature_ids)->select('*, sxpar.id as sxpar_id, sx_module_features.id as feature_id')->getActiveRecords();
			$newfeatures = $this->SxOrgModuleFeatures->join("$join_table as mf", "mf.id = $current_table.feature_id")->where(["$current_table.module_id" => $where['module_id'], "$current_table.org_id" => $get_current_org_id])->whereNotIn('mf.id', $feature_ids)->select("*, mf.id as feature_id")->getActiveRecords();
			// $newfeatures = $this->module_features
			//     ->where(['sx_module_features.module_id' => $where['module_id']])
			//     ->whereNotIn('id', $feature_ids)
			//     ->select('*, sx_module_features.id as feature_id')
			//     ->getActiveRecords();
			$result = array_merge($result, $newfeatures);
		}
		return $this->load->view('privileges/features', compact('result', 'where'));
	}

	public function storeFeatures()
	{
		$common_insert = [
			"previllege_module_id" => $this->input->post('previllege_module_id'),
			"previllege_id" => $this->input->post('previllege_id'),
		];
		$insertBatch = [];
		$updateBatch = [];
		$featuresList = $this->input->post('feature_id');
		if(!empty($featuresList)){
			foreach ($featuresList as $key =>  $feature) {
				$common_array = [
					"feature_id" => $feature,
					"previllege_id" => $common_insert['previllege_id'],
					"previllege_module_id" => $common_insert['previllege_module_id'],
					"add_access" => @$this->input->post('add_access')[$feature] == 'on' ? 1 : 0,
					"modify_access" => @$this->input->post('modify_access')[$feature] == 'on' ? 1 : 0,
					"delete_access" => @$this->input->post('delete_access')[$feature] == 'on' ? 1 : 0,
					"view_access" => @$this->input->post('view_access')[$feature] == 'on' ? 1 : 0,
				];
				if ($id = @$this->input->post('access_id')[$key]) {
					$common_array['id'] = $id;
					$updateBatch[] = $common_array;
				} else {
					$insertBatch[] = $common_array;
				}
			}
		}
		if (!empty($insertBatch)) {
			$this->privilege_access_rules->insertBatch($insertBatch);
		}
		$allIds = $allIdsString = [];
		if (!empty($updateBatch)) {
			foreach ($updateBatch as $item) {
				if (isset($item['id'])) {
					$allIds[] = $item['id'];
				}
			}
			$allIdsString = implode(',', $allIds);
			$updateDataSet = array(
				'add_access' => 0,
				'modify_access' => 0,
				'delete_access' => 0,
				'view_access' => 0,
				'status' => 0,
				'deleted_at' => date('Y-m-d H:i:s')
			);
			$this->db->where_in('id', $allIds);
			$oldIds = $this->db->update("sx_previlege_access_rules",$updateDataSet);
			if($oldIds){
				$this->privilege_access_rules->updateBatch($updateBatch, 'id');
			}
		}
		/*
		 * safe query*/
		/*$this->db->query("SELECT * FROM sx_previlege_access_rules WHERE previllege_id  = 86  AND
		previllege_module_id = 511 AND status = 1 AND add_access = 1 AND modify_access = 1 AND  delete_access = 1 AND view_access = 1
		");*/
		$privilege = $this->previlleges->where(['id' => $common_insert['previllege_id']])->row_array();
		$this->session->set_flashdata('success', 'Successfuly access granted');
		return  redirect('privileges/edit/' . $privilege['previllege_id'] . '?tab=access');
	}

	public function getPrivileges()
	{
		$config = [
			[
				"field" => "entity_id",
				"label" => 'Entity',
				"rules" => 'required',
			],
			[
				"field" => "org_id",
				"label" => 'Organization',
				"rules" => 'required',
			],
			[
				"field" => "structure_id",
				"label" => 'Structure',
				"rules" => 'required',
			]
		];
		$this->form_validation->set_rules($config);
		if ($this->form_validation->run() == FALSE) {
			$errors = $this->form_validation->error_array();
			return $this->output->set_status_header(406)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['error' => $errors]));
		} else {
			$data = $this->previlleges->where(["org_id" => $this->input->post('org_id'), "business_entity" => $this->input->post('entity_id'), "structure_id" => $this->input->post('structure_id')])->getActiveRecords();
			return $this->output->set_status_header(200)->set_content_type('application/json', 'utf-8')->set_output(json_encode(['data' => $data]));
		}
	}

    /**
     * Super user
     */

    public function user()
    {
        if (!check_super_user()) {
            return show_403('master');
        }
        $org_id = $this->session->userdata('org_id');
        $previlleges = $this->db->query('SELECT p.previllege_id, p.previllege_name, pt.type_name as previllege_type, par.type_name as party_type FROM sx_previlleges p INNER JOIN sx_previlege_types pt ON p.previlege_type = pt.id LEFT JOIN sx_party_types par ON p.party_type = par.id WHERE p.status= 1 AND p.org_id = '.$org_id)->result_array();

        return $this->settemplate->master('privileges/user', compact('previlleges'));
    }
}
