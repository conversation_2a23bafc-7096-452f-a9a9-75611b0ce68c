<?php

namespace Database\Factories;

use App\Models\Shipment;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Shipment>
 */
class ShipmentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Shipment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => $this->faker->numberBetween(1, 100),
            'stime' => $this->faker->dateTimeBetween('-1 month', '+1 month'),
            'etime' => $this->faker->dateTimeBetween('+1 hour', '+2 months'),
            'splace' => $this->faker->city(),
            'slat' => $this->faker->latitude(),
            'slng' => $this->faker->longitude(),
            'eplace' => $this->faker->city(),
            'elat' => $this->faker->latitude(),
            'elng' => $this->faker->longitude(),
            'scity' => $this->faker->city(),
            'dcity' => $this->faker->city(),
            'zone_id' => $this->faker->numberBetween(1, 10),
            'empshift_start' => $this->faker->dateTimeBetween('-1 day', '+1 day'),
            'empshift_end' => $this->faker->dateTimeBetween('+1 hour', '+2 days'),
            'trip_type' => $this->faker->randomElement(['pickup', 'delivery', 'round_trip']),
            'startdate' => $this->faker->date(),
            'enddate' => $this->faker->date(),
            'shipment_name' => $this->faker->words(3, true),
            'shipmentid' => $this->faker->unique()->numerify('SH####'),
            'shipment_id' => $this->faker->unique()->numerify('SHIP####'),
            'customer_id' => $this->faker->numberBetween(1, 50),
            'transport_mode' => $this->faker->randomElement(['road', 'rail', 'air', 'sea']),
            'vendor_id' => $this->faker->numberBetween(1, 20),
            'carrier_type' => $this->faker->randomElement(['truck', 'van', 'container']),
            'txnid' => $this->faker->unique()->numerify('TXN####'),
            'weight' => $this->faker->randomFloat(2, 1, 1000),
            'volume' => $this->faker->randomFloat(2, 1, 100),
            'units' => $this->faker->numberBetween(1, 50),
            'domainname' => $this->faker->domainName(),
            'schedule_date' => $this->faker->date(),
            'vehicle_type' => $this->faker->randomElement(['truck', 'van', 'motorcycle']),
            'org_id' => $this->faker->numberBetween(1, 5),
            'be_value' => $this->faker->numberBetween(0, 10),
            'dept_id' => $this->faker->numerify('DEPT###'),
            'border_type' => $this->faker->randomElement(['domestic', 'international']),
            'carrier_instructions' => $this->faker->sentence(),
            'status' => $this->faker->numberBetween(0, 2),
            'shift_leg_id' => $this->faker->numberBetween(1, 100),
            'origin_id' => $this->faker->numberBetween(1, 100),
            'destination_id' => $this->faker->numberBetween(1, 100),
            'interchange_control_reference' => $this->faker->numerify('ICR####'),
            'weight_capacity' => $this->faker->randomFloat(2, 100, 5000),
            'volume_capacity' => $this->faker->randomFloat(2, 10, 500),
            'additional_conditions' => $this->faker->sentence(),
            'temperature_regime' => $this->faker->randomElement(['ambient', 'refrigerated', 'frozen']),
            'time_for_loading_penality_rate' => $this->faker->randomFloat(2, 0, 100),
            'is_carrier_notified' => $this->faker->boolean(),
            'aborted' => $this->faker->boolean(10), // 10% chance of being aborted
            'routetemplate_id' => $this->faker->numberBetween(1, 50),
            'template_leg_id' => $this->faker->numerify('LEG###'),
            'sgeolocation' => $this->faker->latitude() . ',' . $this->faker->longitude(),
            'egeolocation' => $this->faker->latitude() . ',' . $this->faker->longitude(),
            'shipment_order' => $this->faker->numberBetween(1, 100),
            'no_of_vehicles' => $this->faker->numberBetween(1, 5),
            'dimension_type' => $this->faker->randomElement(['standard', 'oversized', 'hazardous']),
            'truck_start_time' => $this->faker->dateTimeBetween('-1 day', '+1 day'),
            'max_distance' => $this->faker->randomFloat(2, 10, 1000),
            'capacity' => $this->faker->randomFloat(2, 100, 2000),
            'truck_starttime' => $this->faker->dateTimeBetween('-1 day', '+1 day'),
            'cost_per_hour' => $this->faker->randomFloat(2, 10, 200),
            'cost_per_kilometer' => $this->faker->randomFloat(2, 1, 50),
            'traffic' => $this->faker->randomElement(['low', 'medium', 'high']),
        ];
    }

    /**
     * Indicate that the shipment is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 1,
        ]);
    }

    /**
     * Indicate that the shipment is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 0,
        ]);
    }

    /**
     * Indicate that the shipment is aborted.
     */
    public function aborted(): static
    {
        return $this->state(fn (array $attributes) => [
            'aborted' => true,
            'status' => 2,
        ]);
    }
}
