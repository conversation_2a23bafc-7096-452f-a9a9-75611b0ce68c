<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'employee_id' => fake()->unique()->numerify('EMP####'),
            'employee_name' => fake()->name(),
            'username' => fake()->unique()->safeEmail(),
            'password' => static::$password ??= Hash::make('password'),
            'effective_fromdate' => now()->format('Y-m-d'),
            'effective_enddate' => now()->addYear()->format('Y-m-d'),
            'contact_num' => fake()->phoneNumber(),
            'theme_id' => 1,
            'currency' => 'USD',
            'date_zone' => null,
            'number_format' => '1,22',
            'created_by' => 0,
            'updated_by' => 0,
            'deleted_at' => null,
            'languages' => '["1"]',
            'date_format' => 'd/m/Y',
            'default_org_id' => fake()->numberBetween(1, 5),
            'country_code' => null,
            'default_currency' => null,
            'lat' => null,
            'lng' => null,
            'status' => 1,
            'emailid' => fake()->unique()->safeEmail(),
            'refresh_token' => null,
            'geolocation' => null,
            'logo' => null,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }
}
