<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('country_master', function (Blueprint $table) {
            $table->increments('id');
            $table->string('country_name', 100);
            $table->string('country_code', 20)->nullable();
            $table->integer('user_id')->nullable()->default(0);          // add FK later if you like
            $table->string('currency', 30)->nullable();
            $table->string('cntry_timezone', 30)->nullable();
            $table->string('cntry_hrs', 30)->nullable();
            $table->string('phone_code', 10)->nullable();
            $table->string('lang_code', 10)->nullable();

            // Targets / metrics
            $table->integer('annual_shipment_target')->default(5000);
            $table->integer('annual_gp_target')->default(0);
            $table->decimal('shipment_gp', 10, 2)->default(0.00);

            // Flags & status
            $table->char('is_panic_alert_required', 1)->default('0');
            $table->smallInteger('status')->default(1);
            $table->timestamps();

             // Indexes
            $table->index(
                ['country_name', 'country_code', 'cntry_hrs', 'status'],
                'idx_country_key_combo'
            );
            $table->index('is_panic_alert_required');
            $table->index('phone_code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('country_master');
    }
};