<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sx_organization', function (Blueprint $table) {
            $table->increments('id');
            $table->string('org_id',60); // varchar(30) NOT NULL
            $table->string('org_name', 60); // varchar(60) NOT NULL
            $table->string('org_description', 500)->nullable(); // varchar(500) DEFAULT NULL
            $table->integer('total_users')->default(0); // int(11) NOT NULL DEFAULT 0
            $table->integer('concurrent_users')->default(0); // int(11) NOT NULL DEFAULT 0
            $table->integer('buffer_users')->default(0); // int(11) NOT NULL DEFAULT 0
            $table->timestamp('effective_from')->nullable(); // timestamp NULL DEFAULT NULL
            $table->timestamp('effective_to')->nullable(); // timestamp NULL DEFAULT NULL
            $table->integer('buffer_days')->default(0); // int(11) NOT NULL DEFAULT 0
            $table->string('alert_contact_admin', 100)->nullable(); // varchar(100) DEFAULT NULL
            $table->string('alert_contact_legal', 100)->nullable(); // varchar(100) DEFAULT NULL
            $table->string('alter_contact_sales', 100)->nullable(); // varchar(100) DEFAULT NULL
            $table->string('auth_name', 60)->nullable(); // varchar(60) DEFAULT NULL
            $table->string('contact_num', 30)->nullable(); // varchar(30) DEFAULT NULL
            $table->string('email_id', 60)->nullable(); // varchar(60) DEFAULT NULL
            $table->string('address', 500)->nullable(); // varchar(500) DEFAULT NULL
            $table->timestamp('deleted_at')->nullable(); // timestamp NULL DEFAULT NULL (soft deletes)
            $table->text('logo')->nullable(); // text DEFAULT NULL
            $table->tinyInteger('status')->default(1); // smallint(6) DEFAULT 1
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sx_organization');
    }
};
