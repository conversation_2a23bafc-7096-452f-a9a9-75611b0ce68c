<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('unique_order_ids', function (Blueprint $table) {
            $table->increments('id');
            $table->string('order_id', 50)->unique();
            // No timestamps as per original schema
        });

        // Set AUTO_INCREMENT starting value
        // DB::statement('ALTER TABLE unique_order_ids ALTER COLUMN id SET START WITH 237288');
    }

    public function down(): void
    {
        Schema::dropIfExists('unique_order_ids');
    }
};