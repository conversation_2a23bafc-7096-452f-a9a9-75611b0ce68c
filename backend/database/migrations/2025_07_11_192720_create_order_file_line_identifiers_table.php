<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('orders_filelineidentifier', function (Blueprint $table) {
            $table->increments('id');
            $table->string('source_city', 50)->nullable();
            $table->string('source_suburb', 50)->nullable();
            $table->string('source_country', 50)->nullable();
            $table->string('destination_city', 50)->nullable();
            $table->string('destination_suburb', 50)->nullable();
            $table->string('destination_country', 50)->nullable();
            $table->string('ref_value', 22)->nullable();
            $table->integer('user_id')->nullable()->default(0);
            $table->tinyInteger('status')->default(1);
            $table->integer('org_id')->nullable()->default(0); 
            $table->integer('be_value')->nullable()->default(0);
            $table->timestamps();

            $table->index(['source_city', 'source_suburb', 'source_country', 'destination_city', 'destination_suburb', 'destination_country', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('orders_filelineidentifier');
    }
};
