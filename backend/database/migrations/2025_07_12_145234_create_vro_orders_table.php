<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vro_orders', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->string('order_ids', 150);
            $table->string('dimension_type', 30)->nullable();
            $table->integer('no_of_vehicles')->default(0);
            $table->integer('vehicle_type')->default(0);
            $table->integer('carrier_id')->default(0);
            $table->smallInteger('traffic_checkbox')->default(0);
            $table->smallInteger('status')->default(1);
            $table->text('carrier_instructions')->nullable();
            $table->string('weight_capacity', 60)->nullable();
            $table->string('volume_capacity', 60)->nullable();
            $table->text('additional_conditions')->nullable();
            $table->string('temperature_regime', 60)->nullable();
            $table->string('time_for_loading_penality_rate', 60)->nullable();
            $table->time('truck_starttime')->nullable();
            $table->decimal('cost_per_hour', 10, 2)->default(0.00);
            $table->decimal('cost_per_kilometer', 10, 2)->default(0.00);
            $table->smallInteger('route_data')->default(0);
            $table->string('max_distance', 30)->nullable();
            $table->decimal('max_truck_distance', 10, 2)->default(0.00);
            $table->integer('org_id')->default(0);
            $table->integer('be_value')->default(0);
            $table->integer('user_id')->default(0);
            $table->timestamps();
            $table->index(['org_id', 'be_value', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('vro_orders');
    }
};
