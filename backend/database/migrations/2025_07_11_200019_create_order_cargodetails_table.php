<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('order_cargodetails', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('order_id');
            $table->integer('org_id')->default(0);
            $table->integer('be_value')->default(0);
            $table->integer('user_id');
            $table->integer('cargo_id')->default(0);
            $table->string('handling_unit', 60)->nullable();
            $table->decimal('length', 10, 2)->default(0.00);
            $table->decimal('width', 10, 2)->default(0.00);
            $table->decimal('height', 10, 2)->default(0.00);
            $table->decimal('weight', 12, 3)->default(0.000);
            $table->decimal('second_weight', 10, 3)->nullable();
            $table->decimal('volumetric_weight', 12, 3)->default(0.000);
            $table->string('volweight_uom', 20)->nullable();
            $table->decimal('ldm', 10, 3)->default(0.000);
            $table->decimal('volume', 10, 3)->default(0.000);
            $table->decimal('second_volume', 10, 3)->nullable();
            $table->decimal('quantity', 10, 2)->default(0.00);
            $table->decimal('scanned_quantity', 10, 2)->nullable();
            $table->string('quantity_type', 100)->nullable();
            $table->string('cargo_content', 100)->nullable();
            $table->string('buyer_part', 30)->nullable();
            $table->string('linebp_num1', 50)->nullable();
            $table->string('linecontainer_no', 50)->nullable();
            $table->integer('stop_detail_id')->nullable();
            $table->text('label_number')->nullable();
            $table->string('qr_code', 66)->nullable();
            $table->string('pallet_name', 80)->nullable();
            $table->boolean('pallet_close')->default(true);
            $table->tinyInteger('status')->default(1);
            $table->string('reference_order_num', 50)->nullable();
            $table->string('load_plan_connote', 30)->nullable();
            $table->string('marks_numbers', 50)->nullable();
            $table->string('dimensions', 50)->nullable();
            $table->string('material', 50)->nullable();
            $table->string('information', 50)->nullable();
            $table->string('abnormal_load', 50)->nullable();
            $table->timestamps();
            $table->index(['cargo_id', 'status']);
            $table->index(['handling_unit']);
            $table->index(['weight', 'volume', 'quantity_type']);
            $table->index(['stop_detail_id']);
            $table->index(['quantity']);
            $table->index(['pallet_name', 'pallet_close']);
            $table->index(['cargo_content']);
            $table->index(['order_id', 'cargo_id', 'handling_unit', 'status']);
            $table->index(['pallet_close']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('order_cargodetails');
    }
};
