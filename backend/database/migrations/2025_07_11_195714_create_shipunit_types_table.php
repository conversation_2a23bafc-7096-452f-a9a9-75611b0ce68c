<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('shipunit_types', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('order_id');
            $table->integer('be_value');
            $table->integer('user_id');
            $table->string('unit_name', 60);
            $table->string('unit_code', 60);
            $table->text('description')->nullable();
            $table->integer('org_id')->nullable()->default(0);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
            $table->index(['unit_name']);
            $table->index(['user_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('shipunit_types');
    }
};
