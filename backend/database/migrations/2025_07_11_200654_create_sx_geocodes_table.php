<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('sx_geocodes', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('org_id')->default(1);
            $table->integer('be_value')->default(1);
            $table->integer('user_id');
            $table->string('postal_code', 10)->nullable();
            $table->string('district', 150)->nullable();
            $table->string('city', 150)->nullable();
            $table->string('province', 150)->nullable();
            $table->string('country', 150)->nullable();
            $table->string('sub_region', 150)->nullable();
            $table->string('region', 150)->nullable();
            $table->string('zone_1', 150)->nullable();
            $table->string('zone_2', 150)->nullable();
            $table->string('zone_3', 150)->nullable();
            $table->string('zone_4', 150)->nullable();
            $table->string('zone_5', 150)->nullable();
            $table->string('zone_6', 150)->nullable();
            $table->string('zone_7', 150)->nullable();
            $table->string('zone_8', 150)->nullable();
            $table->string('zone_9', 150)->nullable();
            $table->string('zone_10', 150)->nullable();
            $table->smallInteger('status')->default(1);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('sx_geocodes');
    }
};