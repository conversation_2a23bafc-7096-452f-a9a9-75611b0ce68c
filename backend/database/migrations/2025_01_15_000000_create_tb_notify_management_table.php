<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notify_management', function (Blueprint $table) {
            $table->increments('id');
            $table->string('notification_id', 50)->unique();
            $table->integer('user_id')->default(0);
            $table->integer('org_id')->default(0);
            $table->integer('be_value')->default(0);
            $table->string('product', 100)->nullable();
            $table->string('inco_term', 50)->nullable();
            $table->string('service', 100)->nullable();
            $table->string('order_type', 50)->nullable();
            $table->string('from_country', 50)->nullable();
            $table->string('to_country', 50)->nullable();
            $table->integer('party_id')->default(0);
            $table->integer('partycontact_id')->default(0);
            $table->boolean('all_note')->default(false);
            $table->integer('customer_id')->default(0);
            $table->string('party_type', 50)->nullable();
            $table->integer('party_type_id')->default(0);
            $table->boolean('booking_create')->default(false);
            $table->boolean('booking_edit')->default(false);
            $table->boolean('booking_delete')->default(false);
            $table->boolean('trip_create')->default(false);
            $table->boolean('trip_edit')->default(false);
            $table->boolean('trip_delete')->default(false);
            $table->boolean('driver_accept')->default(false);
            $table->boolean('route_deviate')->default(false);
            $table->boolean('speed')->default(false);
            $table->boolean('temperature')->default(false);
            $table->boolean('pickup_note')->default(false);
            $table->boolean('delivery_note')->default(false);
            $table->boolean('pod_note')->default(false);
            $table->boolean('sms_note')->default(false);
            $table->boolean('email_note')->default(false);
            $table->boolean('whatsapp_note')->default(false);
            $table->integer('createdby')->default(0);
            $table->integer('updatedby')->default(0);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();

            // Indexes
            $table->index(['user_id', 'org_id', 'be_value']);
            $table->index(['notification_id']);
            $table->index(['customer_id']);
            $table->index(['party_id']);
            $table->index(['status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notify_management');
    }
}; 