<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vendor_profile_list', function (Blueprint $table) {
            $table->increments('id');
            $table->integer('vp_id')->unsigned();
            $table->string('profile_id', 30)->nullable();
            $table->integer('party_id')->unsigned();
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
            $table->softDeletes();

            // $table->foreign('vp_id')->references('id')->on('vendor_profile')->onDelete('cascade');
            // $table->foreign('party_id')->references('id')->on('sx_party_members')->onDelete('cascade');
            $table->index(['vp_id', 'party_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('vendor_profile_list');
    }
}; 