<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cost_center', function (Blueprint $table) {
            $table->increments('id');
            $table->string('type_name', 60);
            $table->string('description', 100)->nullable();
            $table->integer('customer_id')->default(0);
            $table->string('ordtype_code', 10)->nullable();
            $table->tinyInteger('status')->default(1);
            $table->integer('order_id')->default(0);
            $table->integer('user_id')->default(0);
            $table->integer('org_id')->default(0);
            $table->integer('be_value')->default(0);
            $table->timestamps(); // Adds created_at and updated_at
            $table->index(['type_name', 'org_id', 'customer_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cost_center');
    }
};
