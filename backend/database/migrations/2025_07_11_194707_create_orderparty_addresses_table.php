<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('orderparty_addresses', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('order_id');
            $table->integer('be_value');
            $table->integer('user_id');
            $table->string('party_master_id', 22)->nullable();
            $table->string('location_id', 100)->nullable();
            $table->string('street', 700)->nullable();
            $table->string('state', 100)->nullable();
            $table->string('address', 600)->nullable();
            $table->string('pincode', 30)->nullable();
            $table->string('country', 30)->nullable();
            $table->integer('org_id')->nullable()->default(0);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();

            $table->index(['order_id', 'party_master_id', 'user_id', 'status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('orderparty_addresses');
    }
};
