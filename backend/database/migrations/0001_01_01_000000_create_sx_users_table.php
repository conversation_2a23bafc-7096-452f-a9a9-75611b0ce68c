<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sx_users', function (Blueprint $table) {
            $table->increments('id');
            $table->string('employee_id', 60)->nullable();
            $table->string('employee_name', 100)->nullable();
            $table->string('username', 60);
            $table->string('password', 255);
            $table->date('effective_fromdate')->nullable();
            $table->date('effective_enddate')->nullable();
            $table->string('contact_num', 60)->nullable();
            $table->integer('theme_id')->nullable();
            $table->string('currency', 60)->nullable();
            $table->string('date_zone', 60)->nullable();
            $table->string('number_format', 100)->nullable();
            $table->integer('created_by')->default(0);
            $table->integer('updated_by')->default(0);
            $table->timestamp('deleted_at')->nullable()->default(null);
            $table->string('languages', 100)->nullable();
            $table->string('date_format', 100)->nullable();
            $table->integer('default_org_id')->nullable();
            $table->string('country_code', 100)->nullable();
            $table->string('default_currency', 100)->nullable();
            $table->string('lat', 100)->nullable();
            $table->string('lng', 100)->nullable();
            $table->smallInteger('status')->default(1);
            $table->string('emailid', 60)->nullable();
            $table->string('refresh_token', 500)->nullable();
            $table->string('geolocation', 200)->nullable();
            $table->text('logo')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sx_users');
    }
};
