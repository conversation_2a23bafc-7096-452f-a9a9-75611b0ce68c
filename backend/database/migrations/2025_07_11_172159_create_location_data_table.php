<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('location_data', function (Blueprint $table) {
            $table->increments('id');
            $table->string('location_name', 255)->nullable();
            $table->text('name1');
            $table->text('name2');
            $table->string('country', 64)->nullable();
            $table->string('lat', 60)->nullable();
            $table->string('lng', 60)->nullable();
            $table->string('source', 255)->nullable();
            $table->index(['lat', 'lng'], 'idx_location');
            $table->index('name1', 'name1');
            $table->index('source', 'source');
        });

        // Set AUTO_INCREMENT starting value (optional, for PostgreSQL use sequence)
        // DB::statement('ALTER SEQUENCE location_data_id_seq RESTART WITH 1510004');
    }

    public function down(): void
    {
        Schema::dropIfExists('location_data');
    }
};