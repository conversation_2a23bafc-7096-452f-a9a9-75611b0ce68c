<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('order_parties', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->integer('order_id');
            $table->integer('org_id')->default(1);
            $table->integer('be_value')->default(1);
            $table->integer('user_id');
            $table->string('order_number', 30)->nullable();
            $table->integer('party_id');
            $table->integer('party_type')->default(1);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
            $table->index(['order_id', 'party_id', 'party_type', 'status']);
            $table->index(['party_type']);
            $table->index(['party_id']);
            $table->index(['status']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('order_parties');
    }
};
