<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class HandlingUnitsSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('handling_units')->upsert(
            [
                [
                    'id' => 1,
                    'unit_name' => 'Pallets',
                    'unit_code' => '0',
                    'description' => 'Pallets',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'unit_name' => 'Boxes',
                    'unit_code' => '0',
                    'description' => 'Boxes',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 3,
                    'unit_name' => 'TAPES',
                    'unit_code' => '0',
                    'description' => 'TAPES',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 4,
                    'unit_name' => 'BAGS',
                    'unit_code' => '0',
                    'description' => 'BAGS',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['unit_name', 'unit_code', 'description', 'org_id', 'be_value', 'user_id', 'status', 'created_at', 'updated_at']
        );
    }
}