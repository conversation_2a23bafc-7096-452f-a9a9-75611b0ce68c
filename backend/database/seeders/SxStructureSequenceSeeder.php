<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxStructureSequenceSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $structureSequences = [
            [
                'id' => 1,
                'structure_id' => 1,
                'parent_business_entity' => 1,
                'child_business_entity' => 2,
                'created_by' => 0,
                'updated_by' => 0,
                'org_id' => 1,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 2,
                'structure_id' => 1,
                'parent_business_entity' => 2,
                'child_business_entity' => 3,
                'created_by' => 0,
                'updated_by' => 0,
                'org_id' => 1,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_structure_sequence')->insert($structureSequences);
    }
}