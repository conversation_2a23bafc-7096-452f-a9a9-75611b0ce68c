<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OrderTypesSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('order_types')->upsert(
            [
                [
                    'id' => 1,
                    'type_name' => 'Normal',
                    'description' => 'Normal',
                    'org_id' => 1,
                    'be_value' => null,
                    'user_id' => 0,
                    'customer_id' => 0,
                    'ordtype_code' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'type_name' => 'Urgent',
                    'description' => 'Urgent',
                    'org_id' => 1,
                    'be_value' => null,
                    'user_id' => 0,
                    'customer_id' => 0,
                    'ordtype_code' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['type_name', 'description', 'org_id', 'be_value', 'user_id', 'customer_id', 'ordtype_code', 'status', 'created_at', 'updated_at']
        );
    }
}