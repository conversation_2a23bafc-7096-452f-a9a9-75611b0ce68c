<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxPrevillegeModulesSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $previllegeModules = [
            [
                'id' => 4,
                'previllege_id' => 1,
                'module_id' => 9,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 5,
                'previllege_id' => 1,
                'module_id' => 8,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 6,
                'previllege_id' => 1,
                'module_id' => 7,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 7,
                'previllege_id' => 1,
                'module_id' => 6,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 8,
                'previllege_id' => 1,
                'module_id' => 5,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 9,
                'previllege_id' => 1,
                'module_id' => 4,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 1,
                'previllege_id' => 1,
                'module_id' => 3,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 2,
                'previllege_id' => 1,
                'module_id' => 2,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 3,
                'previllege_id' => 1,
                'module_id' => 1,
                'deleted_at' => null,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_previllege_modules')->insert($previllegeModules);
    }
}