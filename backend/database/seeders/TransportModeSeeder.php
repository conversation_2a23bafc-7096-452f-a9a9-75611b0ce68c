<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TransportModeSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('transportmode')->upsert(
            [
                [
                    'id' => 1,
                    'code' => 'AIR-TRANS',
                    'name' => 'AIR TRANSPORT',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'code' => 'SEA-FCL',
                    'name' => 'SEA-FCL',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 3,
                    'code' => 'SEA-LCL',
                    'name' => 'SEA-LCL',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 4,
                    'code' => 'AIR-FCL',
                    'name' => 'AIR-FCL',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 5,
                    'code' => 'AIR-LCL',
                    'name' => 'AIR-LCL',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 6,
                    'code' => 'COURIER',
                    'name' => 'COURIER',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 7,
                    'code' => 'FTL',
                    'name' => 'Full Truck Load',
                    'user_id' => null,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['code', 'name', 'user_id', 'org_id', 'be_value', 'status', 'created_at', 'updated_at']
        );
    }
}