<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxPrevillegesSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $previlleges = [
            [
                'id' => 1,
                'previllege_id' => 'RAMMG',
                'previllege_name' => 'Manager',
                'previllege_description' => 'company level manager role',
                'deleted_at' => null,
                'previlege_type' => 1,
                'party_type' => null,
                'business_entity' => 1,
                'org_id' => 1,
                'structure_id' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_previlleges')->insert($previlleges);
    }
}