<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxOrganizationModulesSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('sx_organization_modules')->upsert(
            [
                [
                    'id'            => 1,
                    'org_id'        => 1,
                    'module_id'     => 9,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 2,
                    'org_id'        => 1,
                    'module_id'     => 8,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 3,
                    'org_id'        => 1,
                    'module_id'     => 7,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 4,
                    'org_id'        => 1,
                    'module_id'     => 6,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 5,
                    'org_id'        => 1,
                    'module_id'     => 5,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 6,
                    'org_id'        => 1,
                    'module_id'     => 4,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 7,
                    'org_id'        => 1,
                    'module_id'     => 3,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 8,
                    'org_id'        => 1,
                    'module_id'     => 2,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
                [
                    'id'            => 9,
                    'org_id'        => 1,
                    'module_id'     => 1,
                    'created_by'    => 0,
                    'updated_by'    => 0,
                    'deleted_at'    => null,
                    'module_status' => 1,
                    'status'        => 1,
                    'created_at'    => null,
                    'updated_at'    => null,
                ],
            ],
            ['id'],  // unique key for UPSERT
            ['org_id','module_id','created_by','updated_by','deleted_at',
             'module_status','status','created_at','updated_at']
        );
    }
}
