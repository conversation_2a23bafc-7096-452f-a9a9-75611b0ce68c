<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxPartyTypesSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $partyTypes = [
            [
                'id' => 1,
                'description' => 'Customer',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Customer',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 2,
                'description' => 'Carrier',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Carrier',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 3,
                'description' => 'Shipper',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Shipper',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 4,
                'description' => 'Consignee',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Consignee',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 5,
                'description' => 'Consignor',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Consignor',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 6,
                'description' => 'Customs',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Customs',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 7,
                'description' => 'Freight Payer',
                'status' => 1,
                'deleted_at' => null,
                'type_name' => 'Freight Payer',
                'org_id' => 0,
                'be_value' => 0,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_party_types')->insert($partyTypes);
    }
}