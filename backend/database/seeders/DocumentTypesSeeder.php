<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class DocumentTypesSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('document_types')->upsert(
            [
                [
                    'id' => 1,
                    'document_id' => 1,
                    'type_name' => 'Document',
                    'org_id' => 1,
                    'be_value' => null,
                    'country_code' => null,
                    'user_id' => 0,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'document_id' => 2,
                    'type_name' => 'Signature',
                    'org_id' => 1,
                    'be_value' => null,
                    'country_code' => null,
                    'user_id' => 0,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['document_id', 'type_name', 'org_id', 'be_value', 'country_code', 'user_id', 'status', 'created_at', 'updated_at']
        );
    }
}
