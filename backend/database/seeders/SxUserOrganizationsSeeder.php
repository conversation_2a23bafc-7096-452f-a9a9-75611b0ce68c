<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxUserOrganizationsSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $userOrganizations = [
            [
                'id' => 1,
                'org_id' => 1,
                'structure_id' => 1,
                'entity_id' => 1,
                'entity_value_id' => 1,
                'roles' => '["1"]',
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'user_id' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_user_organizations')->insert($userOrganizations);
    }
}