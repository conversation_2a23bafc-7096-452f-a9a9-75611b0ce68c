<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxPrevilegeAccessRulesSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $accessRules = [
            [
                'id' => 1,
                'previllege_id' => 1,
                'previllege_module_id' => 3,
                'feature_id' => 74,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 2,
                'previllege_id' => 1,
                'previllege_module_id' => 3,
                'feature_id' => 75,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 3,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 41,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 4,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 42,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 5,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 43,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 6,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 44,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 7,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 38,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 8,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 39,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 9,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 40,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 10,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 45,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 11,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 46,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 12,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 47,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 13,
                'previllege_id' => 1,
                'previllege_module_id' => 1,
                'feature_id' => 48,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 14,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 5,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 15,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 6,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 16,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 7,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 17,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 8,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 18,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 9,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 19,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 10,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
            [
                'id' => 20,
                'previllege_id' => 1,
                'previllege_module_id' => 5,
                'feature_id' => 11,
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'add_access' => 1,
                'modify_access' => 1,
                'delete_access' => 1,
                'view_access' => 1,
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_previlege_access_rules')->insert($accessRules);
    }
}