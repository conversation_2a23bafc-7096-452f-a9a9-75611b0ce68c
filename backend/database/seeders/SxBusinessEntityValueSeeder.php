<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class SxBusinessEntityValueSeeder extends Seeder
{
    public function run(): void
    {
        /**
         * Hard-coded rows lifted directly from the spreadsheet
         * (3 records, every column explicitly listed).
         */
        $currentDateTime = Carbon::now();

        DB::table('sx_business_entity_value')->upsert(
            [
                [
                    'id'                       => 1,
                    'org_id'                   => 1,
                    'entity_id'                => 1,
                    'structure_id'             => 1,
                    'created_by'               => 0,
                    'updated_by'               => 0,
                    'deleted_at'               => null,
                    'entity_value'             => 'India',
                    'entity_value_id'          => 'SHXENV00000001',
                    'street'                   => 'Visakhapatnam',
                    'city'                     => 'Visakhapatnam',
                    'state'                    => 'Andhra Pradesh',
                    'country'                  => 'India',
                    'email_id'                 => '<EMAIL>',
                    'phone'                    => '9966314178',
                    'fax'                      => '56746545647',
                    'zipcode'                  => '530017',
                    'status'                   => 1,
                    'parent_entity_value_id'   => 0,
                    'created_at'               => $currentDateTime,
                    'updated_at'               => null,
                ],
                [
                    'id'                       => 2,
                    'org_id'                   => 1,
                    'entity_id'                => 2,
                    'structure_id'             => 1,
                    'created_by'               => 0,
                    'updated_by'               => 0,
                    'deleted_at'               => null,
                    'entity_value'             => 'Visakhapatnam',
                    'entity_value_id'          => 'SHXENV00000002',
                    'street'                   => 'Visakhapatnam',
                    'city'                     => 'Visakhapatnam',
                    'state'                    => 'Andhra Pradesh',
                    'country'                  => 'India',
                    'email_id'                 => '<EMAIL>',
                    'phone'                    => '9966314178',
                    'fax'                      => '8765766683',
                    'zipcode'                  => '530017',
                    'status'                   => 1,
                    'parent_entity_value_id'   => 1,
                    'created_at'               => $currentDateTime,
                    'updated_at'               => null,
                ],
                [
                    'id'                       => 3,
                    'org_id'                   => 1,
                    'entity_id'                => 3,
                    'structure_id'             => 1,
                    'created_by'               => 0,
                    'updated_by'               => 0,
                    'deleted_at'               => null,
                    'entity_value'             => 'Logistics',
                    'entity_value_id'          => 'SHXENV00000003',
                    'street'                   => 'Visakhapatnam',
                    'city'                     => 'Visakhapatnam',
                    'state'                    => 'Andhra Pradesh',
                    'country'                  => 'India',
                    'email_id'                 => '<EMAIL>',
                    'phone'                    => '9966314178',
                    'fax'                      => '567465456',
                    'zipcode'                  => '530017',
                    'status'                   => 1,
                    'parent_entity_value_id'   => 2,
                    'created_at'               => $currentDateTime,
                    'updated_at'               => null,
                ],
            ],
            ['id'],  // unique key for upsert
            [
                'org_id','entity_id','structure_id','created_by','updated_by',
                'deleted_at','entity_value','entity_value_id','street','city',
                'state','country','email_id','phone','fax','zipcode','status',
                'parent_entity_value_id','created_at','updated_at'
            ]
        );
    }
}
