<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;

class SxBusinessEntitySeeder extends Seeder
{
    public function run(): void
    {
        $currentDateTime = Carbon::now();

        DB::table('sx_business_entity')->upsert(
            [
                [
                    'id'                => 1,
                    'org_id'            => 1,
                    'entity_id'         => 'RAMCMP',
                    'entity_name'       => 'RAMCMP',
                    'entity_description'=> 'RAMCMP',
                    'created_by'        => 0,
                    'updated_by'        => 0,
                    'deleted_at'        => null,
                    'legal_entity'      => 1,
                    'balancing_entity'  => 1,
                    'status'            => 1,
                    'created_at'        => $currentDateTime,
                    'updated_at'        => null,
                ],
                [
                    'id'                => 2,
                    'org_id'            => 1,
                    'entity_id'         => 'RAMBRNCH',
                    'entity_name'       => 'RAMBRNCH',
                    'entity_description'=> 'RAMBRNCH',
                    'created_by'        => 0,
                    'updated_by'        => 0,
                    'deleted_at'        => null,
                    'legal_entity'      => 1,
                    'balancing_entity'  => 1,
                    'status'            => 1,
                    'created_at'        => $currentDateTime,
                    'updated_at'        => null,
                ],
                [
                    'id'                => 3,
                    'org_id'            => 1,
                    'entity_id'         => 'RAMDEPT',
                    'entity_name'       => 'RAMDEPT',
                    'entity_description'=> 'RAMDEPT',
                    'created_by'        => 0,
                    'updated_by'        => 0,
                    'deleted_at'        => null,
                    'legal_entity'      => 1,
                    'balancing_entity'  => 1,
                    'status'            => 1,
                    'created_at'        => $currentDateTime,
                    'updated_at'        => null,
                ],
            ],
            ['id'],   // unique key for upsert
            ['org_id','entity_id','entity_name','entity_description','created_by',
             'updated_by','deleted_at','legal_entity','balancing_entity','status',
             'created_at','updated_at']
        );
    }
}
