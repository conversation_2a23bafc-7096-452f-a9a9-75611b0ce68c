<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxOrgModuleFeaturesSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('sx_org_module_features')->upsert(
            [
                [
                    'id' => 1,
                    'org_id' => 1,
                    'feature_id' => 48,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 2,
                    'org_id' => 1,
                    'feature_id' => 43,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 3,
                    'org_id' => 1,
                    'feature_id' => 42,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 4,
                    'org_id' => 1,
                    'feature_id' => 41,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 5,
                    'org_id' => 1,
                    'feature_id' => 40,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 6,
                    'org_id' => 1,
                    'feature_id' => 39,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 7,
                    'org_id' => 1,
                    'feature_id' => 47,
                    'module_id' => 0,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 8,
                    'org_id' => 1,
                    'feature_id' => 48,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 9,
                    'org_id' => 1,
                    'feature_id' => 47,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 10,
                    'org_id' => 1,
                    'feature_id' => 46,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 11,
                    'org_id' => 1,
                    'feature_id' => 45,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 12,
                    'org_id' => 1,
                    'feature_id' => 40,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 13,
                    'org_id' => 1,
                    'feature_id' => 39,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 14,
                    'org_id' => 1,
                    'feature_id' => 38,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 15,
                    'org_id' => 1,
                    'feature_id' => 44,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 16,
                    'org_id' => 1,
                    'feature_id' => 43,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 17,
                    'org_id' => 1,
                    'feature_id' => 42,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 18,
                    'org_id' => 1,
                    'feature_id' => 41,
                    'module_id' => 7,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 19,
                    'org_id' => 1,
                    'feature_id' => 75,
                    'module_id' => 9,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 20,
                    'org_id' => 1,
                    'feature_id' => 74,
                    'module_id' => 9,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 21,
                    'org_id' => 1,
                    'feature_id' => 11,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 22,
                    'org_id' => 1,
                    'feature_id' => 10,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 23,
                    'org_id' => 1,
                    'feature_id' => 9,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 24,
                    'org_id' => 1,
                    'feature_id' => 8,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 25,
                    'org_id' => 1,
                    'feature_id' => 7,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 26,
                    'org_id' => 1,
                    'feature_id' => 6,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
                [
                    'id' => 27,
                    'org_id' => 1,
                    'feature_id' => 5,
                    'module_id' => 2,
                    'deleted_at' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null,
                ],
            ],
            ['id'],  // unique key for upsert
            ['org_id', 'feature_id', 'module_id', 'deleted_at', 'status', 'created_at', 'updated_at']
        );
    }
}