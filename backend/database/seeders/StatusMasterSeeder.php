<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class StatusMasterSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('status_master')->upsert(
            [
                [
                    'id' => 1,
                    'status_name' => 'Pending',
                    'description' => 'Pending',
                    'status_type' => 'Pending',
                    'status_code' => 'Pending',
                    'customer_id' => null,
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'status_name' => 'Success',
                    'description' => 'Active',
                    'status_type' => 'Active',
                    'status_code' => 'Active',
                    'customer_id' => null,
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                // Add more rows as needed
            ],
            ['id'],  // unique key(s) for upsert
            [
                'status_name', 'description', 'status_type', 'status_code',
                'customer_id', 'user_id', 'org_id', 'be_value', 'status',
                'created_at', 'updated_at'
            ]
        );
    }
}
