<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxModuleFeaturesSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('sx_module_features')->upsert(
            [
                [
                    'id' => 1,
                    'feature_name' => 'Dashboard',
                    'description' => 'Dashboard',
                    'module_id' => 1,
                    'method_name' => 'dashboard',
                    'deleted_at' => null,
                    'menu_name' => 'Dashboard',
                    'status' => 1,
                    'menu_sequence' => 1,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 2,
                    'feature_name' => 'FleetView',
                    'description' => 'Fleet View',
                    'module_id' => 1,
                    'method_name' => 'fleetview',
                    'deleted_at' => null,
                    'menu_name' => 'Fleet View',
                    'status' => 1,
                    'menu_sequence' => 2,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 3,
                    'feature_name' => 'Wrokbench',
                    'description' => 'Wrokbench',
                    'module_id' => 1,
                    'method_name' => 'wrokbench',
                    'deleted_at' => null,
                    'menu_name' => 'Wrokbench',
                    'status' => 1,
                    'menu_sequence' => 3,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 4,
                    'feature_name' => 'Maintenance',
                    'description' => 'Maintenance',
                    'module_id' => 1,
                    'method_name' => 'maintenance',
                    'deleted_at' => null,
                    'menu_name' => 'Maintenance',
                    'status' => 1,
                    'menu_sequence' => 4,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 5,
                    'feature_name' => 'Orders',
                    'description' => 'Orders',
                    'module_id' => 2,
                    'method_name' => 'orders',
                    'deleted_at' => null,
                    'menu_name' => 'Orders',
                    'status' => 1,
                    'menu_sequence' => 5,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 6,
                    'feature_name' => 'BulkUpdate',
                    'description' => 'Bulk Update',
                    'module_id' => 2,
                    'method_name' => 'bulkupdate',
                    'deleted_at' => null,
                    'menu_name' => 'Bulk Update',
                    'status' => 1,
                    'menu_sequence' => 6,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 7,
                    'feature_name' => 'DocuemntControl',
                    'description' => 'Docuemnt Control',
                    'module_id' => 2,
                    'method_name' => 'docuemntcontrol',
                    'deleted_at' => null,
                    'menu_name' => 'Docuemnt Control',
                    'status' => 1,
                    'menu_sequence' => 7,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 8,
                    'feature_name' => 'BulkUpdate',
                    'description' => 'Bulk Update',
                    'module_id' => 2,
                    'method_name' => 'bulkupdate',
                    'deleted_at' => null,
                    'menu_name' => 'Bulk Update',
                    'status' => 1,
                    'menu_sequence' => 8,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 9,
                    'feature_name' => 'Palletizer',
                    'description' => 'Palletizer',
                    'module_id' => 2,
                    'method_name' => 'palletizer',
                    'deleted_at' => null,
                    'menu_name' => 'Palletizer',
                    'status' => 1,
                    'menu_sequence' => 9,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 10,
                    'feature_name' => 'Claims',
                    'description' => 'Claims',
                    'module_id' => 2,
                    'method_name' => 'claims',
                    'deleted_at' => null,
                    'menu_name' => 'Claims',
                    'status' => 1,
                    'menu_sequence' => 10,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 11,
                    'feature_name' => 'Ports',
                    'description' => 'Ports',
                    'module_id' => 2,
                    'method_name' => 'ports',
                    'deleted_at' => null,
                    'menu_name' => 'Ports',
                    'status' => 1,
                    'menu_sequence' => 11,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 12,
                    'feature_name' => 'ShipmentPlan',
                    'description' => 'Shipment Plan',
                    'module_id' => 3,
                    'method_name' => 'shipmentplan',
                    'deleted_at' => null,
                    'menu_name' => 'Shipment Plan',
                    'status' => 1,
                    'menu_sequence' => 12,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 13,
                    'feature_name' => 'Routing',
                    'description' => 'Routing',
                    'module_id' => 3,
                    'method_name' => 'routing',
                    'deleted_at' => null,
                    'menu_name' => 'Routing',
                    'status' => 1,
                    'menu_sequence' => 13,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 14,
                    'feature_name' => 'Trips',
                    'description' => 'Trips',
                    'module_id' => 3,
                    'method_name' => 'trips',
                    'deleted_at' => null,
                    'menu_name' => 'Trips',
                    'status' => 1,
                    'menu_sequence' => 14,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 15,
                    'feature_name' => 'TripTemplate',
                    'description' => 'Trip Template',
                    'module_id' => 3,
                    'method_name' => 'triptemplate',
                    'deleted_at' => null,
                    'menu_name' => 'Trip Template',
                    'status' => 1,
                    'menu_sequence' => 15,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 16,
                    'feature_name' => 'TripExpense',
                    'description' => 'Trip Expense',
                    'module_id' => 3,
                    'method_name' => 'tripexpense',
                    'deleted_at' => null,
                    'menu_name' => 'Trip Expense',
                    'status' => 1,
                    'menu_sequence' => 16,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 17,
                    'feature_name' => 'ReturnTrucks',
                    'description' => 'Return Trucks',
                    'module_id' => 3,
                    'method_name' => 'returntrucks',
                    'deleted_at' => null,
                    'menu_name' => 'Return Trucks',
                    'status' => 1,
                    'menu_sequence' => 17,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 18,
                    'feature_name' => 'XBorderOrders',
                    'description' => 'XBorder Orders',
                    'module_id' => 4,
                    'method_name' => 'xborderorders',
                    'deleted_at' => null,
                    'menu_name' => 'XBorder Orders',
                    'status' => 1,
                    'menu_sequence' => 18,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 19,
                    'feature_name' => 'XBorderTrips',
                    'description' => 'XBorder Trips',
                    'module_id' => 4,
                    'method_name' => 'xbordertrips',
                    'deleted_at' => null,
                    'menu_name' => 'XBorder Trips',
                    'status' => 1,
                    'menu_sequence' => 19,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 20,
                    'feature_name' => 'ActiveOrders',
                    'description' => 'Active Orders',
                    'module_id' => 5,
                    'method_name' => 'activeorders',
                    'deleted_at' => null,
                    'menu_name' => 'Active Orders',
                    'status' => 1,
                    'menu_sequence' => 20,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 21,
                    'feature_name' => 'PendingOrders',
                    'description' => 'Pending Orders',
                    'module_id' => 5,
                    'method_name' => 'pendingorders',
                    'deleted_at' => null,
                    'menu_name' => 'Pending Orders',
                    'status' => 1,
                    'menu_sequence' => 21,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 22,
                    'feature_name' => 'CompletedOrders',
                    'description' => 'Completed Orders',
                    'module_id' => 5,
                    'method_name' => 'completedorders',
                    'deleted_at' => null,
                    'menu_name' => 'Completed Orders',
                    'status' => 1,
                    'menu_sequence' => 22,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 23,
                    'feature_name' => 'ByMilestone',
                    'description' => 'By Milestone',
                    'module_id' => 5,
                    'method_name' => 'bymilestone',
                    'deleted_at' => null,
                    'menu_name' => 'By Milestone',
                    'status' => 1,
                    'menu_sequence' => 23,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 24,
                    'feature_name' => 'Billing',
                    'description' => 'Billing',
                    'module_id' => 6,
                    'method_name' => 'billing',
                    'deleted_at' => null,
                    'menu_name' => 'Billing',
                    'status' => 1,
                    'menu_sequence' => 24,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 25,
                    'feature_name' => 'VATMaster',
                    'description' => 'VAT Master',
                    'module_id' => 6,
                    'method_name' => 'vatmaster',
                    'deleted_at' => null,
                    'menu_name' => 'VAT Master',
                    'status' => 1,
                    'menu_sequence' => 25,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 26,
                    'feature_name' => 'RateService',
                    'description' => 'Rate Service',
                    'module_id' => 6,
                    'method_name' => 'rateservice',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Service',
                    'status' => 1,
                    'menu_sequence' => 26,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 27,
                    'feature_name' => 'RateOffering',
                    'description' => 'Rate Offering',
                    'module_id' => 6,
                    'method_name' => 'rateoffering',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Offering',
                    'status' => 1,
                    'menu_sequence' => 27,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 28,
                    'feature_name' => 'RateRecord',
                    'description' => 'Rate Record',
                    'module_id' => 6,
                    'method_name' => 'raterecord',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Record',
                    'status' => 1,
                    'menu_sequence' => 28,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 29,
                    'feature_name' => 'RatePreference',
                    'description' => 'Rate Preference',
                    'module_id' => 6,
                    'method_name' => 'ratepreference',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Preference',
                    'status' => 1,
                    'menu_sequence' => 29,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 30,
                    'feature_name' => 'Tiers',
                    'description' => 'Tiers',
                    'module_id' => 6,
                    'method_name' => 'tiers',
                    'deleted_at' => null,
                    'menu_name' => 'Tiers',
                    'status' => 1,
                    'menu_sequence' => 30,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 31,
                    'feature_name' => 'RateCalendar',
                    'description' => 'Rate Calendar',
                    'module_id' => 6,
                    'method_name' => 'ratecalendar',
                    'deleted_at' => null,
                    'menu_name' => 'Rate Calendar',
                    'status' => 1,
                    'menu_sequence' => 31,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 32,
                    'feature_name' => 'ExchangeRate',
                    'description' => 'Exchange Rate',
                    'module_id' => 6,
                    'method_name' => 'exchangerate',
                    'deleted_at' => null,
                    'menu_name' => 'Exchange Rate',
                    'status' => 1,
                    'menu_sequence' => 32,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 33,
                    'feature_name' => 'ConversionFactor',
                    'description' => 'Conversion Factor',
                    'module_id' => 6,
                    'method_name' => 'conversionfactor',
                    'deleted_at' => null,
                    'menu_name' => 'Conversion Factor',
                    'status' => 1,
                    'menu_sequence' => 33,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 34,
                    'feature_name' => 'FuelSurcharge',
                    'description' => 'Fuel Surcharge',
                    'module_id' => 6,
                    'method_name' => 'fuelsurcharge',
                    'deleted_at' => null,
                    'menu_name' => 'Fuel Surcharge',
                    'status' => 1,
                    'menu_sequence' => 34,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 35,
                    'feature_name' => 'CostAudit',
                    'description' => 'Cost Audit',
                    'module_id' => 6,
                    'method_name' => 'costaudit',
                    'deleted_at' => null,
                    'menu_name' => 'Cost Audit',
                    'status' => 1,
                    'menu_sequence' => 35,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 36,
                    'feature_name' => 'Consolidation',
                    'description' => 'Consolidation',
                    'module_id' => 6,
                    'method_name' => 'consolidation',
                    'deleted_at' => null,
                    'menu_name' => 'Consolidation',
                    'status' => 1,
                    'menu_sequence' => 36,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 37,
                    'feature_name' => 'QuickRates',
                    'description' => 'Quick Rates',
                    'module_id' => 6,
                    'method_name' => 'quickrates',
                    'deleted_at' => null,
                    'menu_name' => 'Quick Rates',
                    'status' => 1,
                    'menu_sequence' => 37,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 38,
                    'feature_name' => 'CommunicationManagement',
                    'description' => 'Communication Management',
                    'module_id' => 7,
                    'method_name' => 'communicationmanagement',
                    'deleted_at' => null,
                    'menu_name' => 'Communication Management',
                    'status' => 1,
                    'menu_sequence' => 38,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 39,
                    'feature_name' => 'Fleet',
                    'description' => 'Fleet',
                    'module_id' => 7,
                    'method_name' => '[{"id":"Drivers"},{"id":"Vehicles"},{"id":"Order Type"},{"id":"Cost Center"},{"id":"Vehicle Type"}]',
                    'deleted_at' => null,
                    'menu_name' => 'Fleet',
                    'status' => 1,
                    'menu_sequence' => 39,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 40,
                    'feature_name' => 'BusinessPartners',
                    'description' => 'Business Partners',
                    'module_id' => 7,
                    'method_name' => 'businesspartners',
                    'deleted_at' => null,
                    'menu_name' => 'Business Partners',
                    'status' => 1,
                    'menu_sequence' => 40,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 41,
                    'feature_name' => 'TrafficCode',
                    'description' => 'Traffic Code',
                    'module_id' => 7,
                    'method_name' => 'trafficcode',
                    'deleted_at' => null,
                    'menu_name' => 'Traffic Code',
                    'status' => 1,
                    'menu_sequence' => 41,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 42,
                    'feature_name' => 'StatusMaster',
                    'description' => 'Status Master',
                    'module_id' => 7,
                    'method_name' => 'statusmaster',
                    'deleted_at' => null,
                    'menu_name' => 'Status Master',
                    'status' => 1,
                    'menu_sequence' => 42,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 43,
                    'feature_name' => 'LaneMaster',
                    'description' => 'Lane Master',
                    'module_id' => 7,
                    'method_name' => 'lanemaster',
                    'deleted_at' => null,
                    'menu_name' => 'Lane Master',
                    'status' => 1,
                    'menu_sequence' => 43,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 44,
                    'feature_name' => 'Profiles',
                    'description' => 'Profiles',
                    'module_id' => 7,
                    'method_name' => '[{"id":"Customer Profile"},{"id":"Vendor Profile"},{"id":"Vehicle Profile"}]',
                    'deleted_at' => null,
                    'menu_name' => 'Profiles',
                    'status' => 1,
                    'menu_sequence' => 44,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 45,
                    'feature_name' => 'Pincodes',
                    'description' => 'Pincodes',
                    'module_id' => 7,
                    'method_name' => '[{"id":"Pincodes"},{"id":"Preferred State"},{"id":"Trip Allocation Ratio"}]',
                    'deleted_at' => null,
                    'menu_name' => 'Pincodes',
                    'status' => 1,
                    'menu_sequence' => 45,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 46,
                    'feature_name' => 'AllocationRules',
                    'description' => 'Allocation Rules',
                    'module_id' => 7,
                    'method_name' => 'allocationrules',
                    'deleted_at' => null,
                    'menu_name' => 'Allocation Rules',
                    'status' => 1,
                    'menu_sequence' => 46,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 47,
                    'feature_name' => 'ShipmentTypes',
                    'description' => 'Shipment Types',
                    'module_id' => 7,
                    'method_name' => 'shipmenttypes',
                    'deleted_at' => null,
                    'menu_name' => 'Shipment Types',
                    'status' => 1,
                    'menu_sequence' => 47,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 48,
                    'feature_name' => 'Regions',
                    'description' => 'Regions',
                    'module_id' => 7,
                    'method_name' => 'regions',
                    'deleted_at' => null,
                    'menu_name' => 'Regions',
                    'status' => 1,
                    'menu_sequence' => 48,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 49,
                    'feature_name' => 'TripReports',
                    'description' => 'Trip Reports',
                    'module_id' => 8,
                    'method_name' => 'tripreports',
                    'deleted_at' => null,
                    'menu_name' => 'Trip Reports',
                    'status' => 1,
                    'menu_sequence' => 49,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 50,
                    'feature_name' => 'SLA/Occupancy',
                    'description' => 'SLA / Occupancy',
                    'module_id' => 8,
                    'method_name' => 'slaoccupancy',
                    'deleted_at' => null,
                    'menu_name' => 'SLA/Occupancy',
                    'status' => 1,
                    'menu_sequence' => 50,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 51,
                    'feature_name' => 'Shipment/StopLevel',
                    'description' => 'Shipment / Stop Level',
                    'module_id' => 8,
                    'method_name' => 'shipmentstoplevel',
                    'deleted_at' => null,
                    'menu_name' => 'Shipment/Stop Level',
                    'status' => 1,
                    'menu_sequence' => 51,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 52,
                    'feature_name' => 'KMReports',
                    'description' => 'KM Reports',
                    'module_id' => 8,
                    'method_name' => 'kmreports',
                    'deleted_at' => null,
                    'menu_name' => 'KM Reports',
                    'status' => 1,
                    'menu_sequence' => 52,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 53,
                    'feature_name' => 'SpeedometerGraph',
                    'description' => 'Speedometer Graph',
                    'module_id' => 8,
                    'method_name' => 'speedometergraph',
                    'deleted_at' => null,
                    'menu_name' => 'Speedometer Graph',
                    'status' => 1,
                    'menu_sequence' => 53,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 54,
                    'feature_name' => 'SpeedReport',
                    'description' => 'Speed Report',
                    'module_id' => 8,
                    'method_name' => 'speedreport',
                    'deleted_at' => null,
                    'menu_name' => 'Speed Report',
                    'status' => 1,
                    'menu_sequence' => 54,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 55,
                    'feature_name' => 'SMSReport',
                    'description' => 'SMS Report',
                    'module_id' => 8,
                    'method_name' => 'smsreport',
                    'deleted_at' => null,
                    'menu_name' => 'SMS Report',
                    'status' => 1,
                    'menu_sequence' => 55,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 56,
                    'feature_name' => 'OperationalReports',
                    'description' => 'Operational Reports',
                    'module_id' => 8,
                    'method_name' => 'operationalreports',
                    'deleted_at' => null,
                    'menu_name' => 'Operational Reports',
                    'status' => 1,
                    'menu_sequence' => 56,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 57,
                    'feature_name' => 'FinancialReports',
                    'description' => 'Financial Reports',
                    'module_id' => 8,
                    'method_name' => 'financialreports',
                    'deleted_at' => null,
                    'menu_name' => 'Financial Reports',
                    'status' => 1,
                    'menu_sequence' => 57,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 58,
                    'feature_name' => 'CustomerDailyReport',
                    'description' => 'Customer Daily Report',
                    'module_id' => 8,
                    'method_name' => 'customerdailyreport',
                    'deleted_at' => null,
                    'menu_name' => 'Customer Daily Report',
                    'status' => 1,
                    'menu_sequence' => 58,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 59,
                    'feature_name' => 'AllRoundBigReport',
                    'description' => 'All Round Big Report',
                    'module_id' => 8,
                    'method_name' => 'allroundbigreport',
                    'deleted_at' => null,
                    'menu_name' => 'All Round Big Report',
                    'status' => 1,
                    'menu_sequence' => 59,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 60,
                    'feature_name' => 'BillingControlReport',
                    'description' => 'Billing Control Report',
                    'module_id' => 8,
                    'method_name' => 'billingcontrolreport',
                    'deleted_at' => null,
                    'menu_name' => 'Billing Control Report',
                    'status' => 1,
                    'menu_sequence' => 60,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 61,
                    'feature_name' => 'EDILog',
                    'description' => 'EDI Log',
                    'module_id' => 8,
                    'method_name' => 'edilog',
                    'deleted_at' => null,
                    'menu_name' => 'EDI Log',
                    'status' => 1,
                    'menu_sequence' => 61,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 62,
                    'feature_name' => 'TrackReport',
                    'description' => 'Track Report',
                    'module_id' => 8,
                    'method_name' => 'trackreport',
                    'deleted_at' => null,
                    'menu_name' => 'Track Report',
                    'status' => 1,
                    'menu_sequence' => 62,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 63,
                    'feature_name' => 'MISReport',
                    'description' => 'MIS Report',
                    'module_id' => 8,
                    'method_name' => 'misreport',
                    'deleted_at' => null,
                    'menu_name' => 'MIS Report',
                    'status' => 1,
                    'menu_sequence' => 63,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 64,
                    'feature_name' => 'NDRReport',
                    'description' => 'NDR Report',
                    'module_id' => 8,
                    'method_name' => 'ndrreport',
                    'deleted_at' => null,
                    'menu_name' => 'NDR Report',
                    'status' => 1,
                    'menu_sequence' => 64,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 65,
                    'feature_name' => 'CustomizedReport',
                    'description' => 'Customized Report',
                    'module_id' => 8,
                    'method_name' => 'customizedreport',
                    'deleted_at' => null,
                    'menu_name' => 'Customized Report',
                    'status' => 1,
                    'menu_sequence' => 65,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 66,
                    'feature_name' => 'TransitStatusReport',
                    'description' => 'Transit Status Report',
                    'module_id' => 8,
                    'method_name' => 'transitstatusreport',
                    'deleted_at' => null,
                    'menu_name' => 'Transit Status Report',
                    'status' => 1,
                    'menu_sequence' => 66,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 67,
                    'feature_name' => 'KPIOneDay',
                    'description' => 'KPI One Day',
                    'module_id' => 8,
                    'method_name' => 'kpioneday',
                    'deleted_at' => null,
                    'menu_name' => 'KPI One Day',
                    'status' => 1,
                    'menu_sequence' => 67,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 68,
                    'feature_name' => 'ArchivedDocument',
                    'description' => 'Archived Document',
                    'module_id' => 8,
                    'method_name' => 'archiveddocument',
                    'deleted_at' => null,
                    'menu_name' => 'Archived Document',
                    'status' => 1,
                    'menu_sequence' => 68,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 69,
                    'feature_name' => 'TATKPIReport',
                    'description' => 'TAT KPI Report',
                    'module_id' => 8,
                    'method_name' => 'tatkpireport',
                    'deleted_at' => null,
                    'menu_name' => 'TAT KPI Report',
                    'status' => 1,
                    'menu_sequence' => 69,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 70,
                    'feature_name' => 'CarrierKPIReport',
                    'description' => 'Carrier KPI Report',
                    'module_id' => 8,
                    'method_name' => 'carrierkpireport',
                    'deleted_at' => null,
                    'menu_name' => 'Carrier KPI Report',
                    'status' => 1,
                    'menu_sequence' => 70,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 71,
                    'feature_name' => 'OrdersReport',
                    'description' => 'Orders Report',
                    'module_id' => 8,
                    'method_name' => 'ordersreport',
                    'deleted_at' => null,
                    'menu_name' => 'Orders Report',
                    'status' => 1,
                    'menu_sequence' => 71,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 72,
                    'feature_name' => 'SALogKPIReport',
                    'description' => 'SALog KPI Report',
                    'module_id' => 8,
                    'method_name' => 'salogkpireport',
                    'deleted_at' => null,
                    'menu_name' => 'SALog KPI Report',
                    'status' => 1,
                    'menu_sequence' => 72,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 73,
                    'feature_name' => 'GeoFence',
                    'description' => 'Geo Fence',
                    'module_id' => 8,
                    'method_name' => 'geofence',
                    'deleted_at' => null,
                    'menu_name' => 'Geo Fence',
                    'status' => 1,
                    'menu_sequence' => 73,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 74,
                    'feature_name' => 'ShipmentTender',
                    'description' => 'Shipment Tender',
                    'module_id' => 9,
                    'method_name' => 'shipmenttender',
                    'deleted_at' => null,
                    'menu_name' => 'Shipment Tender',
                    'status' => 1,
                    'menu_sequence' => 74,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ],
                [
                    'id' => 75,
                    'feature_name' => 'CarrierRates',
                    'description' => 'Carrier Rates',
                    'module_id' => 9,
                    'method_name' => 'carrierrates',
                    'deleted_at' => null,
                    'menu_name' => 'Carrier Rates',
                    'status' => 1,
                    'menu_sequence' => 75,
                    'created_at' => '2025-07-03 04:00:00',
                    'updated_at' => '2025-07-03 04:00:00',
                ]
            ],
            ['id'], // primary/unique key
            ['feature_name','description','module_id','method_name',
             'deleted_at','menu_name','status','menu_sequence','created_at','updated_at']
        );
    }
}
