<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AddressTypesSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('address_types')->upsert(
            [
                [
                    'id' => 1,
                    'address_type_name' => 'Residential Address',
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'address_type_name' => 'Physical Address',
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 3,
                    'address_type_name' => 'Permanent Address',
                    'user_id' => 0,
                    'org_id' => 1,
                    'be_value' => null,
                    'status' => 1,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['address_type_name', 'user_id', 'org_id', 'be_value', 'status', 'created_at', 'updated_at']
        );
    }
}
