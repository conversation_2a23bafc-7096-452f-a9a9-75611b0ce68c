<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxUsersSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $users = [
            [
                'id' => 1,
                'employee_id' => 'RAMA001',
                'employee_name' => 'RAMA001',
                'username' => '<EMAIL>',
                'password' => '$2y$12$NeJ6jnndaomkpUU266dznOQiKgthEEUyuQ8ukgwI7X4QYv57IpYUa',
                'effective_fromdate' => '2025-07-08',
                'effective_enddate' => '2025-07-31',
                'contact_num' => '9966314178',
                'theme_id' => 1,
                'currency' => 'USD',
                'date_zone' => null,
                'number_format' => '1,22',
                'created_by' => 0,
                'updated_by' => 0,
                'deleted_at' => null,
                'languages' => '["1"]',
                'date_format' => 'd/m/Y',
                'default_org_id' => 1,
                'country_code' => null,
                'default_currency' => null,
                'lat' => null,
                'lng' => null,
                'status' => 1,
                'emailid' => null,
                'refresh_token' => null,
                'geolocation' => null,
                'logo' => null,
                'created_at' => '2025-07-08 02:53:05',
                'updated_at' => '2025-07-08 02:53:05',
            ],
        ];

        DB::table('sx_users')->insert($users);
    }
}