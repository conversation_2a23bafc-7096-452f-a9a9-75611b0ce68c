<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TruckTypesSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('trucktypes')->upsert(
            [
                [
                    'id' => 1,
                    'trucktype' => '10 wheeler heavy vehicle',
                    'description' => '10 wheeler heavy vehicle',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'iconimg' => null,
                    'createdby' => 0,
                    'updatedby' => 0,
                    'created_at' => null,
                    'updated_at' => null
                ],
                [
                    'id' => 2,
                    'trucktype' => '6 wheeler heavy vehicle',
                    'description' => 'Container',
                    'org_id' => 1,
                    'be_value' => 0,
                    'user_id' => null,
                    'status' => 1,
                    'iconimg' => null,
                    'createdby' => 0,
                    'updatedby' => 0,
                    'created_at' => null,
                    'updated_at' => null
                ],
            ],
            ['id'],
            ['trucktype', 'description', 'org_id', 'be_value', 'user_id', 'status', 'iconimg', 'createdby', 'updatedby', 'created_at', 'updated_at']
        );
    }
}