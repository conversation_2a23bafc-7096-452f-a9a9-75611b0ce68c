<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxSuperAdminSeeder extends Seeder
{
    /**
     * Run the seeder.
     *
     * @return void
     */
    public function run()
    {
        $superAdmins = [
            [
                'id'          => 1,
                'username'    => 'shipmentx',
                'password'    => 'eebda9b6e78492c26af4a60bf3649d12',
                'org_id'      => 0,
                'first_name'  => null,
                'last_name'   => null,
                'email'       => null,
                'mobile'      => null,
                'status'      => 1,
                'created_at'  => '2022-07-12 16:48:51',
                'updated_at'  => '2022-07-12 22:18:51',
            ],
            [
                'id' => 2,
                'username' => 'RamCo_Admin',
                'password' => 'c2144df9d5fa1b97c5b6525980ee5e20',
                'org_id' => 1,
                'first_name' => 'RamCo',
                'last_name' => 'Admin',
                'email' => 'kamb<PERSON><PERSON><PERSON><EMAIL>',
                'mobile' => '9966314178',
                'status' => 1,
                'created_at' => null,
                'updated_at' => null,
            ],
        ];

        DB::table('sx_super_admin')->insert($superAdmins);
    }
}