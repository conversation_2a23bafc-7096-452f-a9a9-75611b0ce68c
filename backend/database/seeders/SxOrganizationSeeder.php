<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class SxOrganizationSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('sx_organization')->upsert(
            [
                [
                    'id'                   => 1,
                    'org_id'               => 'RamCo',
                    'org_name'             => 'RamCo',
                    'org_description'      => 'RamCo',
                    'total_users'          => 100,
                    'concurrent_users'     => 100,
                    'buffer_users'         => 100,
                    'effective_from'       => '2025-07-08 00:00:00',
                    'effective_to'         => '2025-07-31 00:00:00',
                    'buffer_days'          => 100,
                    'alert_contact_admin'  => '9966314178',
                    'alert_contact_legal'  => '9966314178',
                    'alter_contact_sales'  => '9966314178',
                    'auth_name'            => 'RCREDDY K',
                    'contact_num'          => '9966314178',
                    'email_id'             => 'kamb<PERSON><PERSON><PERSON><EMAIL>',
                    'address'              => 'Visakhapatnam',
                    'deleted_at'           => null,
                    'logo'                 => 'ramco.jpg',
                    'status'               => 1,
                    'created_at'           => '2025-07-08 02:36:25',
                    'updated_at'           => '2025-07-08 02:36:25',
                ],
            ],
            ['id'],  // primary key for UPSERT
            ['org_id','org_name','org_description','total_users','concurrent_users',
             'buffer_users','effective_from','effective_to','buffer_days',
             'alert_contact_admin','alert_contact_legal','alter_contact_sales',
             'auth_name','contact_num','email_id','address','deleted_at','logo',
             'status','updated_at']
        );
    }
}
