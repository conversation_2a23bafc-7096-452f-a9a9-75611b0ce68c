# Stage 1: Composer dependencies
FROM composer:2.8.9 AS composer-stage
WORKDIR /app

COPY composer.json composer.lock ./
RUN composer install --no-interaction --prefer-dist --no-scripts --optimize-autoloader --ignore-platform-req=ext-sockets

# Stage 2: PHP with Apache
FROM php:8.2.28-apache

# Install system dependencies & PHP extensions
RUN apt-get update && apt-get install -y \
    libpq-dev zip unzip git curl \
 && docker-php-ext-install pdo_pgsql sockets \
 && pecl install redis \
 && docker-php-ext-enable redis \
 && apt-get clean && rm -rf /var/lib/apt/lists/*

# Enable Apache rewrite module
RUN a2enmod rewrite

# Composer binary from official image
COPY --from=composer:2.8.9 /usr/bin/composer /usr/bin/composer

# Working directory
WORKDIR /var/www/html

COPY composer.json composer.lock ./

COPY . .

# To check file sizes in the final image
# This is useful for debugging and optimization
# It will list all files in /var/www/html sorted by size
# and help identify large files that can be optimized or removed.
# Uncomment the next line to enable this check
# RUN du -sh /var/www/html/* | sort -h

# Copy vendor from Composer stage
COPY --from=composer-stage /app/vendor ./vendor

# Copy Apache config and entrypoint last
COPY apache/000-default.conf /etc/apache2/sites-available/000-default.conf
COPY scripts/docker-entrypoint.sh /usr/local/bin/docker-entrypoint
RUN chmod +x /usr/local/bin/docker-entrypoint

ENTRYPOINT ["docker-entrypoint"]
CMD ["apache2-foreground"]
