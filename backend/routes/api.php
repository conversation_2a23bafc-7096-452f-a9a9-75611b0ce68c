<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\BillingController;
use App\Http\Controllers\TrafficCodeController;
use App\Http\Controllers\ShipmentController;
use App\Http\Controllers\VehicleController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\RedisExampleController;
use App\Http\Controllers\TripTemplateController;
use App\Http\Controllers\RoutingController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\VatMasterController;
use App\Http\Controllers\RateServiceController;
use App\Http\Controllers\StatusMasterController;
use App\Http\Controllers\LanesMasterController;
use App\Http\Controllers\DriverController;
use App\Http\Controllers\RateOfferingController;
use App\Http\Controllers\RateRecordController;
use App\Http\Controllers\VehicleProfileController;
use App\Http\Controllers\MasterController;
use App\Http\Controllers\CustomerProfileController;
use App\Http\Controllers\VendorProfileController;
use App\Http\Controllers\NotifymgmtController;
use App\Jobs\RabbitmqHelloWorldJob;


Route::get('/ping', function () {
    return response()->json(['pong' => true]);
});


Route::get('/test-queue1', function () {
    RabbitmqHelloWorldJob::dispatch('Sent to queue1')->onQueue('queue1');
    return 'Dispatched to queue1!';
});

Route::get('/test-queue2', function () {
    RabbitmqHelloWorldJob::dispatch('Sent to queue2')->onQueue('queue2');
    return 'Dispatched to queue2!';
});

Route::post('register', [AuthController::class, 'register']); // Registration disabled
Route::post('login', [AuthController::class, 'login']);
Route::post('refresh', [AuthController::class, 'refresh'])->middleware('throttle:10,1');
Route::post('forgotpassword', [UserController::class, 'forgotPassword']);
Route::post('resetpassword', [UserController::class, 'resetPassword']);

Route::middleware('auth:api')->group(function () {
    Route::post('logout', [AuthController::class, 'logout']);
    Route::get('me', [AuthController::class, 'me']);

        Route::get('shipmentTypes', [ShipmentController::class, 'shipmentTypeIndex']);
    Route::post('shipmentTypes/add', [ShipmentController::class, 'shipmentTypeAdd']);
    Route::post('shipmentTypes', [ShipmentController::class, 'shipmentTypeCreate']);
    Route::get('shipmentTypes/{id}', [ShipmentController::class, 'shipmentTypeShow']);
    Route::post('shipmentTypes/edit/{id}', [ShipmentController::class, 'shipmentTypeEdit']);
    Route::put('shipmentTypes/{id}', [ShipmentController::class, 'shipmentTypeUpdate']);
    Route::delete('shipmentTypes/{id}', [ShipmentController::class, 'shipmentTypeDestroy']);

    Route::get('truckTypes', [VehicleController::class, 'vehicleTypeIndex']);
    Route::get('truckTypes/{id}', [VehicleController::class, 'vehicleTypeShow']);
    Route::get('truckTypes/add', [VehicleController::class, 'vehicleTypeAdd']);
    Route::get('truckTypes/edit/{id}', [VehicleController::class, 'vehicleTypeEdit']);
    Route::post('truckTypes', [VehicleController::class, 'vehicleTypeCreate']);
    Route::put('truckTypes/{id}', [VehicleController::class, 'vehicleTypeUpdate']);
    Route::delete('truckTypes/{id}', [VehicleController::class, 'vehicleTypeDestroy']);

    Route::get('orderTypes', [OrderController::class, 'orderTypeIndex']);
    Route::get('orderTypes/{id}', [OrderController::class, 'orderTypeShow']);
    Route::get('orderTypes/add', [OrderController::class, 'orderTypeAdd']);
    Route::get('orderTypes/edit/{id}', [OrderController::class, 'orderTypeEdit']);
    Route::post('orderTypes', [OrderController::class, 'orderTypeCreate']);
    Route::put('orderTypes/{id}', [OrderController::class, 'orderTypeUpdate']);
    Route::delete('orderTypes/{id}', [OrderController::class, 'orderTypeDestroy']);

    Route::get('regions', [LocationController::class, 'regionIndex']);
    Route::get('regions/{id}', [LocationController::class, 'regionShow']);
    Route::get('regions/add', [LocationController::class, 'regionAdd']);
    Route::get('regions/edit/{id}', [LocationController::class, 'regionEdit']);
    Route::post('regions', [LocationController::class, 'regionStore']);
    Route::put('regions/{id}', [LocationController::class, 'regionUpdate']);
    Route::delete('regions/{id}', [LocationController::class, 'regionDestroy']);

    Route::get('vehicles', [VehicleController::class, 'index']);
    Route::get('vehicles/add', [VehicleController::class, 'add']);
    Route::post('vehicles', [VehicleController::class, 'store']);
    Route::get('vehicles/{id}', [VehicleController::class, 'show']);
    Route::get('vehicles/edit/{id}', [VehicleController::class, 'edit']);
    Route::put('vehicles/{id}', [VehicleController::class, 'update']);
    Route::delete('vehicles/{id}', [VehicleController::class, 'destroy']);

    Route::get('drivers', [DriverController::class, 'index']);
    Route::get('drivers/add', [DriverController::class, 'add']);
    Route::post('drivers', [DriverController::class, 'create']);
    Route::get('drivers/{id}', [DriverController::class, 'show']);
    Route::get('drivers/{id}/edit', [DriverController::class, 'edit']);
    Route::put('drivers/{id}', [DriverController::class, 'update']);
    Route::delete('drivers/{id}', [DriverController::class, 'delete']);

    Route::get('businessPartners', [MasterController::class, 'partners']);
    Route::get('businessPartners/{id}', [MasterController::class, 'partnerShow']);
    Route::post('businessPartners/add', [MasterController::class, 'partnerAdd']);
    Route::post('businessPartners/edit/{id}', [MasterController::class, 'partnerEdit']);
    Route::post('businessPartners', [MasterController::class, 'partnerCreate']);
    Route::put('businessPartners/{id}', [MasterController::class, 'partnerUpdate']);
    Route::delete('businessPartners/{id}', [MasterController::class, 'partnerDestroy']);
});

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::get('/health', function () {
    return response()->json(['status' => 'OK'], 200);
});

Route::middleware('auth:api')->get('/users', [UserController::class, 'index']);

Route::group(['prefix' => 'billing', 'middleware' => 'auth:api'], function () {
    Route::post('/add', [BillingController::class, 'add']);
});

Route::middleware('auth:api')->group(function () {
    //Traffic codes
    Route::get('/traffic-codes', [TrafficCodeController::class, 'index'])->name('traffic-codes.index');
    Route::get('/traffic-codes/create', [TrafficCodeController::class, 'create'])->name('traffic-codes.create');
    Route::post('/traffic-codes/insert', [TrafficCodeController::class, 'insert'])->name('traffic-codes.insert');
    Route::get('/traffic-codes/edit/{id}', [TrafficCodeController::class, 'edit'])->name('traffic-codes.edit');
    Route::put('/traffic-codes/{id}', [TrafficCodeController::class, 'update'])->name('traffic-codes.update');
    Route::delete('/traffic-codes/{id}', [TrafficCodeController::class, 'destroy'])->name('traffic-codes.delete');
    Route::get('/traffic-codes/show/{id}', [TrafficCodeController::class, 'show'])->name('traffic-codes.show');

    //Trip templates
    Route::get('/triptemplates', [TripTemplateController::class, 'triptemplateindex'])->name('triptemplate.index');
    Route::get('/triptemplates/add', [TripTemplateController::class, 'addTemplate'])->name('triptemplate.addTemplate');
    Route::get('/triptemplates/view/{id}', [TripTemplateController::class, 'viewTemplate'])->name('triptemplate.view');
    Route::get('/triptemplates/edit/{id}', [TripTemplateController::class, 'editTemplate'])->name('triptemplate.edit');
    Route::post('/triptemplates/insert', [TripTemplateController::class, 'insertTemplate'])->name('triptemplate.store');
    Route::post('/triptemplates/delete/{id}', [TripTemplateController::class, 'deleteTripTemplateById'])->name('triptemplate.destroy');
    Route::post('/triptemplates/update/{id}', [TripTemplateController::class, 'updateTripWithTripsTemplate'])->name('triptemplate.update-trip');
    Route::get('/triptemplates/master-data', [TripTemplateController::class, 'getMasterDataForTripTemplate'])->name('triptemplate.master-data');
    Route::get('/triptemplates/waypoint-parties', [TripTemplateController::class, 'getWaypointParties'])->name('triptemplate.waypoint-parties');

    //Routing
    Route::get('/routing', [RoutingController::class, 'routingindex'])->name('routing.index');
    Route::get('/routing/add', [RoutingController::class, 'addrouting'])->name('routing.add');
    Route::get('/routing/view/{id}', [RoutingController::class, 'viewrouting'])->name('routing.view');
    Route::get('/routing/edit/{id}', [RoutingController::class, 'editrouting'])->name('routing.edit');
    Route::post('/routing/save', [RoutingController::class, 'saverouting'])->name('routing.save');
    Route::post('/routing/update', [RoutingController::class, 'updaterouting'])->name('routing.update');
    Route::post('/routing/delete', [RoutingController::class, 'deleterouting'])->name('routing.delete');
    Route::get('/routing/items', [RoutingController::class, 'getItemListID'])->name('routing.items');
    Route::get('/routing/cargo', [RoutingController::class, 'viewCargoList'])->name('routing.cargo');
    Route::get('/routing/party-types', [RoutingController::class, 'getAllPartyTypeList'])->name('routing.party-types');

    // VatMaster
    Route::get('/vatmaster', [VatMasterController::class, 'index']);
    Route::get('/vatmaster/add', [VatMasterController::class, 'add']);
    Route::post('/vatmaster/role-types', [VatMasterController::class, 'viewRoleTypeList']);
    Route::post('/vatmaster/partner-details', [VatMasterController::class, 'getPartnerDetailsById']);
    Route::post('/vatmaster/save-lane', [VatMasterController::class, 'saveLane']);
    Route::get('/vatmaster/recent-lanes', [VatMasterController::class, 'getRecentLanes']);
    Route::post('/vatmaster/lane/{id}', [VatMasterController::class, 'getLaneById']);
    Route::post('/vatmaster/update-lane', [VatMasterController::class, 'updateLane']);
    Route::post('/vatmaster/charge-description', [VatMasterController::class, 'getChargeDescription']);
    Route::post('/vatmaster/save-lane-vat', [VatMasterController::class, 'saveLaneVat']);
    Route::post('/vatmaster/lane-vats', [VatMasterController::class, 'getLaneVats']);
    Route::post('/vatmaster/charge/{id}', [VatMasterController::class, 'getChargeById']);
    Route::post('/vatmaster/update-vat', [VatMasterController::class, 'updateVat']);
    Route::post('/vatmaster/delete-charge', [VatMasterController::class, 'deleteCharge']);
    Route::post('/vatmaster/delete-lane', [VatMasterController::class, 'deleteLane']);
    Route::post('/vatmaster/insert-vat', [VatMasterController::class, 'insertVatData']);
    Route::post('/vatmaster/get-lanes', [VatMasterController::class, 'getLanes']);
    Route::get('/vatmaster/edit-vat/{id}', [VatMasterController::class, 'editVat']);
    Route::post('/vatmaster/update-vat-data', [VatMasterController::class, 'updateVatData']);
    Route::get('/vatmaster/view-vat/{id}', [VatMasterController::class, 'viewVat']);
    Route::post('/vatmaster/delete-vat-master', [VatMasterController::class, 'deleteVatMaster']);

    // Rate-Service
    Route::match(['get', 'post'], '/rateservice/index', [RateServiceController::class, 'index']);
    Route::get('/rateservice/add', [RateServiceController::class, 'add']);
    Route::post('/rateservice/getLaneById', [RateServiceController::class, 'getLaneById']);
    Route::post('/rateservice/saveLane', [RateServiceController::class, 'saveLane']);
    Route::post('/rateservice/showLaneDetails', [RateServiceController::class, 'showLaneDetails']);
    Route::post('/rateservice/updateLane/{id}', [RateServiceController::class, 'updateLane']);
    Route::post('/rateservice/deleteLaneDetails', [RateServiceController::class, 'deleteLaneDetails']);
    Route::post('/rateservice/checkServiceName', [RateServiceController::class, 'checkName']);
    Route::post('/rateservice/insertService', [RateServiceController::class, 'insertService']);
    Route::get('/rateservice/edit/{id}', [RateServiceController::class, 'edit']);
    Route::post('/rateservice/editlanedetails', [RateServiceController::class, 'editLaneDetails']);
    Route::post('/rateservice/serviceupdate', [RateServiceController::class, 'serviceUpdate']);
    Route::post('/rateservice/delete', [RateServiceController::class, 'delete']);

    //status master
    Route::get('/statusmaster', [StatusMasterController::class, 'index']);
    Route::get('/statusmaster/add', [StatusMasterController::class, 'add']);
    Route::post('/statusmaster/store', [StatusMasterController::class, 'store']);
    Route::get('/statusmaster/edit/{id}', [StatusMasterController::class, 'edit']);
    Route::put('/statusmaster/update/{id}', [StatusMasterController::class, 'update']);
    Route::get('/statusmaster/view/{id}', [StatusMasterController::class, 'view']);
    Route::delete('/statusmaster/delete/{id}', [StatusMasterController::class, 'delete']);
    Route::post('/statusmaster/checkname', [StatusMasterController::class, 'checkName']);
    Route::post('/statusmaster/checkcode', [StatusMasterController::class, 'checkCode']);
    Route::post('/statusmaster/upload-excel', [StatusMasterController::class, 'uploadStatusMasterExcel']);
    Route::post('/statusmaster/deletestatus', [StatusMasterController::class, 'deleteStatus']);

    //Lanes Master
    Route::get('/lanes', [LanesMasterController::class, 'index']);
    Route::get('/lanes/add', [LanesMasterController::class, 'add']);
    Route::post('/lanes/store', [LanesMasterController::class, 'store']);
    Route::put('/lanes/update/{id}', [LanesMasterController::class, 'update']);
    Route::get('/lanes/edit/{id}', [LanesMasterController::class, 'edit']);
    Route::get('/lanes/view/{id}', [LanesMasterController::class, 'view']);
    Route::delete('/lanes/delete/{id}', [LanesMasterController::class, 'delete']);
    Route::post('/lanes/check-lane', [LanesMasterController::class, 'checkLane']);
    Route::post('/lanes/get-lane-id', [LanesMasterController::class, 'getLaneId']);


    Route::match(['get', 'post'], '/rateoffering/index', [RateOfferingController::class, 'index']);
    Route::match(['get', 'post'], '/rateoffering/add', [RateOfferingController::class, 'add']);
    Route::post('/rateoffering/conversionlist', [RateOfferingController::class, 'conversionlist']);
    Route::post('/rateoffering/getrateservicelanes', [RateOfferingController::class, 'getrateservicelanes']);
    Route::post('/rateoffering/getvehiclepflist', [RateOfferingController::class, 'getvehiclepflist']);
    Route::post('/rateoffering/getvendorpflist', [RateOfferingController::class, 'getvendorpflist']);
    Route::post('/rateoffering/getcargopflist', [RateOfferingController::class, 'getcargopflist']);
    Route::match(['get', 'post'], '/rateoffering/edit/{rowId}', [RateOfferingController::class, 'edit']);
    Route::post('/rateoffering/getdocs', [RateOfferingController::class, 'getdocs']);
    Route::post('/rateoffering/getremarktypes', [RateOfferingController::class, 'getremarktypes']);
    Route::post('/rateoffering/getservices', [RateOfferingController::class, 'getservices']);
    Route::post('/rateoffering/getvasname', [RateOfferingController::class, 'getvasname']);
    Route::post('/rateoffering/saveservice/{id?}', [RateOfferingController::class, 'saveservice']);
    Route::post('/rateoffering/deleteservice', [RateOfferingController::class, 'deleteservice']);
    Route::post('/rateoffering/getdoctypename', [RateOfferingController::class, 'getdoctypename']);
    Route::post('/rateoffering/savedoc/{id?}', [RateOfferingController::class, 'savedoc']);
    Route::get('/rateoffering/view/{id}', [RateOfferingController::class, 'view']);
    Route::post('/rateoffering/deleterateoffering', [RateOfferingController::class, 'deleterateoffering']);

    // Rate-Record
    Route::match(['get', 'post'], '/raterecord/index', [RateRecordController::class, 'index']);
    Route::match(['get', 'post'], '/raterecord/add', [RateRecordController::class, 'add']);
    Route::post('/raterecord/getrefval', [RateRecordController::class, 'getrefval']);
    Route::post('/raterecord/conditionssave/{id?}', [RateRecordController::class, 'conditionssave']);
    Route::post('/raterecord/showcondetails', [RateRecordController::class, 'showcondetails']);
    Route::post('/raterecord/deletecondetails', [RateRecordController::class, 'deletecondetails']);

    // Billing
    Route::get('/billing', [BillingController::class, 'index']);
    Route::post('/billing/add', [BillingController::class, 'newBill']);
    Route::post('/billing/viewRoleTypeList', [BillingController::class, 'viewRoleTypeList']);
    Route::post('/billing/getPartnerDetailsById', [BillingController::class, 'getPartnerDetailsById']);
    Route::post('/billing/billsfilter', [BillingController::class, 'billsfilter']);
    Route::post('/billing/insertbilldata', [BillingController::class, 'insertbilldata']);
    Route::post('/billing/getchargeswithorder', [BillingController::class, 'getchargeswithorder']);
    Route::post('/billing/getcharges', [BillingController::class, 'getcharges']);
    Route::get('/billing/edit/{id}', [BillingController::class, 'editbill']);
    Route::post('/billing/update', [BillingController::class, 'updatebill']);
    Route::get('/billing/view/{id}', [BillingController::class, 'viewbill']);
    Route::delete('/billing/delete/{id}', [BillingController::class, 'deletebill']);

});

Route::get('/redis/set', [RedisExampleController::class, 'set']);
Route::get('/redis/get', [RedisExampleController::class, 'get']);

 // Vehicle Profile
    Route::get('/vehicleprofileindex', [VehicleProfileController::class, 'index']);
    Route::get('/addvehicleprofile', [VehicleProfileController::class, 'add']);
    Route::post('/savevehicleprofile', [VehicleProfileController::class, 'save']);
    Route::post('/updatevehicleprofile/{id}', [VehicleProfileController::class, 'update']);
    Route::get('/editvehicleprofile/{id}', [VehicleProfileController::class, 'edit']);
    Route::get('/viewvehicleprofile/{id}', [VehicleProfileController::class, 'view']);
    Route::delete('/deletevehicleprofile/{id}', [VehicleProfileController::class, 'delete']);
    Route::get('/gettrucktypes', [VehicleProfileController::class, 'getTruckTypes']);
    Route::get('/getprofileids', [VehicleProfileController::class, 'getProfileIds']);
    Route::get('/checkvehiclename', [VehicleProfileController::class, 'checkVehicleName']);
    Route::get('/getvehicleprofileid', [VehicleProfileController::class, 'getVehicleProfileId']);
    Route::post('/savevehicletype/{id?}', [VehicleProfileController::class, 'saveVehicleType']);
    Route::get('/showprofilelist', [VehicleProfileController::class, 'showProfileList']);
    Route::delete('/deleteprofiledetails', [VehicleProfileController::class, 'deleteProfileDetails']);
    Route::get('/getprofilelist', [VehicleProfileController::class, 'getProfileList']);

     // Customer Profile
    Route::get('/customer-profiles', [CustomerProfileController::class, 'index'])->name('customer-profiles.index');
    Route::get('/customer-profiles/create', [CustomerProfileController::class, 'create'])->name('customer-profiles.create');
    Route::post('/customer-profiles', [CustomerProfileController::class, 'store'])->name('customer-profiles.store');
    Route::get('/customer-profiles/show/{id}', [CustomerProfileController::class, 'show'])->name('customer-profiles.show');
    Route::get('/customer-profiles/edit/{id}', [CustomerProfileController::class, 'edit'])->name('customer-profiles.edit');
    Route::put('/customer-profiles/{id}', [CustomerProfileController::class, 'update'])->name('customer-profiles.update');
    Route::delete('/customer-profiles/{id}', [CustomerProfileController::class, 'destroy'])->name('customer-profiles.destroy');
    
    // Customer Profile additional routes
    Route::post('/customer-profiles/get-cust-profile-id', [CustomerProfileController::class, 'getCustProfileId']);
    Route::post('/customer-profiles/get-party-master', [CustomerProfileController::class, 'getPartyMaster']);
    Route::post('/customer-profiles/save-customer/{id?}', [CustomerProfileController::class, 'saveCustomer']);
    Route::post('/customer-profiles/show-profile-list', [CustomerProfileController::class, 'showProfileList']);
    Route::post('/customer-profiles/get-profile-list', [CustomerProfileController::class, 'getProfileList']);
    Route::post('/customer-profiles/delete-profile-details', [CustomerProfileController::class, 'deleteProfileDetails']);
    Route::get('/customer-profiles/get-cust-info/{id}', [CustomerProfileController::class, 'getCustInfo']);
    Route::post('/customer-profiles/check-name', [CustomerProfileController::class, 'checkName']);

    // Vendor Profile
    Route::get('/vendor-profiles', [VendorProfileController::class, 'index'])->name('vendor-profiles.index');
    Route::get('/vendor-profiles/create', [VendorProfileController::class, 'create'])->name('vendor-profiles.create');
    Route::post('/vendor-profiles', [VendorProfileController::class, 'store'])->name('vendor-profiles.store');
    Route::get('/vendor-profiles/show/{id}', [VendorProfileController::class, 'show'])->name('vendor-profiles.show');
    Route::get('/vendor-profiles/edit/{id}', [VendorProfileController::class, 'edit'])->name('vendor-profiles.edit');
    Route::put('/vendor-profiles/{id}', [VendorProfileController::class, 'update'])->name('vendor-profiles.update');
    Route::delete('/vendor-profiles/{id}', [VendorProfileController::class, 'destroy'])->name('vendor-profiles.destroy');
    
    // Vendor Profile additional routes
    Route::post('/vendor-profiles/get-vendor-profile-id', [VendorProfileController::class, 'getVendorProfileId']);
    Route::post('/vendor-profiles/get-party-master', [VendorProfileController::class, 'getPartyMaster']);
    Route::post('/vendor-profiles/save-vendor/{id?}', [VendorProfileController::class, 'saveVendor']);
    Route::post('/vendor-profiles/show-profile-list', [VendorProfileController::class, 'showProfileList']);
    Route::post('/vendor-profiles/get-profile-list', [VendorProfileController::class, 'getProfileList']);
    Route::post('/vendor-profiles/delete-profile-details', [VendorProfileController::class, 'deleteProfileDetails']);
    Route::get('/vendor-profiles/get-vendor-info/{id}', [VendorProfileController::class, 'getVendorInfo']);
    Route::post('/vendor-profiles/check-vendor-name', [VendorProfileController::class, 'checkVendorName']);

    // Notification Management
    Route::get('/notification-management', [NotifymgmtController::class, 'index'])->name('notification-management.index');
    Route::get('/notification-management/create', [NotifymgmtController::class, 'create'])->name('notification-management.create');
    Route::post('/notification-management', [NotifymgmtController::class, 'store'])->name('notification-management.store');
    Route::get('/notification-management/show/{id}', [NotifymgmtController::class, 'show'])->name('notification-management.show');
    Route::get('/notification-management/edit/{id}', [NotifymgmtController::class, 'edit'])->name('notification-management.edit');
    Route::put('/notification-management/{id}', [NotifymgmtController::class, 'update'])->name('notification-management.update');
    Route::delete('/notification-management/{id}', [NotifymgmtController::class, 'destroy'])->name('notification-management.destroy');
    
    // Notification Management additional routes
    Route::post('/notification-management/get-notify-data', [NotifymgmtController::class, 'getNotifyData']);
    Route::post('/notification-management/check-combination', [NotifymgmtController::class, 'checkCombination']);
    Route::post('/notification-management/get-customer-notify-data', [NotifymgmtController::class, 'getCustomerNotifyData']);
    Route::post('/notification-management/get-notification-ids', [NotifymgmtController::class, 'getNotificationIds']);
    Route::post('/notification-management/get-organizations', [NotifymgmtController::class, 'getOrganizations']);
    Route::post('/notification-management/get-customers', [NotifymgmtController::class, 'getCustomers']);
    Route::post('/notification-management/get-customers-list', [NotifymgmtController::class, 'getCustomersList']);
    Route::post('/notification-management/get-business-entities', [NotifymgmtController::class, 'getBusinessEntities']);
    Route::get('/notification-management/test', [NotifymgmtController::class, 'test']);
    Route::get('/notification-management/tpl', [NotifymgmtController::class, 'tpl']);
    Route::get('/notification-management/test-mail', [NotifymgmtController::class, 'testMail']);

    Route::middleware('auth:api')->get('/menu', [UserController::class, 'menu']);

// Route::get('/orderslist/{id?}', [OrderController::class, 'index']);
Route::get('/orderslist', [OrderController::class, 'index']);
Route::get('/orders/new', [OrderController::class, 'neworder'])->name('orders.new');
Route::post('/orders/insertorder', [OrderController::class, 'insertorder'])->name('orders.store');