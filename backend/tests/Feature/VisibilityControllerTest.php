<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Shipment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Schema;
use Laravel\Passport\Passport;
use Tests\TestCase;

class VisibilityControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Run passport install to set up keys for testing
        $this->artisan('passport:install', ['--force' => true]);

        // Create only the tables we need for testing
        $this->createTestTables();
    }

    /**
     * Create minimal tables needed for testing
     */
    private function createTestTables(): void
    {
        // Create sx_users table
        Schema::create('sx_users', function ($table) {
            $table->increments('id');
            $table->string('employee_id')->nullable();
            $table->string('employee_name')->nullable();
            $table->string('username');
            $table->string('password');
            $table->date('effective_fromdate')->nullable();
            $table->date('effective_enddate')->nullable();
            $table->string('contact_num')->nullable();
            $table->integer('theme_id')->nullable();
            $table->string('currency')->nullable();
            $table->string('date_zone')->nullable();
            $table->string('number_format')->nullable();
            $table->integer('created_by')->default(0);
            $table->integer('updated_by')->default(0);
            $table->timestamp('deleted_at')->nullable();
            $table->string('languages')->nullable();
            $table->string('date_format')->nullable();
            $table->integer('default_org_id')->nullable();
            $table->string('country_code')->nullable();
            $table->string('default_currency')->nullable();
            $table->string('lat')->nullable();
            $table->string('lng')->nullable();
            $table->smallInteger('status')->default(1);
            $table->string('emailid')->nullable();
            $table->string('refresh_token')->nullable();
            $table->string('geolocation')->nullable();
            $table->text('logo')->nullable();
            $table->timestamps();
        });

        // Create shipment table
        Schema::create('shipment', function ($table) {
            $table->increments('id');
            $table->integer('user_id')->nullable()->default(0);
            $table->timestamp('stime')->nullable();
            $table->timestamp('etime')->nullable();
            $table->string('splace')->nullable();
            $table->decimal('slat', 10, 8)->nullable();
            $table->decimal('slng', 11, 8)->nullable();
            $table->string('eplace')->nullable();
            $table->decimal('elat', 10, 8)->nullable();
            $table->decimal('elng', 11, 8)->nullable();
            $table->string('scity')->nullable();
            $table->string('dcity')->nullable();
            $table->integer('org_id')->nullable()->default(0);
            $table->integer('be_value')->nullable()->default(0);
            $table->tinyInteger('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Test shipment endpoint returns success with authenticated user and org_id from user
     */
    public function test_shipment_returns_success_with_authenticated_user()
    {
        // Create a user with org_id
        $user = User::factory()->create([
            'default_org_id' => 1,
            'status' => 1
        ]);

        // Create some test shipments for this org
        $shipments = Shipment::factory()->count(3)->create([
            'org_id' => 1,
            'user_id' => $user->id,
            'status' => 1
        ]);

        // Authenticate the user
        Passport::actingAs($user);

        // Make the request
        $response = $this->getJson('/api/shipments');

        // Assert the response
        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Shipments retrieved successfully.',
                 ])
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         '*' => [
                             'id',
                             'org_id',
                             'user_id',
                             'status',
                             'created_at',
                             'updated_at'
                         ]
                     ]
                 ]);

        // Assert we got the correct number of shipments
        $this->assertCount(3, $response->json('data'));
    }

    /**
     * Test shipment endpoint with org_id parameter
     */
    public function test_shipment_returns_success_with_org_id_parameter()
    {
        // Create a user
        $user = User::factory()->create([
            'default_org_id' => 1,
            'status' => 1
        ]);

        // Create shipments for different orgs
        Shipment::factory()->count(2)->create(['org_id' => 1]);
        Shipment::factory()->count(3)->create(['org_id' => 2]);

        // Authenticate the user
        Passport::actingAs($user);

        // Make the request with specific org_id
        $response = $this->getJson('/api/shipments?org_id=2');

        // Assert the response
        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Shipments retrieved successfully.',
                 ]);

        // Assert we got shipments only for org_id 2
        $this->assertCount(3, $response->json('data'));
        foreach ($response->json('data') as $shipment) {
            $this->assertEquals(2, $shipment['org_id']);
        }
    }

    /**
     * Test shipment endpoint with user_id filter
     */
    public function test_shipment_returns_filtered_results_with_user_id()
    {
        // Create a user
        $user = User::factory()->create([
            'default_org_id' => 1,
            'status' => 1
        ]);

        // Create shipments for different users in same org
        Shipment::factory()->count(2)->create([
            'org_id' => 1,
            'user_id' => 100
        ]);
        Shipment::factory()->count(3)->create([
            'org_id' => 1,
            'user_id' => 200
        ]);

        // Authenticate the user
        Passport::actingAs($user);

        // Make the request with user_id filter
        $response = $this->getJson('/api/shipments?user_id=200');

        // Assert the response
        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Shipments retrieved successfully.',
                 ]);

        // Assert we got shipments only for user_id 200
        $this->assertCount(3, $response->json('data'));
        foreach ($response->json('data') as $shipment) {
            $this->assertEquals(200, $shipment['user_id']);
        }
    }

    /**
     * Test shipment endpoint returns error when no org_id available
     */
    public function test_shipment_returns_error_when_no_org_id_available()
    {
        // Create a user without org_id
        $user = User::factory()->create([
            'default_org_id' => null,
            'status' => 1
        ]);

        // Authenticate the user
        Passport::actingAs($user);

        // Make the request without org_id parameter
        $response = $this->getJson('/api/shipments');

        // Assert the response
        $response->assertStatus(422)
                 ->assertJson([
                     'status' => 'error',
                     'message' => 'Organization ID is required.',
                 ]);
    }

    /**
     * Test shipment endpoint without authentication
     */
    public function test_shipment_requires_authentication()
    {
        // Make the request without authentication
        $response = $this->getJson('/api/shipments');

        // Assert the response is unauthorized
        $response->assertStatus(401);
    }

    /**
     * Test shipment endpoint returns empty data when no shipments exist
     */
    public function test_shipment_returns_empty_data_when_no_shipments_exist()
    {
        // Create a user with org_id
        $user = User::factory()->create([
            'default_org_id' => 1,
            'status' => 1
        ]);

        // Authenticate the user (no shipments created)
        Passport::actingAs($user);

        // Make the request
        $response = $this->getJson('/api/shipments');

        // Assert the response
        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Shipments retrieved successfully.',
                     'data' => []
                 ]);
    }

    /**
     * Test shipment endpoint with both org_id and user_id filters
     */
    public function test_shipment_returns_filtered_results_with_both_org_id_and_user_id()
    {
        // Create a user
        $user = User::factory()->create([
            'default_org_id' => 1,
            'status' => 1
        ]);

        // Create shipments for different combinations
        Shipment::factory()->count(2)->create([
            'org_id' => 1,
            'user_id' => 100
        ]);
        Shipment::factory()->count(3)->create([
            'org_id' => 2,
            'user_id' => 100
        ]);
        Shipment::factory()->count(1)->create([
            'org_id' => 2,
            'user_id' => 200
        ]);

        // Authenticate the user
        Passport::actingAs($user);

        // Make the request with both filters
        $response = $this->getJson('/api/shipments?org_id=2&user_id=100');

        // Assert the response
        $response->assertStatus(200)
                 ->assertJson([
                     'status' => 'success',
                     'message' => 'Shipments retrieved successfully.',
                 ]);

        // Assert we got shipments only for org_id 2 and user_id 100
        $this->assertCount(3, $response->json('data'));
        foreach ($response->json('data') as $shipment) {
            $this->assertEquals(2, $shipment['org_id']);
            $this->assertEquals(100, $shipment['user_id']);
        }
    }

    /**
     * Test that response data structure matches expected format
     */
    public function test_shipment_response_data_structure()
    {
        // Create a user with org_id
        $user = User::factory()->create([
            'default_org_id' => 1,
            'status' => 1
        ]);

        // Create a shipment with specific data
        $shipment = Shipment::factory()->create([
            'org_id' => 1,
            'user_id' => $user->id,
            'status' => 1
        ]);

        // Authenticate the user
        Passport::actingAs($user);

        // Make the request
        $response = $this->getJson('/api/shipments');

        // Assert the response structure and data
        $response->assertStatus(200)
                 ->assertJsonStructure([
                     'status',
                     'message',
                     'data' => [
                         '*' => [
                             'id',
                             'org_id',
                             'user_id',
                             'status',
                             'created_at',
                             'updated_at'
                         ]
                     ]
                 ]);

        // Verify the actual data matches what we created
        $responseData = $response->json('data')[0];
        $this->assertEquals($shipment->id, $responseData['id']);
        $this->assertEquals($shipment->org_id, $responseData['org_id']);
        $this->assertEquals($shipment->user_id, $responseData['user_id']);
        $this->assertEquals($shipment->status, $responseData['status']);
    }
}
