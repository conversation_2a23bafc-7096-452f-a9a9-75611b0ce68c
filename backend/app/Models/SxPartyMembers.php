<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SxPartyMembers extends Model
{
    use SoftDeletes;

    protected $table = 'sx_party_members';
    protected $primaryKey = 'id';
    public $timestamps = true;
    protected $dates = ['deleted_at'];

    protected $fillable = [
        'name',
        'email',
        'code',
        'customeridentifier',
        'org_id',
        'be_value',
        'user_id',
        'party_type',
        'mobile',
        'street',
        'country',
        'state',
        'pincode',
        'latitude',
        'longitude',
        'party_id',
        'status',
        'city',
        'fax',
        'img',
        'customer_code',
        'parent_id',
        'location_id',
        'address',
        'division_name',
        'street_2',
        'street_3',
        'house_number',
        'building',
        'extension',
        'homepage',
        'category_type',
        'category_id',
        'time_zone_city',
        'time_zone_code',
        'time_zone_name',
        'acon_debitor_code',
        'password',
        'offering_type',
        'carrier_grade',
        'autoaccept',
        'cargo_limit',
        'limit_amount',
        'currency',
        'party_types',
        'location_reference_name',
        'logo',
        'credit_limit_amount',
        'utilized_amount',
        'credit_utilized_amount',
        'vat_reg_no',
        'tax_payer_no',
        'contract_no',
        'contract_date',
        'ats_status',
        'additional_info',
        'group_email_ids',
        'max_capacity',
        'rating',
        'cost_efficiency',
        'region',
        'certifications',
        'geo_fence_radius',
    ];

    public function partyType()
    {
        return $this->belongsTo(SxPartyTypes::class, 'party_type', 'id');
    }

    public static function getRoleTypeList(int $userId, ?int $orgId, string $type): array
    {
        $query = self::select([
            'sx_party_members.id',
            'sx_party_members.name',
            'sx_party_members.email',
            'sx_party_members.customeridentifier',
            'sx_party_members.org_id',
            'sx_party_members.be_value',
        ])
            ->join('sx_party_types', 'sx_party_types.id', '=', 'sx_party_members.party_type')
            ->where('sx_party_types.type_name', $type);

        $query->where('sx_party_members.org_id', $orgId);

        return $query->get()->toArray();
    }

    public static function getPartnerDetailsById(string $customerIdentifier): ?array
    {
        $result = self::where('customeridentifier', $customerIdentifier)
            ->select('customeridentifier')
            ->orderBy('id', 'DESC')
            ->first();

        return $result ? $result->toArray() : null;
    }

    public static function getCustomerPopupList(int $userId)
    {
        return self::select('id', 'name', 'phone', 'email_id', 'code', 'street', 'pincode')
            ->where('user_id', $userId)
            ->where('status', true)
            ->orderBy('id', 'DESC')
            ->get()
            ->toArray();
    }
}
