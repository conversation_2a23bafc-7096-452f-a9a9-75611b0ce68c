<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class VendorProfileList extends Model
{
    use SoftDeletes;

    protected $table = 'vendor_profile_list';
    protected $primaryKey = 'id';

    public $timestamps = true;

    protected $fillable = [
        'vp_id',
        'profile_id',
        'party_id',
        'status',
    ];

    protected $casts = [
        'status' => 'smallInteger',
    ];

    public function vendor()
    {
        return $this->belongsTo(SxPartyMembers::class, 'party_id', 'id');
    }

    public function profile()
    {
        return $this->belongsTo(VendorProfile::class, 'vp_id', 'id');
    }

    public static function getProfileListData(array $ids)
    {
        return self::select('sx_party_members.name', 'sx_party_members.phone', 'sx_party_members.code', 
                           'sx_party_members.street', 'sx_party_members.pincode', 'sx_party_members.email_id',
                           'vendor_profile_list.id as vendor_profile_list_id',
                           'sx_party_members.id as party_id')
            ->join('sx_party_members', 'sx_party_members.id', '=', 'vendor_profile_list.party_id')
            ->whereIn('vendor_profile_list.id', $ids)
            ->where('vendor_profile_list.status', true)
            ->groupBy('vendor_profile_list.id')
            ->orderBy('vendor_profile_list.id', 'DESC')
            ->get()
            ->toArray();
    }

    public static function getProfileListById($id, $org_id, $be_value)
    {
        return self::select('sx_party_members.name', 'sx_party_members.phone', 'sx_party_members.code', 
                           'sx_party_members.street', 'sx_party_members.pincode', 'sx_party_members.email_id',
                           'vendor_profile_list.id as vendor_profile_list_id',
                           'sx_party_members.id as party_id')
            ->join('sx_party_members', 'sx_party_members.id', '=', 'vendor_profile_list.party_id')
            ->join('vendor_profile', 'vendor_profile.id', '=', 'vendor_profile_list.vp_id')
            ->where('vendor_profile_list.vp_id', $id)
            ->where('vendor_profile.org_id', $org_id)
            ->when($org_id !== '44', function ($query) use ($be_value) {
                $query->where('vendor_profile.be_value', $be_value);
            })
            ->where('vendor_profile_list.status', true)
            ->groupBy('vendor_profile_list.id')
            ->orderBy('vendor_profile_list.id', 'DESC')
            ->get()
            ->toArray();
    }
} 