<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotifyManagement extends Model
{
    protected $table = 'notify_management';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'notification_id',
        'user_id',
        'org_id',
        'be_value',
        'product',
        'inco_term',
        'service',
        'order_type',
        'from_country',
        'to_country',
        'party_id',
        'partycontact_id',
        'all_note',
        'customer_id',
        'party_type',
        'party_type_id',
        'booking_create',
        'booking_edit',
        'booking_delete',
        'trip_create',
        'trip_edit',
        'trip_delete',
        'driver_accept',
        'route_deviate',
        'speed',
        'temperature',
        'pickup_note',
        'delivery_note',
        'pod_note',
        'sms_note',
        'email_note',
        'whatsapp_note',
        'createdby',
        'updatedby',
        'status',
    ];


    /**
     * Get the customer associated with the notification.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(SxPartyMembers::class, 'customer_id', 'id');
    }

    /**
     * Get the party associated with the notification.
     */
    public function party(): BelongsTo
    {
        return $this->belongsTo(SxPartyMembers::class, 'party_id', 'id');
    }

    /**
     * Get the department associated with the notification.
     * Note: Department relationship is disabled due to column mismatch.
     */
    /*
    public function department(): BelongsTo
    {
        return $this->belongsTo(DepartmentMaster::class, 'user_id', 'user_id');
    }
    */

    /**
     * Scope to filter by user and organization
     */
    public function scopeByUserAndOrg($query, $user_id, $org_id, $be_value = null)
    {
        $query->where('user_id', $user_id)
              ->where('org_id', $org_id);
        
        if ($be_value !== null) {
            $query->where('be_value', $be_value);
        }
        
        return $query;
    }

    /**
     * Get notification data with filters
     */
    public static function getNotifyData($where = [])
    {
        $query = self::query();
        
        foreach ($where as $key => $value) {
            if ($value !== '' && $value !== null) {
                $query->where($key, $value);
            }
        }
        
        return $query->get();
    }

    /**
     * Get latest notification ID
     */
    public static function getLatestNotifyId()
    {
        $latest = self::orderBy('id', 'desc')->first();
        return $latest ? $latest->notification_id : 'NOTIFY000';
    }

    /**
     * Get notification IDs for autocomplete
     */
    public static function getNotificationIds($term)
    {
        return self::where('notification_id', 'like', '%' . $term . '%')
                  ->where('status', 1)
                  ->pluck('notification_id')
                  ->toArray();
    }

    /**
     * Get countries for dropdown
     */
    public static function getCountries()
    {
        return CountryMaster::where('status', 1)
                           ->orderBy('country_name')
                           ->get(['id', 'country_name', 'country_code']);
    }

    /**
     * Get customer notification data
     */
    public static function getCustomerNotifyData($where = [])
    {
        $query = self::join('sx_party_members as pm', 'notify_management.party_id', '=', 'pm.id')
                     ->select('pm.*', 'notify_management.*');
        
        foreach ($where as $key => $value) {
            if ($value !== '' && $value !== null) {
                $query->where($key, $value);
            }
        }
        
        return $query->get();
    }

    /**
     * Get customers for autocomplete
     */
    public static function getCustomers($term)
    {
        return SxPartyMembers::where(function($query) use ($term) {
                        $query->where('name', 'like', '%' . $term . '%')
                              ->orWhere('code', 'like', '%' . $term . '%');
                     })
                     ->where('status', 1)
                     ->select('id', 'name', 'code')
                     ->get();
    }
} 