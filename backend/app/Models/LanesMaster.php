<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LanesMaster extends Model
{
    protected $table = 'lanes_master';
    protected $primaryKey = 'id';
    public $timestamps = true;


    protected $fillable = [
        'lane_id',
        'lane_name',
        'source_geo',
        'source',
        'destination_geo',
        'destination',
        'org_id',
        'user_id',
        'status',
        'source_country',
        'destination_country',
        'service_id',
        'be_value',
    ];

    protected $casts = [
        'status' => 'integer',
    ];

    public function scopeGetLanes(array $where): array
    {
        return $this->select(['id', 'lane_id', 'lane_name'])
            ->where($where)
            ->get()
            ->toArray();
    }

    public function scopeGetLaneById(int $id): ?array
    {
        $lane = $this->select(['id', 'lane_id', 'lane_name', 'source_geo', 'source', 'destination_geo', 'destination'])
            ->where('id', $id)
            ->first();

        return $lane ? $lane->toArray() : null;
    }

    public function scopeGetLaneByDetails(array $data): ?array
    {
        $lane = $this->select('id')
            ->where([
                'source_geo' => $data['source_geo'],
                'source' => $data['source'],
                'destination_geo' => $data['destination_geo'],
                'destination' => $data['destination'],
                'user_id' => $data['user_id'],
                'lane_name' => $data['lane_name'],
                'org_id' => $data['org_id'],
                'status' => 1,
            ])
            ->first();

        return $lane ? $lane->toArray() : null;
    }

    public function scopeGenerateLaneId(?int $orgId): string
    {
        $year = date('y');
        $week = date('W');
        $countryCode = $orgId ? strtoupper(substr((string) $orgId, 0, 2)) : 'XX';

        $lastLane = $this->select('lane_id')
            ->orderBy('id', 'DESC')
            ->first();

        if ($lastLane) {
            $lastLaneId = $lastLane->lane_id;
            $previousWeekNumber = substr($lastLaneId, 6, 2);
            $sequence = (int) ltrim(substr($lastLaneId, 8), '0');

            if ($previousWeekNumber < $week) {
                $sequence = '0001';
            } else {
                $sequence = str_pad($sequence + 1, 4, '0', STR_PAD_LEFT);
            }

            $newLaneId = "LN{$countryCode}{$year}{$week}{$sequence}";

            while ($this->where('lane_id', $newLaneId)->exists()) {
                $sequence = (int) $sequence + 1;
                $sequence = str_pad($sequence, 4, '0', STR_PAD_LEFT);
                $newLaneId = "LN{$countryCode}{$year}{$week}{$sequence}";
            }
        } else {
            $newLaneId = "LN{$countryCode}{$year}{$week}0001";
        }

        return $newLaneId;
    }

    public function scopeInsertLane(array $data): int
    {
        $lane = $this->create($data);
        return $lane->id;
    }

    public function scopeUpdateLane(int $id, array $data, ?int $orgId): bool
    {
        return $this->where(['id' => $id, 'org_id' => $orgId])
            ->update($data);
    }

    public function scopeGetActiveLanes(?int $orgId): array
    {
        return $this->select(['id', 'lane_id', 'lane_name'])
            ->where(['status' => 1, 'org_id' => $orgId])
            ->get()
            ->toArray();
    }

    public function scopeSourceGeo()
    {
        return $this->belongsTo(GeoMaster::class, 'source_geo', 'id');
    }

    public function scopeDestinationGeo()
    {
        return $this->belongsTo(GeoMaster::class, 'destination_geo', 'id');
    }

    public function scopeGetSearchData($query, array $likeConditions, array $whereConditions)
    {
        $query->select(['tl.*', 'gm.name as source_geo_name', 'gm1.name as destination_geo_name'])
            ->from('lanes_master as tl')
            ->join('geo_master as gm', 'tl.source_geo', '=', 'gm.id')
            ->join('geo_master as gm1', 'tl.destination_geo', '=', 'gm1.id');

        foreach ($likeConditions as $column => $value) {
            $query->where($column, 'ILIKE', "%{$value}%");
        }

        foreach ($whereConditions as $column => $value) {
            if ($value === null) {
                $query->whereNull($column);
            } else {
                $query->where($column, '=', $value);
            }
        }

        return $query->orderBy('tl.id', 'DESC')->get();
    }

    public function scopeUserList($query)
    {
        return $query->from('sx_users')
            ->select(['name', 'id'])
            ->groupBy('name')
            ->get();
    }

    public function scopeEditData($query, int $id)
    {
        return $query->select(['tl.*', 'gm.name as source_geo_name', 'gm1.name as destination_geo_name'])
            ->from('lanes_master as tl')
            ->join('geo_master as gm', 'tl.source_geo', '=', 'gm.id')
            ->join('geo_master as gm1', 'tl.destination_geo', '=', 'gm1.id')
            ->where('tl.id', $id)
            ->first();
    }

    public function scopeViewData($query, int $id)
    {
        return $query->select(['tl.*', 'gm.name as source_geo_name', 'gm1.name as destination_geo_name'])
            ->from('lanes_master as tl')
            ->join('geo_master as gm', 'tl.source_geo', '=', 'gm.id')
            ->join('geo_master as gm1', 'tl.destination_geo', '=', 'gm1.id')
            ->where('tl.id', $id)
            ->first();
    }

    public function scopeSelectList($query, string $select, string $table, array $whereConditions, string $group)
    {
        return $query->from($table)
            ->selectRaw($select)
            ->where($whereConditions)
            ->groupBy($group)
            ->get();
    }
}