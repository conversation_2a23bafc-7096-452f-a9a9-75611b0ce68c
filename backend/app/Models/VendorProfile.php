<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VendorProfile extends Model
{
    protected $table = 'vendor_profile';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'vend_profile_id',
        'name',
        'description',
        'org_id',
        'be_value',
        'user_id',
        'status',
    ];

    public function getProfileById(string $vendProfileId): ?array
    {
        $profile = $this->select('id')
            ->where('vend_profile_id', $vendProfileId)
            ->first();

        return $profile ? $profile->toArray() : null;
    }

    public function getVendorProfileList(array $where, ?string $term = null): array
    {
        $query = $this->select(['id', 'vend_profile_id', 'name', 'org_id', 'be_value'])
            ->where($where)
            ->where('status', 1);

        if ($term) {
            $query->where('vend_profile_id', 'like', '%' . $term . '%');
        }

        return $query->orderBy('id', 'DESC')
            ->get()
            ->toArray();
    }

    public function scopeFilter($query, array $where, array $like = [])
    {
        return $query->select('id', 'vend_profile_id', 'name', 'description', 'user_id', 'org_id', 'be_value', 'status')
            ->when(!empty($like), function ($query) use ($like) {
                foreach ($like as $key => $value) {
                    $query->where($key, 'like', "%{$value}%");
                }
            })
            ->where($where)
            ->orderBy('id', 'DESC');
    }

    public function scopeByOrgAndBe($query, $id, $org_id, $be_value)
    {
        return $query->select('id', 'vend_profile_id', 'name', 'description', 'user_id', 'org_id', 'be_value', 'status')
            ->where('id', $id)
            ->where('org_id', $org_id)
            ->when($org_id !== '44', function ($query) use ($be_value) {
                $query->where('be_value', $be_value);
            })
            ->first();
    }

    public function vendorProfileLists()
    {
        return $this->hasMany(VendorProfileList::class, 'vp_id', 'id');
    }
}
