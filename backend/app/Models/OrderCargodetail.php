<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OrderCargodetail extends Model
{
    protected $table = 'order_cargodetails';
    protected $fillable = [
        'order_id',
        'be_value',
        'user_id',
        'cargo_id',
        'handling_unit',
        'length',
        'width',
        'height',
        'weight',
        'second_weight',
        'volumetric_weight',
        'volweight_uom',
        'ldm',
        'volume',
        'second_volume',
        'quantity',
        'scanned_quantity',
        'quantity_type',
        'cargo_content',
        'buyer_part',
        'linebp_num1',
        'linecontainer_no',
        'stop_detail_id',
        'label_number',
        'qr_code',
        'pallet_name',
        'pallet_close',
        'status',
        'createdon',
        'reference_order_num',
        'load_plan_connote',
        'marks_numbers',
        'updatedon',
        'dimensions',
        'material',
        'information',
        'abnormal_load',
    ];


}