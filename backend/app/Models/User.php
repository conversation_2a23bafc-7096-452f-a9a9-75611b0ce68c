<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Passport\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'sx_users';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'employee_id',
        'employee_name',
        'username',
        'password',
        'effective_fromdate',
        'effective_enddate',
        'contact_num',
        'theme_id',
        'currency',
        'date_zone',
        'number_format',
        'created_by',
        'updated_by',
        'deleted_at',
        'languages',
        'date_format',
        'default_org_id',
        'country_code',
        'default_currency',
        'refresh_token',
        'lat',
        'lng',
        'status',
        'emailid',
        'geolocation',
        'logo',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'effective_fromdate' => 'date',
        'effective_enddate' => 'date',
        'deleted_at' => 'datetime',
        'geolocation' => 'string', // You may need a custom cast for POINT type
    ];

    /**
     * Get the email address for authentication.
     * If you want to use 'username' or 'emailid' for login, update auth config and this method.
     */
    public function getAuthIdentifierName()
    {
        return 'id';
    }

    /**
     * Automatically hash the password when set.
     */
    public function setPasswordAttribute($value)
    {
        // Only hash if not already hashed
        if (!empty($value) && strlen($value) !== 60) {
            $this->attributes['password'] = bcrypt($value);
        } else {
            $this->attributes['password'] = $value;
        }
    }
}
