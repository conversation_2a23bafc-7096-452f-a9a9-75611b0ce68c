<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Shipment extends Model
{
    use HasFactory;

    protected $table = 'shipment';
    protected $primaryKey = 'id';
    protected $fillable = [
        'user_id', 'stime', 'etime', 'splace', 'slat', 'slng', 'eplace', 'elat', 'elng',
        'scity', 'dcity', 'zone_id', 'empshift_start', 'empshift_end', 'trip_type',
        'startdate', 'enddate', 'shipment_name', 'shipmentid', 'shipment_id', 'customer_id',
        'transport_mode', 'vendor_id', 'carrier_type', 'txnid', 'weight', 'volume', 'units',
        'domainname', 'schedule_date', 'vehicle_type', 'org_id', 'be_value', 'dept_id',
        'border_type', 'carrier_instructions', 'status',
        'shift_leg_id', 'origin_id', 'destination_id', 'interchange_control_reference',
        'weight_capacity', 'volume_capacity', 'additional_conditions', 'temperature_regime',
        'time_for_loading_penality_rate', 'is_carrier_notified', 'aborted', 'routetemplate_id',
        'template_leg_id', 'sgeolocation', 'egeolocation', 'shipment_order', 'no_of_vehicles',
        'dimension_type', 'truck_start_time', 'max_distance', 'capacity', 'truck_starttime',
        'cost_per_hour', 'cost_per_kilometer', 'traffic'
    ];

    protected $casts = [
        'status' => 'smallInteger',
        'is_carrier_notified' => 'boolean',
        'aborted' => 'boolean',
        'stime' => 'datetime',
        'etime' => 'datetime',
        'empshift_start' => 'datetime',
        'empshift_end' => 'datetime',
        'startdate' => 'date',
        'enddate' => 'date',
        'schedule_date' => 'date',
        'truck_start_time' => 'datetime',
        'truck_starttime' => 'datetime',

    ];

    public $timestamps = true;
}
