<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class TransportMode extends Model
{
    protected $table = 'transportmode';
    protected $primaryKey = 'id';
    public $timestamps = true;
    protected $fillable = ['code', 'name', 'user_id', 'org_id', 'be_value', 'status'];

    protected $casts = [
        'status' => 'smallInteger',
    ];

    /**
     * Get a transport mode by ID.
     *
     * @param int $id Transport mode ID
     * @return array|null
     */
    public function getById(int $id): ?array
    {
        $result = $this->select('code')->where('id', $id)->first();

        return $result ? $result->toArray() : null;
    }

    /**
     * Get active transport modes based on org_id, be_value, and optional date filters.
     *
     * @param string $org_id Organization ID (formerly company_code)
     * @param string $be_value Business entity value (formerly branch_code)
     * @param string $order_date Order date for filtering
     * @param string $less_date Comparison date for filtering
     * @return array
     */
    public function getActiveTransportModes(string $org_id, string $be_value, string $order_date = '', string $less_date = ''): array
    {
        $query = $this->select('id', 'code', 'name')->where('status', true);

        if ($order_date !== '' && $less_date !== '' && $order_date < $less_date) {
            $query->where(function ($q) {
                $q->where('org_id', '')
                    ->orWhereNull('org_id');
            })->where(function ($q) {
                $q->where('be_value', '')
                    ->orWhereNull('be_value');
            });
        } else {
            $branch_modes_exist = $this->where('status', true)
                ->where('be_value', $be_value)
                ->where('org_id', $org_id)
                ->exists();

            if ($branch_modes_exist) {
                $query->where(function ($q) use ($org_id, $be_value) {
                    $q->where(function ($q2) use ($org_id, $be_value) {
                        $q2->where('org_id', $org_id)
                            ->where(function ($q3) use ($be_value) {
                                $q3->where('be_value', $be_value)
                                    ->orWhereNull('be_value')
                                    ->orWhere('be_value', '');
                            });
                    })->orWhere(function ($q2) {
                        $q2->where(function ($q3) {
                            $q3->whereNull('org_id')
                                ->orWhere('org_id', '');
                        })->where(function ($q3) {
                            $q3->whereNull('be_value')
                                ->orWhere('be_value', '');
                        });
                    });
                });
            } else {
                $query->where(function ($q) use ($org_id) {
                    $q->where('org_id', $org_id)
                        ->orWhereNull('org_id')
                        ->orWhere('org_id', '');
                })->where(function ($q) {
                    $q->whereNull('be_value')
                        ->orWhere('be_value', '');
                });
            }
        }

        try {
            $results = $query->get()->map(function ($row) {
                return [
                    'id' => $row->id,
                    'code' => $row->code,
                    'name' => $row->name,
                ];
            })->toArray();
        } catch (\Exception $e) {
            Log::error('Database error: ' . $e->getMessage());
            return [];
        }

        return $results;
    }

    public function getTransportMode(string $orderDate = '', string $lessDate = '', ?int $orgId, ?int $beValue): array
    {
        // Check cache
        $cacheKey = "transport_modes_{$orgId}_{$beValue}";
        $cachedData = Cache::get($cacheKey);
        if (!empty($cachedData)) {
            return $cachedData;
        }

        $query = $this->select(['id', 'code', 'name'])->where('status', 1);

        if ($orderDate && $lessDate) {
            if ($orderDate < $lessDate) {
                $query->where(function ($q) {
                    $q->whereNull('org_id')
                        ->orWhere('org_id', '');
                })->where(function ($q) {
                    $q->whereNull('be_value')
                        ->orWhere('be_value', '');
                });
            } else {
                $this->applyCompanyBranchConditions($query, $orgId, $beValue);
            }
        } else {
            $this->applyCompanyBranchConditions($query, $orgId, $beValue);
        }

        $transport = $query->get()->map(function ($res) {
            return [
                'id' => $res->id,
                'code' => $res->code,
                'name' => $res->name,
            ];
        })->toArray();

        Cache::put($cacheKey, $transport, now()->addMinutes(60));

        return $transport;
    }
    private function applyCompanyBranchConditions($query, ?int $orgId, ?int $beValue)
    {
        $branchExists = $this->where([
            'status' => 1,
            'be_value' => $beValue,
            'org_id' => $orgId,
        ])->exists();

        if ($branchExists) {
            $query->where(function ($q) use ($orgId, $beValue) {
                $q->where(['org_id' => $orgId, 'be_value' => $beValue])
                    ->orWhere(function ($subQ) use ($orgId) {
                        $subQ->where('org_id', $orgId)
                            ->whereNull('be_value');
                    })
                    ->orWhere(function ($subQ) {
                        $subQ->whereNull('org_id')
                            ->whereNull('be_value');
                    });
            });
        } else {
            $query->where(function ($q) use ($orgId) {
                $q->where('org_id', $orgId)
                    ->orWhereNull('org_id');
            })->whereNull('be_value');
        }
    }

}
