<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LocationData extends Model
{
    protected $table = 'location_data';
    protected $primaryKey = 'id';
    public $timestamps = false;

    protected $fillable = [
        'location_name',
        'name1',
        'name2',
        'country',
        'lat',
        'lng',
        'source',
    ];

    protected $casts = [
        'lat' => 'string',
        'lng' => 'string',
    ];
}
