<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Revenue extends Model
{
    protected $table = 'reveneus';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'type',
        'order_id',
        'recipient_role',
        'recipient_code',
        'recipient_name',
        'chargeable_weight',
        'debtor_jfr',
        'debitor_time',
        'bu_jfr',
        'invoice_number',
        'credit_note_number',
        'invoice_date',
        'invoice_creation_date',
        'invoice_receivdon_date',
        'amount',
        'accrual_amount',
        'actual_amount',
        'currency',
        'exchange_rate',
        'foreign_currency',
        'accrual_foreign_amount',
        'actual_foreign_amount',
        'invoice_status',
        'bill_id',
        'remarks',
        'parent_id',
        'status',
        'user_id',
        'leg_id',
        'source_created',
        'org_id',
        'be_value',
    ];

    protected $casts = [
        'invoice_status' => 'smallInteger',
        'status' => 'smallInteger',
        'amount' => 'decimal:2',
    ];
}
