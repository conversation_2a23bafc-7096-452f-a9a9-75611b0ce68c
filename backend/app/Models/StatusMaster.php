<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;

class StatusMaster extends Model
{
    protected $table = 'status_master';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'status_name',
        'description',
        'status_type',
        'status_code',
        'customer_id',
        'user_id',
        'org_id',
        'be_value',
        'status',
    ];

    protected $casts = [
        'status' => 'tinyInteger',
    ];

    /**
     * Get the customer associated with the status master.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(SxPartyMembers::class, 'customer_id', 'id');
    }

    /**
     * Get status list with customer information using PostgreSQL queries
     */
    public function GetSearchData($query, array $likeConditions, int $sera = 0)
    {
        if ($sera === 0) {
            $query->where('status', true);
        }

        foreach ($likeConditions as $column => $value) {
            $query->whereRaw("LOWER({$column}) LIKE LOWER(?)", ["%{$value}%"]);
        }

        return $query->get();
    }

    public function SelectList($query, string $select, string $table, array $whereConditions)
    {
        return $query->from($table)
            ->selectRaw($select)
            ->where($whereConditions)
            ->groupBy('name')
            ->get();
    }

    /**
     * Get status list with customer information using PostgreSQL-specific queries
     */
    public function StatusList($query, array $likeConditions, array $whereConditions)
    {
        $query->selectRaw('
            sm.id,
            sm.status_name,
            sm.description,
            sm.status_type,
            sm.status_code,
            sm.customer_id,
            sm.user_id,
            sm.org_id,
            sm.be_value,
            sm.status,
            sm.created_at,
            sm.updated_at,
            m.id as customer_id,
            m.name as customer_name
        ')
        ->from('status_master as sm')
        ->leftJoin('sx_party_members as m', 'sm.customer_id', '=', 'm.id');

        // Apply LIKE conditions using PostgreSQL ILIKE for case-insensitive search
        foreach ($likeConditions as $column => $value) {
            if (!empty($value)) {
                // Handle table aliases in column names
                if (strpos($column, '.') !== false) {
                    $query->whereRaw("LOWER({$column}) LIKE LOWER(?)", ["%{$value}%"]);
                } else {
                    $query->whereRaw("LOWER(sm.{$column}) LIKE LOWER(?)", ["%{$value}%"]);
                }
            }
        }

        // Apply WHERE conditions
        foreach ($whereConditions as $column => $value) {
            if ($value === null) {
                $query->whereNull($column);
            } else {
                $query->where($column, '=', $value);
            }
        }

        return $query->orderByRaw('sm.id DESC NULLS LAST')->get();
    }

    /**
     * Get single status view with customer information using PostgreSQL queries
     */
    public function StatusView($query, array $whereConditions)
    {
        return $query->selectRaw('
            sm.id,
            sm.status_name,
            sm.description,
            sm.status_type,
            sm.status_code,
            sm.customer_id,
            sm.user_id,
            sm.org_id,
            sm.be_value,
            sm.status,
            sm.created_at,
            sm.updated_at,
            m.id as customer_id,
            m.name as customer_name
        ')
        ->from('status_master as sm')
        ->leftJoin('sx_party_members as m', 'sm.customer_id', '=', 'm.id')
        ->where($whereConditions)
        ->first();
    }

    /**
     * Get model ID from status code using PostgreSQL queries
     */
    public function getModelIdFromStatusCode(string $statusCode, ?string $orgId = null, ?string $beValue = null): ?int
    {
        $query = $this->newQuery();
        
        if ($orgId && $beValue) {
            $result = $query->whereRaw('LOWER(status_code) = LOWER(?)', [$statusCode])
                ->where('org_id', $orgId)
                ->where('be_value', $beValue)
                ->where('status', true)
                ->first();
        } else {
            $result = $query->whereRaw('LOWER(status_code) = LOWER(?)', [$statusCode])
                ->whereNull('org_id')
                ->whereNull('be_value')
                ->where('status', true)
                ->first();
        }

        return $result ? $result->id : null;
    }

    /**
     * Get model ID from status name using PostgreSQL queries
     */
    public function getModelIdFromStatusName(string $statusName, ?string $orgId = null, ?string $beValue = null): ?int
    {
        $query = $this->newQuery();
        
        if ($orgId && $beValue) {
            $result = $query->whereRaw('LOWER(status_name) = LOWER(?)', [$statusName])
                ->where('org_id', $orgId)
                ->where('be_value', $beValue)
                ->where('status', true)
                ->first();
        } else {
            $result = $query->whereRaw('LOWER(status_name) = LOWER(?)', [$statusName])
                ->whereNull('org_id')
                ->whereNull('be_value')
                ->where('status', true)
                ->first();
        }

        return $result ? $result->id : null;
    }

    /**
     * Get attribute by ID using PostgreSQL queries
     */
    public function getAttributeById(int $id, string $attribute): ?string
    {
        $result = $this->where('id', $id)->select($attribute)->first();
        return $result ? $result->$attribute : null;
    }

    /**
     * Search status master with PostgreSQL full-text search capabilities
     */
    public function searchStatusMaster(string $searchTerm, array $filters = []): \Illuminate\Database\Eloquent\Collection
    {
        $query = $this->newQuery()
            ->selectRaw('
                sm.id,
                sm.status_name,
                sm.description,
                sm.status_type,
                sm.status_code,
                sm.customer_id,
                sm.user_id,
                sm.org_id,
                sm.be_value,
                sm.status,
                sm.created_at,
                sm.updated_at,
                m.name as customer_name
            ')
            ->from('status_master as sm')
            ->leftJoin('sx_party_members as m', 'sm.customer_id', '=', 'm.id')
            ->where('sm.status', true);

        // Full-text search using PostgreSQL
        if (!empty($searchTerm)) {
            $query->whereRaw('
                LOWER(sm.status_name) LIKE LOWER(?) OR 
                LOWER(sm.status_code) LIKE LOWER(?) OR 
                LOWER(sm.description) LIKE LOWER(?) OR 
                LOWER(m.name) LIKE LOWER(?)
            ', [
                "%{$searchTerm}%",
                "%{$searchTerm}%", 
                "%{$searchTerm}%",
                "%{$searchTerm}%"
            ]);
        }

        // Apply filters
        foreach ($filters as $column => $value) {
            if ($value !== null) {
                $query->where("sm.{$column}", $value);
            }
        }

        return $query->orderByRaw('sm.id DESC NULLS LAST')->get();
    }
} 