<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class VroOrder extends Model
{
    protected $table = 'vro_orders';

    protected $fillable = [
        'org_id',
        'be_value',
        'user_id',
        'order_ids',
        'dimension_type',
        'no_of_vehicles',
        'vehicle_type',
        'carrier_id',
        'traffic_checkbox',
        'status',
        'created_at',
        'updated_at',
        'carrier_instructions',
        'weight_capacity',
        'volume_capacity',
        'additional_conditions',
        'temperature_regime',
        'time_for_loading_penality_rate',
        'truck_starttime',
        'cost_per_hour',
        'cost_per_kilometer',
        'route_data',
        'max_distance',
        'max_truck_distance',
    ];

}