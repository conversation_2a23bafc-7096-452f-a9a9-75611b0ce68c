<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CountryMaster extends Model
{
    protected $table = 'country_master';
    protected $primaryKey = 'id';
    public $timestamps = true;

    protected $fillable = [
        'country_name',
        'user_id',
        'org_id',
        'be_value',
        'status',
        'currency',
    ];

    protected $casts = [
        'status' => 'smallInteger',
    ];

    public function scopeSelectList($query, string $select, string $table, array $whereConditions, string $group)
    {
        return $query->from($table)
            ->selectRaw($select)
            ->where($whereConditions)
            ->groupBy($group)
            ->get();
    }

    public static function getCurrencies(): array
    {
        return self::select('currency')
            ->where('status', 1)
            ->distinct()
            ->orderBy('currency')
            ->pluck('currency')
            ->toArray();
    }
}
