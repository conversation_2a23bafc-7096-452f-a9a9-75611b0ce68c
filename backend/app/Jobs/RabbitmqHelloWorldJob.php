<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class RabbitmqHelloWorldJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public string $content;

    public function __construct($content = "Hello from RabbitMQ!")
    {
        $this->content = $content;
    }

    public function handle()
    {
        Log::info('RabbitMQ processed: ' . $this->content);

        DB::table('rabbitmq_demo_messages')->insert([
            'content' => $this->content,
            'created_at' => now(),
            'updated_at' => now(),
        ]);
    }
}
