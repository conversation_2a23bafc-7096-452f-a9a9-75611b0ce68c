<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

use App\Models\Shipment;

class VisibilityController extends Controller
{
    /**
     * Display a listing of Shipments.
     */
    public function shipment(Request $request)
    {
        try {
            try {
                $testQuery = Shipment::query()->limit(1);
                $testResult = $testQuery->get();
                Log::debug('Database connection test successful', ['count' => $testResult->count()]);
            } catch (\Exception $dbError) {
                Log::error('Database connection failed', [
                    'error' => $dbError->getMessage(),
                    'file' => $dbError->getFile(),
                    'line' => $dbError->getLine(),
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Database connection failed.',
                    'debug' => config('app.debug') ? $dbError->getMessage() : null,
                ], 500);
            }

            $org_id = $request->input('org_id');

            if (!$org_id) {
                $user = Auth::user();
                if ($user) {
                    $org_id = $user->org_id;
                }
            }

            if (!$org_id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required.',
                ], 422);
            }

            $query = Shipment::query()->where('org_id', $org_id);

            if ($request->has('user_id')) {
                $query->where('user_id', $request->input('user_id'));
            }

            $shipments = $query->get();

            $responseData = $shipments->map(fn($row) => [
                'id' => $row->id,
                'org_id' => $row->org_id,
                'user_id' => $row->user_id,
                'status' => $row->status,
                'created_at' => $row->created_at,
                'updated_at' => $row->updated_at,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Shipments retrieved successfully.',
                'data' => $responseData,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching shipments', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch shipments.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
}