<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Shipment;

class VisibilityController extends Controller
{
    private $userdata;

    /**
     * Display a listing of Shipments.
     */
    public function shipments(Request $request)
    {
        try {
            $user = Auth::guard('api')->user();
            $userOrganization = SxUserOrganization::where('user_id', $user->id)->where('org_id', $user->default_org_id)->first();
            if (!$userOrganization) {
                return response()->json(['status' => 'fail', 'message' => 'User organization not found', 'status' => 0], 404);
            }
            $defaultOrgPrivilegeIds = json_decode($userOrganization->roles, true) ?? [];

            if (empty($defaultOrgPrivilegeIds)) {
                return response()->json(['status' => 'fail', 'message' => 'Privileges not found', 'status' => 0], 404);
            }

            $results = DB::table('sx_users as u')
                ->join('sx_user_previlege_access_rules as r', 'u.id', '=', 'r.user_id')
                ->join('sx_previlleges as p', 'r.previllege_id', '=', 'p.id')
                ->join('sx_previlege_types as t', 'p.previlege_type', '=', 't.id')
                ->join('sx_party_types as pt', 'p.party_type', '=', 'pt.id')
                ->select('u.id', 'u.employee_name')
                ->whereIn('r.previllege_id', $defaultOrgPrivilegeIds)
                ->where('t.type_name', 'Party')
                ->where('pt.type_name', '!=', 'Carrier')
                ->where('r.status', 1)
                ->where('t.status', 1)
                ->where('p.status', 1)
                ->where('u.status', 1)
                ->get();

            $partiesData = $results->toArray();

            return response()->json([
                'status' => 'success',
                'message' => 'parties retrieved successfully.',
                'data' => $shipment,
                'count' => count($partiesData)
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error getting parties data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get parties data.',
            ], 500);
        }
    }
}