<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\Shipment;

class VisibilityController extends Controller
{
    private $userdata;

    /**
     * Display a listing of Shipments.
     */
    public function shipment(Request $request)
    {
        try {
            try {
                $testQuery = Shipment::query()->limit(1);
                $testResult = $testQuery->get();
                Log::debug('Database connection test successful', ['count' => $testResult->count()]);
            } catch (\Exception $dbError) {
                Log::error('Database connection failed', [
                    'error' => $dbError->getMessage(),
                    'file' => $dbError->getFile(),
                    'line' => $dbError->getLine(),
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Database connection failed.',
                    'debug' => config('app.debug') ? $dbError->getMessage() : null,
                ], 500);
            }

            $this->userdata = Auth::guard('api')->user();
            $org_id = $request->input('org_id');

            if (!$org_id) {
                if ($this->userdata) {
                    $org_id = $this->userdata->org_id;
                }
            }

            if (!$org_id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required.',
                ], 422);
            }

            $data = [
                        'page_title' => 'Shipment Visibility',
                        'orders' => [],
                        'postData' => $request->all()
                    ];
            $searchTerm = $data['postData']['table_search'];

            // Get shifts data and apply search filter if needed
            $shiftsData = $this->getFilteredShiftsData($searchTerm);

            $datatable_headers = [
                'Id',
                'Source',
                'Destination',
                'Start Date',
                'End Date'
            ];

            if ($this->userdata->company_code != 'ASCO') {
                $datatable_headers[] = $this->lang->line('customer');
            }

            $datatable_headers[] = $this->lang->line('carrier');

            return response()->json([
                'status' => 'success',
                'message' => 'Shipments info retrieved successfully.',
                'data' => $data,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching shipments', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch shipments.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    private function getFilteredShiftsData(?string $searchTerm): array
    {
        $shiftsData = $this->getUserShiftsData();

        if (!empty($searchTerm)) {
            if ($this->userdata->company_code == "ASCO") {
                $searchTerm = $this->handleASCOSearchTerm($searchTerm);
            }

            $shiftsData = array_filter($shiftsData, function ($shift) use ($searchTerm) {
                foreach ($shift as $fieldValue) {
                    if (is_string($fieldValue) && stripos($fieldValue, $searchTerm) !== false) {
                        return true;
                    }
                }
                return false;
            });
        }

        return $shiftsData;
    }

    private function getUserShiftsData(): array
    {
        // get all IDs available
        $allShiftIds = $this->getShiftIdsAccessibleForUser(
            $this->CI->input->post(null, true)
        );

        // enable pagination, on what page am I ?
        $pageNumber = $this->setupPaginationByUrl(count($allShiftIds));

        // get and return Data of trips on that page
        $shiftsData = $this->getTripsList( $allShiftIds, $pageNumber );

        // filling gaps
        $vehicleUpdates = $this->getVehicleUpdates($shiftsData);
        $carrierUpdates = $this->getCarrierUpdates($shiftsData);
        $driverUpdates = $this->getDriverUpdates($shiftsData);
        $endtimeUpdates = $this->CI->ship_order_stops
            ->getEndtimeUpdates($shiftsData);

        $result = [];
        foreach ($shiftsData as $row) {
            $row['register_number'] = $vehicleUpdates[$row['vehicle_id'] ?? '-'] ?? $row['register_number'];
            $row['carrier_name'] = $carrierUpdates[$row['vendor_id'] ?? '-']['name'] ?? $row['carrier_name'];
            $row['carrier_broker'] = $carrierUpdates[$row['vendor_id'] ?? '-']['broker'] ?? $row['carrier_broker'];
            $row['driver_name'] = $driverUpdates[$row['id'] ?? '-'] ?? $row['driver_name'];
            if (isset($row['startdate'])) {
                $row['startdate'] = $this->updateTimestampDate($row['startdate']);
            }
            if (isset($row['enddate'])) {
                $row['enddate'] = $this->updateTimestampDate(
                    $endtimeUpdates[$row['id'] ?? '-'] ?? $row['enddate']
                );
            }

            $row['stops'] = [];
            $row['transportmode'] = $row['transport_mode'] ?? 'FTL';
            $result [$row['id']] = $row;
        }

        return $result;
    }

    private function handleASCOSearchTerm(string $searchTerm): string
    {
        $ordref = ["INBOUND", "OUTBOUND"];
        foreach ($ordref as $value) {
            if (stripos($searchTerm, $value) !== false) {
                $qry = $this->db->query("SELECT o.shipmentid FROM tb_orders o,tb_order_references r 
                                       WHERE o.id=r.order_id AND r.reference_id='Journey ID' 
                                       AND r.ref_value LIKE '".$searchTerm."' LIMIT 1");
                if ($qry->num_rows() > 0) {
                    return $qry->row()->shipmentid;
                }
            }
        }
        return $searchTerm;
    }
}