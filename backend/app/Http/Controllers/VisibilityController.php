<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Models\User;
use App\Models\Shipment;

class VisibilityController extends Controller
{
    private $userdata;

    /**
     * Display a listing of Shipments.
     */
    public function shipment(Request $request)
    {
        try {
            try {
                $testQuery = Shipment::query()->limit(1);
                $testResult = $testQuery->get();
                Log::debug('Database connection test successful', ['count' => $testResult->count()]);
            } catch (\Exception $dbError) {
                Log::error('Database connection failed', [
                    'error' => $dbError->getMessage(),
                    'file' => $dbError->getFile(),
                    'line' => $dbError->getLine(),
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Database connection failed.',
                    'debug' => config('app.debug') ? $dbError->getMessage() : null,
                ], 500);
            }

            $this->userdata = Auth::guard('api')->user();
            $org_id = $request->input('org_id');

            if (!$org_id) {
                if ($this->userdata) {
                    $org_id = $this->userdata->org_id;
                }
            }

            if (!$org_id) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required.',
                ], 422);
            }

            $postData = $request->all();

            $data = [
                        'page_title' => 'Shipment Visibility',
                        'orders' => [],
                        'postData' => $postData
                    ];

            // Get shifts data and apply search filter if needed
            $shiftsData = $this->getFilteredShiftsData($postData);

            $datatable_headers = [
                'Id',
                'Source',
                'Destination',
                'Start Date',
                'End Date'
            ];

            if ($this->userdata->company_code != 'ASCO') {
                $datatable_headers[] = $this->lang->line('customer');
            }

            $datatable_headers[] = $this->lang->line('carrier');

            return response()->json([
                'status' => 'success',
                'message' => 'Shipments info retrieved successfully.',
                'data' => $data,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching shipments', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch shipments.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    private function getFilteredShiftsData(array $postData): array
    {
        $shiftsData = $this->getUserShiftsData($postData);

        if (!empty($postData['table_search'])) {
            if ($this->userdata->company_code == "ASCO") {
                $searchTerm = $this->handleASCOSearchTerm($postData['table_search']);
            }

            $shiftsData = array_filter($shiftsData, function ($shift) use ($searchTerm) {
                foreach ($shift as $fieldValue) {
                    if (is_string($fieldValue) && stripos($fieldValue, $searchTerm) !== false) {
                        return true;
                    }
                }
                return false;
            });
        }

        return $shiftsData;
    }

    private function handleASCOSearchTerm(string $searchTerm): string
    {
        $ordref = ["INBOUND", "OUTBOUND"];

        foreach ($ordref as $value) {
            if (stripos($searchTerm, $value) !== false) {
                // Laravel Eloquent query using Query Builder
                $result = DB::table('orders as o')
                    ->join('order_references as r', 'o.id', '=', 'r.order_id')
                    ->select('o.shipmentid')
                    ->where('r.reference_id', 'Journey ID')
                    ->where('r.ref_value', 'LIKE', $searchTerm)
                    ->first();

                if ($result) {
                    return $result->shipmentid;
                }
            }
        }

        return $searchTerm;
    }

    private function getUserShiftsData(array $postData): array
    {
        // get all IDs available
        $allShiftIds = $this->getShiftIdsAccessibleForUser($postData); // TO-DO

        // enable pagination, on what page am I ?
        $pageNumber = $this->setupPaginationByUrl(count($allShiftIds));

        // get and return Data of trips on that page
        $shiftsData = $this->getTripsList( $allShiftIds, $pageNumber );

        // filling gaps
        $vehicleUpdates = $this->getVehicleUpdates($shiftsData);
        $carrierUpdates = $this->getCarrierUpdates($shiftsData);
        $driverUpdates = $this->getDriverUpdates($shiftsData);
        $endtimeUpdates = $this->CI->ship_order_stops
            ->getEndtimeUpdates($shiftsData);

        $result = [];
        foreach ($shiftsData as $row) {
            $row['register_number'] = $vehicleUpdates[$row['vehicle_id'] ?? '-'] ?? $row['register_number'];
            $row['carrier_name'] = $carrierUpdates[$row['vendor_id'] ?? '-']['name'] ?? $row['carrier_name'];
            $row['carrier_broker'] = $carrierUpdates[$row['vendor_id'] ?? '-']['broker'] ?? $row['carrier_broker'];
            $row['driver_name'] = $driverUpdates[$row['id'] ?? '-'] ?? $row['driver_name'];
            if (isset($row['startdate'])) {
                $row['startdate'] = $this->updateTimestampDate($row['startdate']);
            }
            if (isset($row['enddate'])) {
                $row['enddate'] = $this->updateTimestampDate(
                    $endtimeUpdates[$row['id'] ?? '-'] ?? $row['enddate']
                );
            }

            $row['stops'] = [];
            $row['transportmode'] = $row['transport_mode'] ?? 'FTL';
            $result [$row['id']] = $row;
        }

        return $result;
    }

    private function getShiftIdsAccessibleForUser(array $post): array
    {
        $userRoleId = $this->userdata->previllege_id;
        if ($userRoleId === "5") {
            $shiftIds = $this->getShiftsBasedOnKNOps($post);
        } else {
            [$stopDetailUserIds, $userWhere] = $this->getUsersForShipmentsCondition();

            $shiftIds = $this->CI->ShipmentVisibilityModel->getNormalTripsFroVisibility(
                $post,
                $stopDetailUserIds,
                $userWhere,
                'tb_shifts s',
                $indexColumn = 's.id',
                $groupBy = 's.id',
                $excludeCountQuery = 'Yes'
            );
        }

        return empty($shiftIds) ? [] : $shiftIds;
    }

    private function getShiftsBasedOnKNOps(array $post): array
    {
        $data = $this->getAllKnopsBasedOrderIds();
        $shiftIds = $data['allshiftIds'];
        if (empty($shiftIds)) {
            return [];
        }
        $normalTripsCondition = $this->CI->knopstripsmodel->getSearchConditionsForKNOpsInVisibilityPage($this->userdata, $post, $shiftIds);
        $joinsArray[] = [
            'table_name' => 'tb_shft_veh as sv',
            'condition' => "sv.shft_id=s.id AND sv.status=1",
            'join_type' => 'left'
        ];
        $joinsArray[] = [
            'table_name' => 'tb_vendors as v',
            'condition' => "v.id = sv.carrier_id",
            'join_type' => 'left'
        ];
        $joinsArray[] = [
            'table_name' => 'tb_orders as o',
            'condition' => "o.shift_id=s.id AND o.status>0",
            'join_type' => 'inner'
        ];

        $getRecordListing = $this->CI->Datatables_model->datatablesQuery(
            ['s.id'],
            [],
            'tb_shifts s',
            $joinsArray,
            implode(' AND ', $normalTripsCondition),
            's.id',
            'TRUE',
            's.id',
            'Yes',
            'POST'
        );
        $recordsData = $getRecordListing['data'] ?? [];
        $finalShiftIds = $this->CI->knopstripsmodel->getAllKNOpsBasedCrossBorderShiftIds($this->userdata, $post);
        foreach ($recordsData as $eachRow) {
            $finalShiftIds[] = $eachRow['id'];
        }
        return array_unique($finalShiftIds);
    }

    public function getAllKnopsBasedOrderIds(): array
    {
        $queryString = "SELECT o.id,o.order_id,o.shift_id,o.trip_id,o.shipmentid FROM tb_orders o, tb_order_details d 
        WHERE o.id = d.order_row_id AND o.status > ? AND  o.shift_id > ? AND o.trip_sts = ? ";
        $queryParams = [0, 0, 0,];
        if ($this->userData->childId > 0) {
            $usersWithoutDepartment = $this->userData->usersWithoutDepartment;
            if (!empty($usersWithoutDepartment)) {
                $i = 1;
                $arrayCount = count($usersWithoutDepartment);
                foreach ($usersWithoutDepartment as $eachLine) {
                    if ($i > 1) {
                        $queryString .= " OR (o.company_code = ? AND o.branch_code = ? ) ";
                        $queryParams[] = $eachLine['companyCode'];
                        $queryParams[] = $eachLine['branchCode'];
                    } else {
                        $queryString .= " AND ( (o.company_code = ? AND o.branch_code = ? ) ";
                        $queryParams[] = $eachLine['companyCode'];
                        $queryParams[] = $eachLine['branchCode'];
                    }
                    if ($arrayCount === $i) {
                        $queryString .= " ) AND ";
                    }
                    $i++;
                }
            } else {
                $j = 1;
                $usersWithDepartmentCodes = $this->userData->usersWithDepartmentCodes;
                if (!empty($usersWithDepartmentCodes)) {
                    $arrayCount = count($usersWithDepartmentCodes);
                    foreach ($usersWithDepartmentCodes as $eachLine) {
                        if ($j > 1) {
                            $queryString .= " OR (o.company_code = ? AND o.branch_code = ? AND d.department_code = ? ) ";
                            $queryParams[] = $eachLine['companyCode'];
                            $queryParams[] = $eachLine['branchCode'];
                            $queryParams[] = $eachLine['departmentCode'];
                        } else {
                            $queryString .= " AND ( (o.company_code = ? AND o.branch_code = ? AND d.department_code = ? ) ";
                            $queryParams[] = $eachLine['companyCode'];
                            $queryParams[] = $eachLine['branchCode'];
                            $queryParams[] = $eachLine['departmentCode'];
                        }
                        if ($arrayCount === $j) {
                            $queryString .= " ) AND ";
                        }
                        $j++;
                    }
                }
            }
        } else {
            $queryString .= "AND o.company_code = ? AND o.branch_code = ? AND ";
            $queryParams[] = $this->userData->company_code;
            $queryParams[] = $this->userData->branch_code;
            if ($this->userData->department_code != "") {
                $queryString .= " d.department_code = ? AND ";
                $queryParams[] = $this->userData->department_code;
            }
        }
        $queryString .= " d.status > ? ORDER BY o.id DESC";
        $queryParams[] = 0;
        $getOrderIds = $this->db->query($queryString, $queryParams);
        $getOrdersData = $getOrderIds->num_rows() > 0 ? $getOrderIds->result_array() : [];
        foreach ($getOrdersData as $eachRow) {
            $orderIds[] = $eachRow['id'];
            $tripId = $eachRow['trip_id'];
            $shiftId = $eachRow['shift_id'];
            $allshiftIds[] = $shiftId;
            $ordersData[$eachRow['id']] = ['rowId' => $eachRow['id'], 'bookingId' => $eachRow['order_id'], 'shiftId' => $shiftId, 'tripId' => $tripId, 'shipmentId' => $eachRow['shipmentid']];
        }
        return ['orderIds' => $orderIds ?? [], 'allshiftIds' => $allshiftIds ?? [], 'ordersData' => $ordersData ?? []];
    }
}