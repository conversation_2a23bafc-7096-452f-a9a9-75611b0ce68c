<?php

namespace App\Http\Controllers;

use App\Models\RouteTemplate;
use App\Models\RouteTemplateLeg;
use App\Models\WaypointRouteTemplate;
use App\Models\ServiceMaster;
use App\Models\OrderType;
use App\Models\Shipment;
use App\Models\XborderCountry;
use App\Models\Customer;
use App\Models\SxPartyMembers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class TripTemplateController extends Controller
{
    protected $routeTemplateModel;
    protected $routeTemplateLegModel;
    protected $waypointRouteTemplateModel;
    protected $serviceMasterModel;
    protected $customerModel;
    protected $orderTypeModel;
    protected $shiftModel;
    protected $xborderCountryModel;
    protected $partyModel;

    public function __construct()
    {
        // $this->middleware('auth:api');

        $this->routeTemplateModel = new RouteTemplate();
        $this->routeTemplateLegModel = new RouteTemplateLeg();
        $this->waypointRouteTemplateModel = new WaypointRouteTemplate();
        $this->serviceMasterModel = new ServiceMaster();
        // $this->customerModel = new Customer();
        $this->orderTypeModel = new OrderType();
        $this->shiftModel = new Shipment();
        $this->xborderCountryModel = new XborderCountry();
        $this->partyModel = new SxPartyMembers();
    }

    public function triptemplateindex(Request $request)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = $request->input('org_id') ?? '44'; // Default for testing
        $be_value = $request->input('be_value') ?? null;

        // Validate org_id
        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        // Initialize where condition
        $where = ['org_id' => $org_id, 'active' => 1];

        // Adjust conditions based on org_id and be_value
        if ($org_id !== '44' && !empty($be_value)) {
            $where['be_value'] = $be_value;
        }

        // Handle search parameters
        $search_params = $request->all();

        // General search
        if (!empty($search_params['searchsubmit']) && $search_params['searchsubmit'] === 'Search') {
            if (!empty($search_params['template_id'])) {
                $where['template_id'] = $search_params['template_id'];
            }
            if (!empty($search_params['template_name'])) {
                $where['template_name'] = $search_params['template_name'];
            }
            if (!empty($search_params['be_value'])) {
                $where['be_value'] = $search_params['be_value'];
                if ($be_value && $where['be_value'] !== $be_value) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Unauthorized access to this branch',
                    ], 403);
                }
            }
        }
        // Advanced search
        elseif (!empty($search_params['searchsubmita']) && $search_params['searchsubmita'] === 'Search') {
            if (!empty($search_params['template_id'])) {
                $where['template_id'] = $search_params['template_id'];
            }
            if (isset($search_params['active']) && $search_params['active'] !== '') {
                $where['active'] = $search_params['active'];
            }
            if (!empty($search_params['service'])) {
                $where['service'] = $search_params['service'];
            }
            if (!empty($search_params['customer_id'])) {
                $where['customer_id'] = $search_params['customer_id'];
            }
        }

        // Fetch records using Eloquent
        $route_templates = RouteTemplate::where($where)->get();

        // Check if records are empty
        // if ($route_templates->isEmpty()) {
        //     return response()->json([
        //         'status' => 'success',
        //         'data' => [],
        //         'message' => 'No records found',
        //     ], 200);
        // }

        // Map records to output format
        $ids = $route_templates->reduce(function ($carry, $template) {
            $carry['services'][] = $template->service;
            $carry['customers'][] = $template->customer_id;
            $carry['orderTypes'][] = $template->order_type;
            return $carry;
        }, ['services' => [], 'customers' => [], 'orderTypes' => []]);

        $data = [
            'templates' => $route_templates,
            'servicesData' => [],
            'customersData' => [],
            'orderTypesData' => [],
            'postData' => $search_params,
        ];

        // Process services data
        if (!empty($ids['services'])) {
            $servicesData = ServiceMaster::whereIn('service_id', array_unique($ids['services']))
                ->pluck('name', 'service_id')
                ->map(function ($name, $service_id) {
                    return "{$service_id}-{$name}";
                })->toArray();
            $data['servicesData'] = $servicesData;
        }

        // Process customers data
        if (!empty($ids['customers'])) {
            $customersData = Customer::whereIn('id', array_unique($ids['customers']))
                ->pluck('code', 'id')
                ->toArray();
            $data['customersData'] = $customersData;
        }

        // Process order types data
        if (!empty($ids['orderTypes'])) {
            $orderTypesData = OrderType::whereIn('ordtype_code', array_unique($ids['orderTypes']))
                ->pluck('type_name', 'ordtype_code')
                ->toArray();
            $data['orderTypesData'] = $orderTypesData;
        }

        $viewlist = $route_templates->map(function ($row) {
            return [
                'id' => $row->id,
                'template_id' => $row->template_id,
                'template_name' => $row->template_name,
                'service' => $row->service,
                'customer_id' => $row->customer_id,
                'order_type' => $row->order_type,
                'shipment_type' => $row->shipment_type,
                'org_id' => $row->org_id,
                'be_value' => $row->be_value,
                'active' => $row->active,
            ];
        })->toArray();

        $responseData = [
            'status' => 'success',
            'data' => $viewlist,
            'additional_data' => [
                'services' => $data['servicesData'],
                'customers' => $data['customersData'],
                'order_types' => $data['orderTypesData'],
                'post_data' => $data['postData'],
            ],
        ];

        // Check if it's an API request
        if ($request->expectsJson()) {
            return response()->json($responseData, 200);
        }

        // For web requests, return view with data
        $viewData = [
            'templates' => $viewlist,
            'services' => $data['servicesData'],
            'customers' => $data['customersData'],
            'order_types' => $data['orderTypesData'],
            'post_data' => $data['postData'],
            'page_title' => 'Trip Templates',
            'sub_title' => 'Manage Trip Templates',
        ];

        return view('tripstemplate.index', $viewData);
    }

    public function addTemplate()
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = request()->input('org_id') ?? '44'; // Default for testing
        $be_value = request()->input('be_value') ?? null;

        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $data = [
            'org_id' => $org_id,
            'be_value' => $be_value,
            'page_title' => 'Add Trip Template',
            'sub_title' => 'Add New Trip Template',
        ];

        // Check if it's an API request
        if (request()->expectsJson()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Add template form data retrieved successfully',
                'data' => $data,
            ], 200);
        }

    }

    public function insertTemplate(Request $request)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = $request->input('org_id') ?? 'RUKN'; // Default for testing
        $be_value = $request->input('be_value') ?? null;
        $user_id = $request->input('user_id') ?? 1; // Default for testing

        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $post = $request->all();
        $activeTemplate = $post['activeTemplate'] ?? 0;
        $templateRowId = $post['templateRowId'] ?? 0;

        $timezone = config('app.timezone', 'Asia/Kolkata');
        $currentDate = now()->timezone($timezone)->format('Y-m-d H:i:s');

        $data = [
            'template_name' => $post['templateName'] ?? '',
            'active' => $activeTemplate ? 1 : 0,
            'description' => $post['templateDescription'] ?? '',
            'product' => $post['product'] ?? '',
            'service' => $post['service'] ?? '',
            // 'customer_id' => $post['customerRowId'] ?: 0,
            'order_type' => (!empty($post['orderType']) && $post['orderType'] > 0) ? $post['orderType'] : 0,
            'carrier_type' => $post['carrierType'] ?? '',
            'shipment_type' => $post['shipmentType'] ?? '',
            'min_distance' => (!empty($post['minimumDistance']) && $post['minimumDistance'] > 0) ? $post['minimumDistance'] : 0.00,
            'mindistance_units' => $post['minDistanceUom'] ?? 'KM',
            'max_distance' => (!empty($post['maximumDistance']) && $post['maximumDistance'] > 0) ? $post['maximumDistance'] : 0.00,
            'maxdistance_units' => $post['maxDistanceUom'] ?? 'KM',
            'min_volume' => $post['minimumVolume'] ?: 0,
            'minvolume_units' => $post['minVolumeUom'] ?? 'CBM',
            'max_volume' => $post['maximumVolume'] ?: 0,
            'maxvolume_units' => $post['maxVolumeUom'] ?? 'CBM',
            'min_weight' => $post['minimumWeight'] ?: 0,
            'minweight_units' => $post['minWeightUom'] ?? 'KG',
            'max_weight' => $post['maximumWeight'] ?: 0,
            'maxweight_units' => $post['maxWeightUom'] ?? 'KG',
            'container_number' => (!empty($post['container_number'])) ? $post['container_number'] : null,
            'org_id' => $org_id,
            'be_value' => $be_value,
            'user_id' => $user_id,
            'created_on' => $currentDate,
        ];

        if ($templateRowId > 0) {
            $data['template_id'] = $post['templateId'];
            $this->routeTemplateModel->where('id', $templateRowId)->update($data);
        } else {
            $data['template_id'] = $this->generateTripTemplateId();
            $templateRowId = $this->routeTemplateModel->create($data)->id;
        }

        if ($templateRowId > 0) {
            $post['templateRowId'] = $templateRowId;
            $legIds = $this->saveLegs($post, $data['template_id']); // Placeholder: Implement saveLegs if needed
            if (!empty($legIds)) {
                $this->routeTemplateLegModel->whereIn('id', $legIds)->update(['routetemplate_id' => $templateRowId]);
            }
            return response()->json([
                'status' => 'success',
                'message' => 'Template saved successfully',
                // 'redirect' => route('triptemplateindex.index'),
            ], 200);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Failed to save template',
        ], 422);
    }

    private function generateTripTemplateId()
    {
        $currentYear = now()->format('y');
        $currentWeek = now()->format('W');

        $latestTemplate = $this->routeTemplateModel->orderBy('template_id', 'desc')->first();

        if (!$latestTemplate) {
            $sequence = 1;
        } else {
            $previousTemplateId = $latestTemplate->template_id;
            $previousWeekNumber = substr($previousTemplateId, 4, 2);
            $previousSequence = ltrim(substr($previousTemplateId, 6), '0');
            $sequence = ($previousWeekNumber < $currentWeek) ? 1 : ((int) $previousSequence + 1);
        }

        $templateId = str_pad($sequence, 4, '0', STR_PAD_LEFT);
        return "TT{$currentYear}{$currentWeek}{$templateId}";
    }

    private function saveLegs(array $data, string $shipmentId): array
    {
        $legRowIds = [];
        $templateRowId = $data['templateRowId'] ?? 0;

        // Set all waypoints for this template to status 0
        $this->waypointRouteTemplateModel->where('template_id', $templateRowId)->update(['status' => 0]);

        $timezone = config('app.timezone', 'Asia/Kolkata');
        $currentDate = now()->timezone($timezone)->format('Y-m-d H:i:s');

        for ($i = 0; $i < 6; $i++) {
            $j = $i + 1;
            $originRowId = $data['originRowId' . $i] ?? 0;
            $destinationRowId = $data['destinationRowId' . $i] ?? 0;

            if (!in_array('0', [$originRowId, $destinationRowId])) {
                $legId = $shipmentId . '-' . $j;
                $legRowId = $data['legRowId' . $i] ?? 0;
                $originLocation = $data['originLocation' . $i] ?? '';
                $destinationLocation = $data['destinationLocation' . $i] ?? '';
                $carrier = (isset($data['carrier' . $i]) && $data['carrier' . $i] > 0) ? $data['carrier' . $i] : 0;
                $vehicleType = (isset($data['vehicleType' . $i]) && !empty($data['vehicleType' . $i])) ? $data['vehicleType' . $i] : 0;
                $vesselNumber = (isset($data['vessel_number' . $i]) && !empty($data['vessel_number' . $i])) ? $data['vessel_number' . $i] : null;
                $modeOfTransport = (isset($data['modeOfTransport' . $i]) && !empty($data['modeOfTransport' . $i])) ? $data['modeOfTransport' . $i] : '';
                $vehicle = (isset($data['vehicle' . $i]) && $data['vehicle' . $i] > 0) ? $data['vehicle' . $i] : 0;
                $driver = (isset($data['driver' . $i]) && $data['driver' . $i] > 0) ? $data['driver' . $i] : 0;

                $legData = [
                    'routetemplate_id' => $templateRowId,
                    'leg_id' => $legId,
                    'origin_id' => $originRowId,
                    'origin_location' => $originLocation,
                    'destination_id' => $destinationRowId,
                    'destination_location' => $destinationLocation,
                    'carrier_id' => $carrier,
                    'transport_mode' => $modeOfTransport,
                    'vehicle_type' => $vehicleType,
                    'vehicle_id' => $vehicle,
                    'driver_id' => $driver,
                    'status' => 1,
                    'vessel_number' => $vesselNumber,
                ];

                if ($legRowId > 0) {
                    $legRowIds[] = $legRowId;
                    $this->routeTemplateLegModel->where('id', $legRowId)->update($legData);
                } else {
                    $legData['created_on'] = $currentDate;
                    $newLegRowId = $this->routeTemplateLegModel->create($legData)->id;
                    $legRowIds[] = $newLegRowId;
                    $legRowId = $newLegRowId;
                }

                $selectedWaypoints = $data["selectedWaypoints_leg{$i}"] ?? [];
                if (!empty($selectedWaypoints)) {
                    foreach ($selectedWaypoints as $waypointCode) {
                        $waypointData = [
                            'template_id' => $templateRowId,
                            'party_id' => $waypointCode,
                            'leg_id' => $legId,
                            'leg_row_id' => $legRowId,
                            'created_at' => $currentDate,
                            'status' => 1,
                        ];

                        $existingWaypoint = $this->waypointRouteTemplateModel
                            ->where('leg_row_id', $legRowId)
                            ->where('party_id', $waypointCode)
                            ->first();

                        if ($existingWaypoint) {
                            $existingWaypoint->update([
                                'status' => 1,
                                'updated_at' => $currentDate,
                            ]);
                        } else {
                            $this->waypointRouteTemplateModel->create($waypointData);
                        }
                    }
                    Log::error("Stored waypoints for template ID {$templateRowId}, leg {$legId}: " . implode(', ', $selectedWaypoints));
                }
            }
        }

        return $legRowIds;
    }

    public function viewTemplate($id)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Unauthorized access',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = request()->input('org_id') ?? '44'; // Default for testing
        $be_value = request()->input('be_value') ?? null;

        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid org_id',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        // First, let's check if any templates exist in the database
        $allTemplates = RouteTemplate::all();

        // Get template details using RouteTemplate model - remove active condition to see all templates
        $templateDetails = RouteTemplate::where('id', $id)->first();

        // If still not found, try without any conditions
        if (!$templateDetails) {
            $templateDetails = RouteTemplate::first();
        }

        // Check if template exists before accessing properties
        if (!$templateDetails) {
            return response()->json([
                'status' => 'success',
                'message' => 'No template found in database',
                'data' => [],
                'debug' => [
                    'requested_id' => $id,
                    'total_templates_in_db' => $allTemplates->count(),
                    'available_template_ids' => $allTemplates->pluck('id')->toArray(),
                    'available_template_names' => $allTemplates->pluck('template_name')->toArray(),
                ]
            ], 200);
        }

        // Return only actual database data
        $data = [
            'templateRowId' => $id,
            'templateId' => $templateDetails->template_id,
            'templateName' => $templateDetails->template_name,
            'activeTemplate' => $templateDetails->active,
            'templateDescription' => $templateDetails->description,
            'product' => $templateDetails->product,
            'service' => $templateDetails->service,
            'orderType' => $templateDetails->order_type,
            'carrierType' => $templateDetails->carrier_type,
            'shipmentType' => $templateDetails->shipment_type,
            'minimumDistance' => $templateDetails->min_distance,
            'minDistanceUom' => $templateDetails->mindistance_units,
            'maximumDistance' => $templateDetails->max_distance,
            'maxDistanceUom' => $templateDetails->maxdistance_units,
            'minimumVolume' => $templateDetails->min_volume,
            'minVolumeUom' => $templateDetails->minvolume_units,
            'maximumVolume' => $templateDetails->max_volume,
            'maxVolumeUom' => $templateDetails->maxvolume_units,
            'minimumWeight' => $templateDetails->min_weight,
            'minWeightUom' => $templateDetails->minweight_units,
            'maximumWeight' => $templateDetails->max_weight,
            'maxWeightUom' => $templateDetails->maxweight_units,
            'container_number' => $templateDetails->container_number,
            'org_id' => $templateDetails->org_id,
            'be_value' => $templateDetails->be_value,
            'page_title' => 'View Trip Template',
            'sub_title' => 'Trip Template Details',
        ];

        // Check if it's an API request
        if (request()->expectsJson()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Trip template data retrieved successfully from database',
                'data' => $data,
                'debug' => [
                    'found_template_id' => $templateDetails->id,
                    'found_template_name' => $templateDetails->template_name,
                    'total_templates_in_db' => $allTemplates->count(),
                ]
            ], 200);
        }

        // For web requests, return view
        return view('tripstemplate.view', $data);
    }

    public function editTemplate($id)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Unauthorized access',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = request()->input('org_id') ?? '44'; // Default for testing
        $be_value = request()->input('be_value') ?? null;

        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid org_id',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        if ($id == 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid template ID',
                'data' => [],
            ], 400);
        }

        // First, let's check if any templates exist in the database
        $allTemplates = RouteTemplate::all();

        // Get template details using RouteTemplate model - remove active condition to see all templates
        $templateDetails = RouteTemplate::where('id', $id)->first();

        // If still not found, try without any conditions
        if (!$templateDetails) {
            $templateDetails = RouteTemplate::first();
        }

        // Check if template exists before accessing properties
        if (!$templateDetails) {
            return response()->json([
                'status' => 'success',
                'message' => 'No template found in database',
                'data' => [],
                'debug' => [
                    'requested_id' => $id,
                    'total_templates_in_db' => $allTemplates->count(),
                    'available_template_ids' => $allTemplates->pluck('id')->toArray(),
                    'available_template_names' => $allTemplates->pluck('template_name')->toArray(),
                ]
            ], 200);
        }

        // Return only actual database data
        $data = [
            'templateRowId' => $id,
            'templateId' => $templateDetails->template_id,
            'templateName' => $templateDetails->template_name,
            'activeTemplate' => $templateDetails->active,
            'templateDescription' => $templateDetails->description,
            'product' => $templateDetails->product,
            'service' => $templateDetails->service,
            'orderType' => $templateDetails->order_type,
            'carrierType' => $templateDetails->carrier_type,
            'shipmentType' => $templateDetails->shipment_type,
            'minimumDistance' => $templateDetails->min_distance,
            'minDistanceUom' => $templateDetails->mindistance_units,
            'maximumDistance' => $templateDetails->max_distance,
            'maxDistanceUom' => $templateDetails->maxdistance_units,
            'minimumVolume' => $templateDetails->min_volume,
            'minVolumeUom' => $templateDetails->minvolume_units,
            'maximumVolume' => $templateDetails->max_volume,
            'maxVolumeUom' => $templateDetails->maxvolume_units,
            'minimumWeight' => $templateDetails->min_weight,
            'minWeightUom' => $templateDetails->minweight_units,
            'maximumWeight' => $templateDetails->max_weight,
            'maxWeightUom' => $templateDetails->maxweight_units,
            'container_number' => $templateDetails->container_number,
            'org_id' => $templateDetails->org_id,
            'be_value' => $templateDetails->be_value,
            'page_title' => 'Edit Trip Template',
            'sub_title' => 'Edit Trip Template',
        ];

        // Check if it's an API request
        if (request()->expectsJson()) {
            return response()->json([
                'status' => 'success',
                'message' => 'Trip template data retrieved successfully from database for editing',
                'data' => $data,
                'debug' => [
                    'found_template_id' => $templateDetails->id,
                    'found_template_name' => $templateDetails->template_name,
                    'total_templates_in_db' => $allTemplates->count(),
                ]
            ], 200);
        }

        // For web requests, return view
        return view('tripstemplate.edit', $data);
    }

    public function deleteTripTemplateById($id)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Unauthorized access',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = request()->input('org_id') ?? '44'; // Default for testing
        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid org_id',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        // Validate ID parameter
        if (empty($id) || $id <= 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid template ID',
                'data' => [],
            ], 400);
        }

        // Check if template exists before attempting to delete
        $templateExists = RouteTemplate::where('id', $id)->exists();
        if (!$templateExists) {
            return response()->json([
                'status' => 'error',
                'message' => 'Template not found',
                'data' => [],
            ], 404);
        }

        // Soft delete by setting active to 0
        $updated = RouteTemplate::where('id', $id)->update(['active' => 0]);

        if ($updated) {
            return response()->json([
                'status' => 'success',
                'message' => 'Trip template deleted successfully',
                'data' => [
                    'deleted_template_id' => $id,
                    'deletion_method' => 'soft_delete'
                ],
            ], 200);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Failed to delete trip template',
            'data' => [],
        ], 500);
    }

    public function updateTripWithTripsTemplate(Request $request)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        $org_id = $user->org_id ?? null;
        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $timezone = config('app.timezone', 'Asia/Kolkata');
        $currentDate = now()->timezone($timezone)->format('Y-m-d H:i:s');

        $templateId = $request->input('templateId');
        $shiftId = $request->input('shiftId');
        $shipmentId = $request->input('shipmentId');

        if (empty($shiftId) || empty($templateId)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Validation failed',
                'errors' => [
                    'shiftId' => 'The shiftId field is required.',
                    'templateId' => 'The templateId field is required.',
                ],
            ], 422);
        }

        $getTemplateDetails = $this->routeTemplateModel->where('template_id', $templateId)->first();
        if (!$getTemplateDetails) {
            return response()->json([
                'status' => 'error',
                'message' => 'Trip template not found',
                'data' => [],
            ], 404);
        }

        $getTemplateLegsData = $this->routeTemplateLegModel->where('routetemplate_id', $getTemplateDetails->id)->get();
        $daysCount = $getTemplateLegsData->count();

        $this->updateNormalTripAsCrossBorderTrip($getTemplateLegsData->toArray(), $shiftId, $getTemplateDetails->toArray());

        $saveLegs = $this->tripCreateFromOrdersModel->saveTripLegsForShiftId([
            'mainShiftId' => $shiftId,
            'customerId' => $getTemplateDetails->customer_id ?? 0,
            'shiftCreateDate' => $currentDate,
            'shipmentId' => $shipmentId,
            'legsCount' => $daysCount,
            'carrierType' => $getTemplateDetails->carrier_type ?? '',
            'shipmentType' => $getTemplateDetails->shipment_type ?? '',
            'date' => $currentDate,
        ], $getTemplateLegsData->toArray(), $templateId);

        if ($saveLegs) {
            return response()->json([
                'status' => 'success',
                'message' => 'Trip updated successfully with template',
                'data' => [
                    'shiftId' => $shiftId,
                    'templateId' => $getTemplateDetails->template_id,
                    'templateName' => $getTemplateDetails->template_name,
                ],
                'debug' => [
                    'template_found' => true,
                    'template_name' => $getTemplateDetails->template_name,
                    'legs_processed' => $daysCount,
                ]
            ], 200);
        }

        return response()->json([
            'status' => 'error',
            'message' => 'Failed to update trip with template',
            'data' => [],
            'debug' => [
                'template_found' => true,
                'template_name' => $getTemplateDetails->template_name,
                'legs_processed' => $daysCount,
            ]
        ], 422);
    }

    private function updateNormalTripAsCrossBorderTrip(array $legsData, $shiftId, array $tripsData)
    {
        $shipmentOriginRowId = 0;
        $shipmentDestinationRowId = 0;

        foreach ($legsData as $index => $leg) {
            $legRowId = $leg['id'] ?? 0;
            if ($legRowId > 0) {
                if ($index === 0) {
                    $shipmentOriginRowId = $leg['origin_id'];
                }
                $shipmentDestinationRowId = $leg['destination_id'];
            }
        }

        if ($shipmentOriginRowId === 0 || $shipmentDestinationRowId === 0) {
            Log::error('Invalid origin or destination ID for shiftId: ' . $shiftId);
            return;
        }

        $originData = $this->partyModel->find($shipmentOriginRowId);
        $destinationData = $this->partyModel->find($shipmentDestinationRowId);

        if (!$originData || !$destinationData) {
            Log::error('Party master data not found for shiftId: ' . $shiftId);
            return;
        }

        $updateData = [
            'carrier_type' => $tripsData['carrier_type'],
            'border_type' => $tripsData['shipment_type'],
            'origin_id' => $originData->code,
            'destination_id' => $destinationData->code,
            'splace' => $originData->city,
            'eplace' => $destinationData->city,
            'scity' => $originData->city,
            'dcity' => $destinationData->city,
            'slat' => $originData->latitude,
            'slng' => $originData->longitude,
            'elat' => $destinationData->latitude,
            'elng' => $destinationData->longitude,
            'updated_on' => now()->timezone(config('app.timezone', 'Asia/Kolkata'))->format('Y-m-d H:i:s'),
        ];

        $updated = $this->shiftModel->where('id', $shiftId)->update($updateData);
        if (!$updated) {
            Log::error('Failed to update shift data for shiftId: ' . $shiftId);
        }
    }

    public function getMasterDataForTripTemplate(Request $request)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = $request->input('org_id') ?? 'RUKN'; // Default for testing
        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'The org_id field is required.',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $code = $request->input('code');
        if (empty($code)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Code is required',
                'data' => [],
            ], 400);
        }

        $getCrossBorderCompanies = $this->xborderCountryModel->where('org_id', $org_id)->first();

        if (!$getCrossBorderCompanies) {
            $crossBorderCompanyCodes = [$org_id];
        } else {
            $countriesString = $getCrossBorderCompanies->xborder_code ?? '';
            $crossBorderCompanyCodes = !empty($countriesString) ? explode(',', $countriesString) : [];
            $crossBorderCompanyCodes[] = $org_id;
        }

        $partyDetails = $this->partyModel->whereIn('org_id', $crossBorderCompanyCodes)
            ->where('code', $code)
            ->where('party_type', $request->input('partyType', 'origin'))
            ->get();

        return response()->json([
            'status' => 'success',
            'data' => $partyDetails,
        ], 200);
    }

    public function getWaypointParties(Request $request)
    {
        // Commented out auth for testing
        // $user = Auth::user();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //     ], 401);
        // }

        // Get org_id from request for testing, fallback to user if available
        $org_id = $request->input('org_id') ?? 'RUKN'; // Default for testing
        if (empty($org_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'The org_id field is required.',
                'errors' => ['org_id' => 'The org_id field is required.'],
            ], 422);
        }

        $legRowId = $request->input('legRowId') && $request->input('legRowId') > 0 ? (int) $request->input('legRowId') : 0;

        $getPartyMasterData = $this->partyModel->where('org_id', $org_id)->get();

        $existingWaypoints = [];
        if ($legRowId > 0) {
            $waypoints = $this->waypointRouteTemplateModel->where('leg_row_id', $legRowId)->pluck('party_id')->toArray();
            $existingWaypoints = $waypoints;
        }

        if ($getPartyMasterData->isNotEmpty()) {
            $parties = $getPartyMasterData->map(function ($eachLine) use ($existingWaypoints) {
                $isChecked = in_array($eachLine->id, $existingWaypoints) ? ' checked' : '';
                $checkBox = "<input type='checkbox' name='waypointParties[]' id='waypoint_{$eachLine->id}' class='masterlist' value='{$eachLine->id}'{$isChecked}>";
                return [
                    'check' => $checkBox,
                    'id' => $eachLine->code,
                    'name' => $eachLine->name,
                    'email' => $eachLine->code, // As per original, using code as email
                    'mobile' => $eachLine->location_id,
                    'country' => $eachLine->country,
                    'state' => $eachLine->state,
                    'street' => $eachLine->street,
                    'city' => $eachLine->pincode, // As per original, using pincode as city
                    'org_id' => $eachLine->org_id,
                    'be_value' => $eachLine->be_value,
                ];
            })->toArray();

            return response()->json([
                'status' => 'success',
                'data' => $parties,
            ], 200);
        }

        return response()->json([
            'status' => 'success',
            'data' => [],
            'message' => 'No parties found',
        ], 200);
    }
}

?>