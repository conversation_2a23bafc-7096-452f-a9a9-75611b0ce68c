<?php

namespace App\Http\Controllers;

use App\Models\NotifyManagement;
use App\Models\SxPartyMembers;
use App\Models\DepartmentMaster;
use App\Models\CountryMaster;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
// use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Schema;
// use Carbon\Carbon;

class NotifymgmtController extends Controller
{
    public function __construct()
    {
        // Authentication will be handled in each method
    }

    /**
     * Display a listing of notification management records.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            // $org_id = $user->org_id;
            // $be_value = $user->be_value;
            // $user_id = $user->id;
            
            // Temporary hardcoded values for testing
            $org_id = 44;
            $be_value = 1;
            $user_id = 1;

            $query = NotifyManagement::query()
                ->where('user_id', $user_id)
                ->where('org_id', $org_id);

            // Apply filters based on request
            if ($request->isMethod('post') && $request->input('searchsubmit') === 'Search') {
                $post = $request->all();
                
                if (!empty($post['customer_code'])) {
                    $query->whereHas('customer', function($q) use ($post) {
                        $q->where('code', $post['customer_code']);
                    });
                }

                if (!empty($post['service'])) {
                    $query->where('service', $post['service']);
                }

                if (!empty($post['product'])) {
                    $query->where('product', $post['product']);
                }

                if (!empty($post['incoterm'])) {
                    $query->where('inco_term', $post['incoterm']);
                }

                if (!empty($post['order_type'])) {
                    $query->where('order_type', $post['order_type']);
                }

                if (!empty($post['notification_id'])) {
                    $query->where('notification_id', $post['notification_id']);
                }

                if (!empty($post['org_id'])) {
                    $query->where('org_id', $post['org_id']);
                }

                if (!empty($post['be_value'])) {
                    $query->where('be_value', $post['be_value']);
                }

                if (!empty($post['user_id'])) {
                    $query->where('user_id', $post['user_id']);
                }

                if (!empty($post['from_country'])) {
                    $query->where('from_country', $post['from_country']);
                }

                if (!empty($post['to_country'])) {
                    $query->where('to_country', $post['to_country']);
                }
            }

            $notifications = $query->with(['customer', 'party', 'department'])->get();

            $mappedNotifications = $notifications->map(fn($row) => [
                'id' => $row->id,
                'notification_id' => $row->notification_id,
                'org_id' => $row->org_id,
                'be_value' => $row->be_value,
                'user_id' => $row->user_id,
                'customer_name' => $row->customer ? $row->customer->name : '',
                'order_type' => $row->order_type,
                'service' => $row->service,
                'product' => $row->product,
                'inco_term' => $row->inco_term,
                'from_country' => $row->from_country,
                'to_country' => $row->to_country,
                'status' => $row->status,
                'created_at' => $row->created_at,
                'updated_at' => $row->updated_at,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Notification management records retrieved successfully.',
                'data' => $mappedNotifications,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching notification management records', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch notification management records.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    /**
     * Show the form for creating a new notification management record.
     */
    public function create(Request $request): JsonResponse
    {
        try {
            // Temporary hardcoded values for testing
            $org_id = 44;
            $be_value = 1;
            $user_id = 1;

            // Get countries for dropdown
            $countries = CountryMaster::select('id', 'country_name', 'country_code')
                ->where('status', 1)
                ->orderBy('country_name')
                ->get();

            // Get departments for dropdown
            $departments = DepartmentMaster::select('id', 'department_name', 'be_value')
                ->where('org_id', $org_id)
                ->where('be_value', $be_value)
                ->where('status', 1)
                ->orderBy('department_name')
                ->get();

            // Get party types
            $partyTypes = SxPartyMembers::select('id', 'name', 'party_type')
                ->where('user_id', $user_id)
                ->where('status', 1)
                ->orderBy('name')
                ->get();

            return response()->json([
                'status' => 'success',
                'message' => 'Notification management creation form data retrieved successfully.',
                'data' => [
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'user_id' => $user_id,
                    'countries' => $countries,
                    'departments' => $departments,
                    'party_types' => $partyTypes,
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error getting notification management creation form data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get notification management creation form data.',
            ], 500);
        }
    }

    /**
     * Store a newly created notification management record.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            // Test database connectivity
            try {
                DB::connection()->getPdo();
                Log::info('Database connection successful');
            } catch (\Exception $e) {
                Log::error('Database connection failed', [
                    'error' => $e->getMessage()
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Database connection failed: ' . $e->getMessage(),
                ], 500);
            }

            // Check if table exists
            try {
                $tableExists = Schema::hasTable('notify_management');
                Log::info('Table check result', ['exists' => $tableExists]);
                if (!$tableExists) {
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Table notify_management does not exist. Please run migrations.',
                    ], 500);
                }
            } catch (\Exception $e) {
                Log::error('Table check failed', [
                    'error' => $e->getMessage()
                ]);
                return response()->json([
                    'status' => 'error',
                    'message' => 'Table check failed: ' . $e->getMessage(),
                ], 500);
            }
            // Commented out validation for testing
            /*
            $validator = Validator::make($request->all(), [
                'org_id' => 'required|string',
                'be_value' => 'required|string',
                'notify_product' => 'nullable|string',
                'notify_incoterm' => 'nullable|string',
                'notify_service' => 'nullable|string',
                'notify_order_type' => 'nullable|string',
                'notify_from_country' => 'required|string',
                'notify_to_country' => 'required|string',
                'notify_customer_id' => 'nullable|string',
                'count' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors(),
                ], 422);
            }
            */

            // Temporary hardcoded values for testing
            $user_id = 1;

            $input = [
                'user_id' => $user_id,
                'org_id' => $request->input('org_id'),
                'be_value' => $request->input('be_value'),
                'product' => $request->input('notify_product') == '0' ? '' : $request->input('notify_product'),
                'inco_term' => $request->input('notify_incoterm') == '0' ? '' : $request->input('notify_incoterm'),
                'service' => $request->input('notify_service') == '0' ? '' : $request->input('notify_service'),
                'order_type' => $request->input('notify_order_type') == '0' ? '' : $request->input('notify_order_type'),
                'from_country' => $request->input('notify_from_country'),
                'to_country' => $request->input('notify_to_country'),
                'party_id' => 0,
                'partycontact_id' => 0,
                'all_note' => false,
                'customer_id' => 0,
                'createdby' => $user_id,
                'updatedby' => $user_id,
            ];

            // Get customer ID if provided
            if (!empty($request->input('notify_customer_id'))) {
                $customer = SxPartyMembers::where('code', $request->input('notify_customer_id'))->first();
                if ($customer) {
                    $input['customer_id'] = $customer->id;
                }
            }

            // Generate notification ID if not provided
            if (empty($request->input('notification_id'))) {
                $input['notification_id'] = $this->generateNotifyID();
            }

            $insertedIds = [];
            $count = $request->input('count', 1); // Default to 1 if count is not provided

            Log::info('Store method debug', [
                'count' => $count,
                'request_data' => $request->all()
            ]);

            // If count is 0 or not provided, create at least one record
            if ($count <= 0) {
                $count = 1;
            }

            for ($i = 0; $i < $count; $i++) {
                Log::info("Processing iteration {$i}");
                
                $role = $request->input("role{$i}", 'default_role');
                $input['party_type'] = str_replace(' ', '', $role);
                $primaryId = $request->input("role{$i}_primaryid", 0);
                $input['party_type_id'] = $request->input("role{$i}_partyid", 0);
                $input['booking_create'] = $request->has("{$role}_bookingcreate");
                $input['booking_edit'] = $request->has("{$role}_bookingedit");
                $input['booking_delete'] = $request->has("{$role}_bookingdelete");
                $input['trip_create'] = $request->has("{$role}_tripcreate");
                $input['trip_edit'] = $request->has("{$role}_tripedit");
                $input['trip_delete'] = $request->has("{$role}_tripdelete");
                $input['driver_accept'] = $request->has("{$role}_driveraccept");
                $input['route_deviate'] = $request->has("{$role}_routedeviate");
                $input['speed'] = $request->has("{$role}_speed");
                $input['temperature'] = $request->has("{$role}_temperature");
                $input['pickup_note'] = $request->has("{$role}_pickupnote");
                $input['delivery_note'] = $request->has("{$role}_deliverynote");
                $input['pod_note'] = $request->has("{$role}_podnote");
                $input['sms_note'] = $request->has("{$role}_smsnote");
                $input['email_note'] = $request->has("{$role}_emailnote");
                $input['whatsapp_note'] = $request->has("{$role}_whatsappnote");

                Log::info("Input data for iteration {$i}", [
                    'role' => $role,
                    'party_type' => $input['party_type'],
                    'primaryId' => $primaryId,
                    'input' => $input
                ]);

                if ($primaryId == 0) {
                    try {
                        $notification = NotifyManagement::create($input);
                        $insertedIds[] = $notification->id;
                        Log::info('Record created successfully', [
                            'id' => $notification->id,
                            'input' => $input
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error creating record', [
                            'error' => $e->getMessage(),
                            'input' => $input
                        ]);
                        throw $e;
                    }
                } else {
                    try {
                        NotifyManagement::where('id', $primaryId)->update($input);
                        $insertedIds[] = $primaryId;
                        Log::info('Record updated successfully', [
                            'id' => $primaryId,
                            'input' => $input
                        ]);
                    } catch (\Exception $e) {
                        Log::error('Error updating record', [
                            'error' => $e->getMessage(),
                            'input' => $input
                        ]);
                        throw $e;
                    }
                }
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Notification management record created successfully.',
                'data' => [
                    'inserted_ids' => $insertedIds,
                    'notification_id' => $input['notification_id'],
                    'input_data' => $input,
                    'request_data' => $request->all(),
                ],
            ], 201);
        } catch (\Exception $e) {
            Log::error('Error creating notification management record', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create notification management record.',
                'debug' => $e->getMessage(),
            ], 500);
        }
    }

    public function show($id): JsonResponse
    {
        try {
            // First check if record exists without filters
            $notification = NotifyManagement::where('id', $id)
                ->with(['customer', 'party'])
                ->first();

            if (!$notification) {
                // Log for debugging
                Log::info('Record not found by ID', ['id' => $id]);
                
                // Check if any records exist in the table
                $totalRecords = NotifyManagement::count();
                Log::info('Total records in table', ['count' => $totalRecords]);
                
                // Get a sample record to see the structure
                $sampleRecord = NotifyManagement::first();
                if ($sampleRecord) {
                    Log::info('Sample record structure', [
                        'id' => $sampleRecord->id,
                        'org_id' => $sampleRecord->org_id,
                        'be_value' => $sampleRecord->be_value,
                        'user_id' => $sampleRecord->user_id
                    ]);
                }

                return response()->json([
                    'status' => 'error',
                    'message' => 'Notification management record not found.',
                    'debug' => [
                        'requested_id' => $id,
                        'total_records' => $totalRecords,
                        'sample_record' => $sampleRecord ? [
                            'id' => $sampleRecord->id,
                            'org_id' => $sampleRecord->org_id,
                            'be_value' => $sampleRecord->be_value,
                            'user_id' => $sampleRecord->user_id
                        ] : null
                    ]
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Notification management record retrieved successfully.',
                'data' => [
                    'id' => $notification->id,
                    'notification_id' => $notification->notification_id,
                    'user_id' => $notification->user_id,
                    'org_id' => $notification->org_id,
                    'be_value' => $notification->be_value,
                    'product' => $notification->product,
                    'inco_term' => $notification->inco_term,
                    'service' => $notification->service,
                    'order_type' => $notification->order_type,
                    'from_country' => $notification->from_country,
                    'to_country' => $notification->to_country,
                    'party_id' => $notification->party_id,
                    'partycontact_id' => $notification->partycontact_id,
                    'all_note' => $notification->all_note,
                    'customer_id' => $notification->customer_id,
                    'party_type' => $notification->party_type,
                    'party_type_id' => $notification->party_type_id,
                    'booking_create' => $notification->booking_create,
                    'booking_edit' => $notification->booking_edit,
                    'booking_delete' => $notification->booking_delete,
                    'trip_create' => $notification->trip_create,
                    'trip_edit' => $notification->trip_edit,
                    'trip_delete' => $notification->trip_delete,
                    'driver_accept' => $notification->driver_accept,
                    'route_deviate' => $notification->route_deviate,
                    'speed' => $notification->speed,
                    'temperature' => $notification->temperature,
                    'pickup_note' => $notification->pickup_note,
                    'delivery_note' => $notification->delivery_note,
                    'pod_note' => $notification->pod_note,
                    'sms_note' => $notification->sms_note,
                    'email_note' => $notification->email_note,
                    'whatsapp_note' => $notification->whatsapp_note,
                    'createdby' => $notification->createdby,
                    'updatedby' => $notification->updatedby,
                    'status' => $notification->status,
                    'created_at' => $notification->created_at,
                    'updated_at' => $notification->updated_at,
                    'customer' => $notification->customer,
                    'party' => $notification->party,
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching notification management record', [
                'error' => $e->getMessage(),
                'id' => $id,
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch notification management record.',
                'debug' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified notification management record.
     */
    public function edit($id): JsonResponse
    {
        try {
            // First check if record exists without filters
            $notification = NotifyManagement::where('id', $id)
                ->with(['customer', 'party'])
                ->first();

            if (!$notification) {
                // Log for debugging
                Log::info('Record not found by ID in edit', ['id' => $id]);
                
                // Check if any records exist in the table
                $totalRecords = NotifyManagement::count();
                Log::info('Total records in table', ['count' => $totalRecords]);
                
                // Get a sample record to see the structure
                $sampleRecord = NotifyManagement::first();
                if ($sampleRecord) {
                    Log::info('Sample record structure', [
                        'id' => $sampleRecord->id,
                        'org_id' => $sampleRecord->org_id,
                        'be_value' => $sampleRecord->be_value,
                        'user_id' => $sampleRecord->user_id
                    ]);
                }

                return response()->json([
                    'status' => 'error',
                    'message' => 'Notification management record not found.',
                    'debug' => [
                        'requested_id' => $id,
                        'total_records' => $totalRecords,
                        'sample_record' => $sampleRecord ? [
                            'id' => $sampleRecord->id,
                            'org_id' => $sampleRecord->org_id,
                            'be_value' => $sampleRecord->be_value,
                            'user_id' => $sampleRecord->user_id
                        ] : null
                    ]
                ], 404);
            }

            // Get the actual values from the found record
            $org_id = $notification->org_id;
            $be_value = $notification->be_value;
            $user_id = $notification->user_id;

            // Get countries for dropdown
            $countries = CountryMaster::select('id', 'country_name', 'country_code')
                ->where('status', 1)
                ->orderBy('country_name')
                ->get();

            // Get departments for dropdown
            $departments = DepartmentMaster::select('id', 'department_name', 'be_value')
                ->where('org_id', $org_id)
                ->where('be_value', $be_value)
                ->where('status', 1)
                ->orderBy('department_name')
                ->get();

            // Get party types
            $partyTypes = SxPartyMembers::select('id', 'name', 'party_type')
                ->where('user_id', $user_id)
                ->where('status', 1)
                ->orderBy('name')
                ->get();

            // Get notification list for this notification ID
            $notificationList = NotifyManagement::where('notification_id', $notification->notification_id)
                ->with(['customer', 'party'])
                ->get();

            return response()->json([
                'status' => 'success',
                'message' => 'Notification management edit form data retrieved successfully.',
                'data' => [
                    'notification' => [
                        'id' => $notification->id,
                        'notification_id' => $notification->notification_id,
                        'user_id' => $notification->user_id,
                        'org_id' => $notification->org_id,
                        'be_value' => $notification->be_value,
                        'product' => $notification->product,
                        'inco_term' => $notification->inco_term,
                        'service' => $notification->service,
                        'order_type' => $notification->order_type,
                        'from_country' => $notification->from_country,
                        'to_country' => $notification->to_country,
                        'party_id' => $notification->party_id,
                        'partycontact_id' => $notification->partycontact_id,
                        'all_note' => $notification->all_note,
                        'customer_id' => $notification->customer_id,
                        'party_type' => $notification->party_type,
                        'party_type_id' => $notification->party_type_id,
                        'booking_create' => $notification->booking_create,
                        'booking_edit' => $notification->booking_edit,
                        'booking_delete' => $notification->booking_delete,
                        'trip_create' => $notification->trip_create,
                        'trip_edit' => $notification->trip_edit,
                        'trip_delete' => $notification->trip_delete,
                        'driver_accept' => $notification->driver_accept,
                        'route_deviate' => $notification->route_deviate,
                        'speed' => $notification->speed,
                        'temperature' => $notification->temperature,
                        'pickup_note' => $notification->pickup_note,
                        'delivery_note' => $notification->delivery_note,
                        'pod_note' => $notification->pod_note,
                        'sms_note' => $notification->sms_note,
                        'email_note' => $notification->email_note,
                        'whatsapp_note' => $notification->whatsapp_note,
                        'createdby' => $notification->createdby,
                        'updatedby' => $notification->updatedby,
                        'status' => $notification->status,
                        'created_at' => $notification->created_at,
                        'updated_at' => $notification->updated_at,
                        'customer' => $notification->customer,
                        'party' => $notification->party,
                    ],
                    'notification_list' => $notificationList,
                    'countries' => $countries,
                    'departments' => $departments,
                    'party_types' => $partyTypes,
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error getting notification management edit form data', [
                'error' => $e->getMessage(),
                'id' => $id,
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get notification management edit form data.',
                'debug' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update the specified notification management record.
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            // Commented out validation for testing
            /*
            $validator = Validator::make($request->all(), [
                'org_id' => 'required|string',
                'be_value' => 'required|string',
                'notify_product' => 'nullable|string',
                'notify_incoterm' => 'nullable|string',
                'notify_service' => 'nullable|string',
                'notify_order_type' => 'nullable|string',
                'notify_from_country' => 'required|string',
                'notify_to_country' => 'required|string',
                'notify_customer_id' => 'nullable|string',
                'count' => 'required|integer|min:1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors(),
                ], 422);
            }
            */

            // Get the existing record first
            $existingNotification = NotifyManagement::where('id', $id)->first();
            if (!$existingNotification) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Notification management record not found.',
                    'debug' => [
                        'requested_id' => $id,
                        'total_records' => NotifyManagement::count()
                    ]
                ], 404);
            }

            Log::info('Updating existing record', [
                'id' => $id,
                'existing_record' => $existingNotification->toArray()
            ]);

            // Use the existing record's user_id or default to 1
            $user_id = $existingNotification->user_id ?? 1;

            $input = [
                'user_id' => $user_id,
                'org_id' => $request->input('org_id'),
                'be_value' => $request->input('be_value'),
                'product' => $request->input('notify_product') == '0' ? '' : $request->input('notify_product'),
                'inco_term' => $request->input('notify_incoterm') == '0' ? '' : $request->input('notify_incoterm'),
                'service' => $request->input('notify_service') == '0' ? '' : $request->input('notify_service'),
                'order_type' => $request->input('notify_order_type') == '0' ? '' : $request->input('notify_order_type'),
                'from_country' => $request->input('notify_from_country'),
                'to_country' => $request->input('notify_to_country'),
                'party_id' => 0,
                'partycontact_id' => 0,
                'all_note' => false,
                'customer_id' => 0,
                'updatedby' => $user_id,
            ];

            // Get customer ID if provided
            if (!empty($request->input('notify_customer_id'))) {
                $customer = SxPartyMembers::where('code', $request->input('notify_customer_id'))->first();
                if ($customer) {
                    $input['customer_id'] = $customer->id;
                }
            }

            // Use existing notification_id or generate new one if not provided
            if (empty($request->input('notification_id'))) {
                $input['notification_id'] = $existingNotification->notification_id ?? $this->generateNotifyID();
            } else {
                $input['notification_id'] = $request->input('notification_id');
            }

            $count = $request->input('count', 1); // Default to 1 if count is not provided

            Log::info('Update method debug', [
                'count' => $count,
                'request_data' => $request->all(),
                'input_data' => $input
            ]);

            // If count is 0 or not provided, update at least one record
            if ($count <= 0) {
                $count = 1;
            }

            // Update the existing record with the provided ID
            for ($i = 0; $i < $count; $i++) {
                Log::info("Processing update iteration {$i}");
                
                $role = $request->input("role{$i}", 'default_role');
                $input['party_type'] = str_replace(' ', '', $role);
                $input['party_type_id'] = $request->input("role{$i}_partyid", 0);
                $input['booking_create'] = $request->has("{$role}_bookingcreate");
                $input['booking_edit'] = $request->has("{$role}_bookingedit");
                $input['booking_delete'] = $request->has("{$role}_bookingdelete");
                $input['trip_create'] = $request->has("{$role}_tripcreate");
                $input['trip_edit'] = $request->has("{$role}_tripedit");
                $input['trip_delete'] = $request->has("{$role}_tripdelete");
                $input['driver_accept'] = $request->has("{$role}_driveraccept");
                $input['route_deviate'] = $request->has("{$role}_routedeviate");
                $input['speed'] = $request->has("{$role}_speed");
                $input['temperature'] = $request->has("{$role}_temperature");
                $input['pickup_note'] = $request->has("{$role}_pickupnote");
                $input['delivery_note'] = $request->has("{$role}_deliverynote");
                $input['pod_note'] = $request->has("{$role}_podnote");
                $input['sms_note'] = $request->has("{$role}_smsnote");
                $input['email_note'] = $request->has("{$role}_emailnote");
                $input['whatsapp_note'] = $request->has("{$role}_whatsappnote");

                Log::info("Update input data for iteration {$i}", [
                    'role' => $role,
                    'party_type' => $input['party_type'],
                    'input' => $input
                ]);

                try {
                    // Update the existing record with the provided ID
                    $updated = NotifyManagement::where('id', $id)->update($input);
                    
                    if ($updated) {
                        Log::info('Record updated successfully', [
                            'id' => $id,
                            'input' => $input
                        ]);
                    } else {
                        Log::warning('No records were updated', [
                            'id' => $id,
                            'input' => $input
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::error('Error updating record', [
                        'error' => $e->getMessage(),
                        'id' => $id,
                        'input' => $input
                    ]);
                    throw $e;
                }
            }

            // Get the updated record to show in response
            $updatedRecord = NotifyManagement::where('id', $id)
                ->with(['customer', 'party'])
                ->first();

            if (!$updatedRecord) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to retrieve updated record.',
                ], 500);
            }

            $updatedData = [
                'id' => $updatedRecord->id,
                'notification_id' => $updatedRecord->notification_id,
                'user_id' => $updatedRecord->user_id,
                'org_id' => $updatedRecord->org_id,
                'be_value' => $updatedRecord->be_value,
                'product' => $updatedRecord->product,
                'inco_term' => $updatedRecord->inco_term,
                'service' => $updatedRecord->service,
                'order_type' => $updatedRecord->order_type,
                'from_country' => $updatedRecord->from_country,
                'to_country' => $updatedRecord->to_country,
                'party_id' => $updatedRecord->party_id,
                'partycontact_id' => $updatedRecord->partycontact_id,
                'all_note' => $updatedRecord->all_note,
                'customer_id' => $updatedRecord->customer_id,
                'party_type' => $updatedRecord->party_type,
                'party_type_id' => $updatedRecord->party_type_id,
                'booking_create' => $updatedRecord->booking_create,
                'booking_edit' => $updatedRecord->booking_edit,
                'booking_delete' => $updatedRecord->booking_delete,
                'trip_create' => $updatedRecord->trip_create,
                'trip_edit' => $updatedRecord->trip_edit,
                'trip_delete' => $updatedRecord->trip_delete,
                'driver_accept' => $updatedRecord->driver_accept,
                'route_deviate' => $updatedRecord->route_deviate,
                'speed' => $updatedRecord->speed,
                'temperature' => $updatedRecord->temperature,
                'pickup_note' => $updatedRecord->pickup_note,
                'delivery_note' => $updatedRecord->delivery_note,
                'pod_note' => $updatedRecord->pod_note,
                'sms_note' => $updatedRecord->sms_note,
                'email_note' => $updatedRecord->email_note,
                'whatsapp_note' => $updatedRecord->whatsapp_note,
                'createdby' => $updatedRecord->createdby,
                'updatedby' => $updatedRecord->updatedby,
                'status' => $updatedRecord->status,
                'created_at' => $updatedRecord->created_at,
                'updated_at' => $updatedRecord->updated_at,
                'customer' => $updatedRecord->customer,
                'party' => $updatedRecord->party,
            ];

            return response()->json([
                'status' => 'success',
                'message' => 'Notification management record updated successfully.',
                'data' => [
                    'updated_id' => $id,
                    'updated_record' => $updatedData,
                    'input_data' => $input,
                    'request_data' => $request->all(),
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error updating notification management record', [
                'error' => $e->getMessage(),
                'id' => $id,
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update notification management record.',
                'debug' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified notification management record.
     */
    public function destroy($id): JsonResponse
    {
        try {
            Log::info('Attempting to delete record', [
                'id' => $id,
                'total_records' => NotifyManagement::count()
            ]);

            // Find the record by ID only (remove hardcoded filters)
            $notification = NotifyManagement::where('id', $id)->first();

            if (!$notification) {
                Log::warning('Record not found for deletion', [
                    'id' => $id,
                    'all_records' => NotifyManagement::select('id', 'org_id', 'be_value', 'notification_id')->get()->toArray()
                ]);
                
                return response()->json([
                    'status' => 'error',
                    'message' => 'Notification management record not found.',
                    'debug' => [
                        'requested_id' => $id,
                        'total_records' => NotifyManagement::count(),
                        'available_ids' => NotifyManagement::pluck('id')->toArray()
                    ]
                ], 404);
            }

            Log::info('Found record for deletion', [
                'id' => $notification->id,
                'org_id' => $notification->org_id,
                'be_value' => $notification->be_value,
                'notification_id' => $notification->notification_id
            ]);

            // Soft delete by setting status to 0
            $updated = $notification->update(['status' => 0]);

            if ($updated) {
                Log::info('Record deleted successfully', [
                    'id' => $id,
                    'status' => 0
                ]);

                return response()->json([
                    'status' => 'success',
                    'message' => 'Notification management record deleted successfully.',
                    'data' => [
                        'deleted_id' => $id,
                        'notification_id' => $notification->notification_id,
                        'org_id' => $notification->org_id,
                        'be_value' => $notification->be_value
                    ]
                ], 200);
            } else {
                Log::warning('Failed to update record status', [
                    'id' => $id
                ]);

                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to delete notification management record.',
                    'debug' => 'Update operation returned false'
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error('Error deleting notification management record', [
                'error' => $e->getMessage(),
                'id' => $id,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete notification management record.',
                'debug' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification data based on filters.
     */
    public function getNotifyData(Request $request): JsonResponse
    {
        try {
            // Commented out validation for testing
            /*
            $validator = Validator::make($request->all(), [
                'org_id' => 'required|string',
                'be_value' => 'required|string',
                'notify_product' => 'required|string',
                'notify_service' => 'required|string',
                'notify_order_type' => 'required|string',
                'notify_incoterm' => 'required|string',
                'notify_from_country' => 'required|string',
                'notify_to_country' => 'required|string',
                'notify_customer_id' => 'nullable|string',
                'notification_id' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors(),
                ], 422);
            }
            */

            $where = [
                'org_id' => $request->input('org_id'),
                'be_value' => $request->input('be_value'),
                'product' => $request->input('notify_product'),
                'service' => $request->input('notify_service'),
                'order_type' => $request->input('notify_order_type'),
                'inco_term' => $request->input('notify_incoterm'),
                'from_country' => $request->input('notify_from_country'),
                'to_country' => $request->input('notify_to_country'),
            ];

            if (!empty($request->input('notification_id'))) {
                $where['notification_id'] = $request->input('notification_id');
            }

            if ($request->input('notify_customer_id')) {
                $customer = SxPartyMembers::where('code', $request->input('notify_customer_id'))->first();
                $where['customer_id'] = $customer ? $customer->id : 0;
            } else {
                $where['customer_id'] = 0;
            }

            $notifyData = NotifyManagement::getNotifyData($where);

            return response()->json($notifyData);
        } catch (\Exception $e) {
            Log::error('Error getting notification data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    /**
     * Check if combination already exists.
     */
    public function checkCombination(Request $request): JsonResponse
    {
        try {
            // Commented out validation for testing
            /*
            $validator = Validator::make($request->all(), [
                'org_id' => 'required|string',
                'be_value' => 'required|string',
                'notify_product' => 'required|string',
                'notify_service' => 'required|string',
                'notify_order_type' => 'required|string',
                'notify_incoterm' => 'required|string',
                'notify_from_country' => 'required|string',
                'notify_to_country' => 'required|string',
                'notify_customer_id' => 'nullable|string',
                'notification_id' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors(),
                ], 422);
            }
            */

            $where = [
                'org_id' => $request->input('org_id'),
                'be_value' => $request->input('be_value'),
                'product' => $request->input('notify_product'),
                'service' => $request->input('notify_service'),
                'order_type' => $request->input('notify_order_type'),
                'inco_term' => $request->input('notify_incoterm'),
                'from_country' => $request->input('notify_from_country'),
                'to_country' => $request->input('notify_to_country'),
            ];

            if (!empty($request->input('notification_id'))) {
                $where['notification_id !='] = $request->input('notification_id');
            }

            if ($request->input('notify_customer_id')) {
                $customer = SxPartyMembers::where('code', $request->input('notify_customer_id'))->first();
                $where['customer_id'] = $customer ? $customer->id : 0;
            } else {
                $where['customer_id'] = 0;
            }

            $result = NotifyManagement::where($where)->exists();

            return response()->json($result ? 'existed' : 'Not existed');
        } catch (\Exception $e) {
            Log::error('Error checking combination', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json('existed', 500);
        }
    }

    /**
     * Delete notification by ID.
     */
    public function deleteNotify($id): JsonResponse
    {
        try {
            if (empty($id) || $id == 0) {
                return response()->json('0');
            }

            $notification = NotifyManagement::where('id', $id)->first();
            if (!$notification) {
                return response()->json('0');
            }

            $result = NotifyManagement::where('notification_id', $notification->notification_id)
                ->update(['status' => 0]);

            return response()->json($result ? '1' : '0');
        } catch (\Exception $e) {
            Log::error('Error deleting notification', [
                'error' => $e->getMessage(),
                'id' => $id,
            ]);
            return response()->json('0');
        }
    }

    /**
     * Get customer notification data.
     */
    public function getCustomerNotifyData(Request $request): JsonResponse
    {
        try {
            $where = [];

            if ($request->input('customer_id')) {
                $where['pm.code'] = $request->input('customer_id');
            } else {
                $where['pm.code !='] = '';
            }

            if ($request->input('org_id')) {
                $where['pm.org_id'] = $request->input('org_id');
            }

            if ($request->input('be_value')) {
                $where['pm.be_value'] = $request->input('be_value');
            }

            $customerData = NotifyManagement::getCustomerNotifyData($where);

            $result = $customerData->map(function($item) {
                $item['partytype'] = ucwords($item['partytype']);
                return $item;
            });

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting customer notification data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    /**
     * Get notification IDs for autocomplete.
     */
    public function getNotificationIds(Request $request): JsonResponse
    {
        try {
            $term = $request->input('term', '');
            $list = NotifyManagement::getNotificationIds($term);
            return response()->json($list);
        } catch (\Exception $e) {
            Log::error('Error getting notification IDs', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    /**
     * Get organizations for autocomplete.
     */
    public function getOrganizations(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query', '');
            $result = [];

            if (!empty($query)) {
                $organizations = DB::table('organization_master')
                    ->where(function($q) use ($query) {
                        $q->where('organization_name', 'like', $query . '%')
                          ->orWhere('org_id', 'like', $query . '%');
                    })
                    ->where('status', 1)
                    ->pluck('org_id')
                    ->toArray();

                $result = $organizations;
            }

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting organizations', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    /**
     * Get customers for autocomplete.
     */
    public function getCustomers(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query', '');
            $result = [];

            if (!empty($query)) {
                $customers = SxPartyMembers::where(function($q) use ($query) {
                        $q->where('name', 'like', $query . '%')
                          ->orWhere('code', 'like', $query . '%');
                     })
                     ->where('status', 1)
                     ->pluck('code')
                     ->toArray();

                $result = $customers;
            }

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting customers', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    /**
     * Get customers list for autocomplete.
     */
    public function getCustomersList(Request $request): JsonResponse
    {
        try {
            $term = $request->input('term', '');
            $customers = NotifyManagement::getCustomers($term);
            return response()->json($customers);
        } catch (\Exception $e) {
            Log::error('Error getting customers list', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    /**
     * Get business entities for autocomplete.
     */
    public function getBusinessEntities(Request $request): JsonResponse
    {
        try {
            $query = $request->input('query', '');
            $org_id = $request->input('org_id', '');
            $result = [];

            if (!empty($query) && !empty($org_id)) {
                $businessEntities = DB::table('business_entity_master')
                    ->where(function($q) use ($query) {
                        $q->where('business_entity_name', 'like', $query . '%')
                          ->orWhere('be_value', 'like', $query . '%');
                    })
                    ->where('org_id', $org_id)
                    ->where('status', 1)
                    ->pluck('be_value')
                    ->toArray();

                $result = $businessEntities;
            }

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting business entities', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    /**
     * Generate notification ID.
     */
    private function generateNotifyID(): string
    {
        $latestId = NotifyManagement::getLatestNotifyId();
        $num = (int)substr($latestId, 6) + 1;
        $numLength = strlen((string)$num);
        
        if ($numLength == 1) {
            $finalId = 'NOTIFY00' . $num;
        } elseif ($numLength == 2) {
            $finalId = 'NOTIFY0' . $num;
        } else {
            $finalId = 'NOTIFY' . $num;
        }
        
        return $finalId;
    }

    /**
     * Test notification functionality.
     */
    public function test(): JsonResponse
    {
        try {
            // This is a test method from the original CI3 controller
            // You can implement actual notification testing logic here
            return response()->json([
                'status' => 'success',
                'message' => 'Test method called successfully.',
                'data' => [
                    'shift_id' => 866,
                    'action' => 'trip_create',
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in test method', [
                'error' => $e->getMessage(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Test method failed.',
            ], 500);
        }
    }

    /**
     * Test template functionality.
     */
    public function tpl(): JsonResponse
    {
        try {
            // This is a test method from the original CI3 controller
            // You can implement actual template testing logic here
            return response()->json([
                'status' => 'success',
                'message' => 'Template test method called successfully.',
                'data' => [
                    'pagetitle' => 'New Booking Created',
                    'receivename' => 'receivename',
                    'order_id' => 1754,
                    'orderid' => 63223456,
                    'shiftid' => 'TA123456',
                    'driver' => 'Driver',
                    'drivermobile' => 'DriverMobile',
                    'register_number' => '9876543210',
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in template test method', [
                'error' => $e->getMessage(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Template test method failed.',
            ], 500);
        }
    }

    /**
     * Test email functionality.
     */
    public function testMail(): JsonResponse
    {
        try {
            // This is a test method from the original CI3 controller
            // You can implement actual email testing logic here
            return response()->json([
                'status' => 'success',
                'message' => 'Email test method called successfully.',
                'data' => [
                    'to' => '<EMAIL>',
                    'from' => '<EMAIL>',
                    'subject' => 'ShipmentX :: Notify MGNT TESTING',
                    'body' => 'NOTIFY MGNT TESTING MAIL BODY',
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error in email test method', [
                'error' => $e->getMessage(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Email test method failed.',
            ], 500);
        }
    }
}
