<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Lara<PERSON>\Passport\RefreshTokenRepository;
use Illuminate\Support\Facades\Log;
use App\Models\SxOrganization;
use App\Models\SxUserOrganization;
use App\Models\SxPrevilleges;
use App\Models\SxStructureMaster;
use App\Models\SxPartyTypes;
use App\Models\SxPrevililegeTypes;


class AuthController extends Controller
{

    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string|unique:sx_users,username',
            'emailid' => 'required|email|unique:sx_users,emailid',
            'password' => 'required|string|min:6',
            'employee_id' => 'nullable|string',
            'employee_name' => 'nullable|string',
            'effective_from' => 'nullable|date_format:Y-m-d',
            'effective_to' => 'nullable|date_format:Y-m-d|after_or_equal:effective_from',
            'contact_num' => 'nullable|string',
            'theme_id' => 'nullable|integer',
            'currency' => 'nullable|string',
            'number_format' => 'nullable|string',
            'languages' => 'nullable|array',
            'date_format' => 'nullable|string',
            'org_id' => 'nullable|integer',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }
        try {
            $user = User::create([
                'username'           => $request->username,
                'emailid'            => $request->email,
                'password'           => $request->password,
                'employee_id'        => $request->employee_id,
                'employee_name'      => $request->employee_name,
                'effective_fromdate' => \Carbon\Carbon::parse($request->effective_from)->format('Y-m-d'),
                'effective_enddate'  => \Carbon\Carbon::parse($request->effective_to)->format('Y-m-d'),
                'contact_num'        => $request->contact_num,
                'theme_id'           => $request->theme_id,
                'currency'           => $request->currency,
                'number_format'      => $request->number_format,
                'languages'          => json_encode($request->languages ?? [], JSON_UNESCAPED_SLASHES),
                'date_format'        => $request->date_format,
                'default_org_id'     => $request->org_id,
            ]);

            $resp = response()->json([
                'status' => 'success',
                'message' => 'User registered successfully',
                'user' => $user->id,
            ], 201);
            return $resp;
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'User registration failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'username' => 'required|string',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $user = User::where('username', $request->username)->first();

        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json(['status' => 'fail', 'message' => 'Invalid credentials'], 401);
        }

        $tokenResult = $user->createToken('auth_token', ['*']);
        $accessToken = $tokenResult->accessToken;
        $refreshToken = $tokenResult->token->refresh_token ?? null;
        $defaultOrganization = SxOrganization::where('id', $user->default_org_id)->select('org_name', 'logo', 'email_id')->first();
        if (!$defaultOrganization) {
            return response()->json(['status' => 'fail', 'message' => 'Organization not found', 'status' => 0], 404);
        }
        if(!empty($refreshToken)){
            $user->refresh_token = $refreshToken; 
            $user->save();
        }
        $userOrganization = SxUserOrganization::where('user_id', $user->id)->where('org_id', $user->default_org_id)->first();
        if (!$userOrganization) {
            return response()->json(['status' => 'fail', 'message' => 'User organization not found', 'status' => 0], 404);
        }
        $defaultOrgPrivilegeIds = json_decode($userOrganization->roles, true) ?? [];

        $defaultOrgPrivileges = SxPrevilleges::whereIn('id', $defaultOrgPrivilegeIds)->whereNull('deleted_at')->orderBy('id', 'desc')->get()->toArray();
        if (empty($defaultOrgPrivileges)) {
            return response()->json(['status' => 'fail', 'message' => 'Privileges not found', 'status' => 0], 404);
        }

        $defaultOrgStructures = SxStructureMaster::where('org_id', $user->default_org_id)->where('id', $userOrganization->structure_id)->whereNull('deleted_at')->select('id')->get();
        if (empty($defaultOrgStructures)) {
            return response()->json(['status' => 'fail', 'message' => 'Organization structure not found', 'status' => 0], 404);
        }

        $isSuperUser = false;
        foreach ($defaultOrgPrivileges as $privilege) {
            if (strtolower(str_replace(' ', '', $privilege['previllege_name'])) === 'superuser') {
                $isSuperUser = true;
                break;
            }
        }

        $previlegeType = $defaultOrgPrivileges[0]['previlege_type'];
        $privilegeTypeData = SxPrevililegeTypes::where('id', $previlegeType)->select('type_name')->first();
        $usertype = $privilegeTypeData ? strtolower($privilegeTypeData->type_name) : null;
        $sxPartyType = '';
        if ($usertype && $usertype === 'party') {
            $partyTypeId = $defaultOrgPrivileges[0]['party_type'];
            $partyTypeData = SxPartyTypes::where('status', 1)->where('id', $partyTypeId)->select('type_name')->first();
            $sxPartyType = $partyTypeData ? $partyTypeData->type_name : '';
        }


        return response()->json([
            'status' => 'success',
            'user' => [
                'user_id' => $user->id,
                'emp_id' => $user->employee_id,
                'emp_name' => $user->employee_name,
                'org_id' => $user->default_org_id,
                'org_name' => $defaultOrganization->org_name,
                'companyname' => $defaultOrganization->org_name,
                'org_logo' => $defaultOrganization->logo,
                'email_id' => $defaultOrganization->email_id,
                'user_orgs' => $userOrganization->toArray(),
                'usertype' => $usertype,
                'default_org_privilege_ids' => $defaultOrgPrivilegeIds,
                'default_org_structures' => $defaultOrgStructures,
                'is_super_user' => $isSuperUser,
                'part_type' => $sxPartyType,
                'site_lang' => 'english',
                'map_type' => 1,
                'role_id' => $previlegeType,
                'previleges' => $defaultOrgPrivilegeIds,
                'distance_unit' => 'km'
            ],
            'access_token' => $accessToken,
            'refresh_token' => $refreshToken,
            'token_type' => 'Bearer',
            'expires_in' => config('passport.tokens_expire_in.hours', 1) * 3600,
        ]);
    }

    public function refresh(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'refresh_token' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $refreshTokenRepository = app(RefreshTokenRepository::class);
        $refreshToken = $refreshTokenRepository->find($request->refresh_token);

        if (!$refreshToken || $refreshToken->revoked || now()->gt($refreshToken->expires_at)) {
            return response()->json(['message' => 'Invalid or expired refresh token'], 401);
        }

        $user = User::find($refreshToken->token->user_id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $refreshTokenRepository->revokeRefreshToken($refreshToken->id);

        $tokenResult = $user->createToken('auth_token', ['*']);
        $newAccessToken = $tokenResult->accessToken;
        $newRefreshToken = $tokenResult->token->refresh_token ?? null;

        return response()->json([
            'user' => $user,
            'access_token' => $newAccessToken,
            'refresh_token' => $newRefreshToken,
            'token_type' => 'Bearer',
            'expires_in' => config('passport.tokens_expire_in.hours', 1) * 3600,
        ]);
    }

    public function logout(Request $request)
    {
        $request->user()->token()->revoke();
        app(RefreshTokenRepository::class)->revokeRefreshTokensByAccessTokenId($request->user()->token()->id);
        return response()->json(['message' => 'Successfully logged out']);
    }
}
