<?php

namespace App\Http\Controllers;

use App\Models\SxPartyMembers;
use App\Models\SxPartyTypes;
use App\Models\SxPartyReferences;
use App\Models\CountryMaster;
use App\Models\CompanyCategory;
use App\Models\Vendor;
use App\Models\Customer;
use App\Models\CustomerProfileList;
use App\Models\VendorProfileList;
use App\Models\Regions;
use App\Models\PartnerContact;
use App\Models\PartyReference;
use App\Models\ReferenceType;
use App\Models\PartnerAccounts;
use App\Models\PartnerContracts;
use App\Models\PartnerEdiApi;
use App\Models\TransactionAction;
use App\Models\OrderType;
use App\Models\Product;
use App\Models\ServiceMaster;
use App\Models\TransportMode;
use App\Models\EdiType;
use App\Models\IncoTerms;
use App\Models\StatusMaster;
use App\Models\PartyGroupType;
use App\Models\PartyGroup;
use App\Models\SxPartnerAccounts;
use App\Models\SxPartnerContacts;
use App\Models\SxPartnerContract;
use App\Models\SxPartnerEdiApi;
use App\Models\SxPartyGroups;
use App\Models\SxPartyGroupTypes;
use App\Models\VendorRule;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use SxOrders;

class MasterController extends Controller
{
    public function __construct()
    {
        // $this->middleware('auth:api');
    }

    /**
     * Log user activity
     */
    private function logActivity(array $data)
    {
        try {
            // Assuming a UserActivity model exists
            \App\Models\UserActivity::create([
                'user_id' => $data['user_id'],
                'description' => $data['description'],
                'createdon' => $data['createdon'],
                'status' => 1
            ]);
            Log::info('Activity logged: ' . $data['description']);
        } catch (\Exception $e) {
            Log::error('Failed to log activity: ' . $e->getMessage());
        }
    }

    /**
     * Display a listing of partners
     */
    public function partnerIndex(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $org_id = $user->org_id;

        $meta = [
            'countries' => CountryMaster::where('status', 1)->orderBy('country_name')->get(),
            'parttypes' => SxPartyTypes::where(['org_id' => $org_id, 'status' => 1])
                ->orderBy('type_name')
                ->get(),
            'business_entities' => DB::table('sx_business_entity_value')
                ->where(['org_id' => $org_id, 'status' => 1])
                ->orderBy('entity_id')
                ->get(),
        ];

        $where = [
            'org_id' => $org_id,
            'parent_id' => 0,
            'status' => 1,
        ];

        $exact = [
            'customeridentifier' => 'customeridentifier',
            'country' => 'country',
            'be_value' => 'be_value',
            'house_number' => 'house_number',
            'location_id' => 'location_id',
            'pincode' => 'pincode',
            'code' => 'code',
            'acon_debitor_code' => 'acon_debitor_code',
        ];

        foreach ($exact as $column => $param) {
            if ($request->has($param)) {
                $where[$column] = trim($request->input($param));
            }
        }

        if ($request->has('name')) {
            $where['name LIKE'] = '%' . trim($request->input('name')) . '%';
        }
        if ($request->has('street')) {
            $where['street LIKE'] = '%' . trim($request->input('street')) . '%';
        }
        if ($request->has('fromdate')) {
            $where["DATE_FORMAT(created_at,'%Y-%m-%d') >="] = date('Y-m-d', strtotime($request->input('fromdate')));
        }
        if ($request->has('todate')) {
            $where["DATE_FORMAT(created_at,'%Y-%m-%d') <="] = date('Y-m-d', strtotime($request->input('todate')));
        }

        $query = SxPartyMembers::where($where);

        if ($request->has('party_type')) {
            $filterTypes = is_array($request->input('party_type'))
                ? $request->input('party_type')
                : array_map('trim', explode(',', $request->input('party_type')));
            $typeIds = SxPartyTypes::whereIn('type_name', $filterTypes)
                ->pluck('id')
                ->toArray();

            if ($typeIds) {
                $query->where(function ($q) use ($typeIds) {
                    $q->whereIn('party_type', $typeIds)
                        ->orWhere(function ($subq) use ($typeIds) {
                            foreach ($typeIds as $id) {
                                $subq->orWhereRaw("FIND_IN_SET(?, party_types) > 0", [$id]);
                            }
                        });
                });
            }
        }

        $perPage = (int) ($request->input('per_page', 100));
        $page = max(1, (int) ($request->input('page', 1)));

        $partners = $query->orderBy('name')->paginate($perPage, ['*'], 'page', $page);

        $partyIds = [];
        foreach ($partners as $p) {
            $partyIds[] = $p->party_type;
            if ($p->party_types) {
                $partyIds = array_merge($partyIds, explode(',', $p->party_types));
            }
        }
        $partyIds = array_unique($partyIds);
        $typeNameById = $partyIds
            ? SxPartyTypes::whereIn('id', $partyIds)->pluck('type_name', 'id')->toArray()
            : [];

        $dynamicCountries = [];
        $dynamicBusinessEntities = [];
        $dynamicTypes = [];
        $outRows = [];

        foreach ($partners as $p) {
            $ids = array_unique(array_merge(
                [$p->party_type],
                $p->party_types ? explode(',', $p->party_types) : []
            ));
            $typeNames = [];
            foreach ($ids as $id) {
                if (isset($typeNameById[$id])) {
                    $typeNames[] = $typeNameById[$id];
                }
            }

            $dynamicCountries[] = $p->country;
            $dynamicBusinessEntities[] = $p->be_value;
            $dynamicTypes = array_merge($dynamicTypes, $typeNames);

            $outRows[] = [
                'id' => $p->id,
                'name' => $p->name,
                'location_id' => $p->location_id,
                'division_name' => $p->division_name,
                'sub_district' => $p->sub_district,
                'street' => $p->street,
                'street_2' => $p->street_2,
                'street_3' => $p->street_3,
                'house_number' => $p->house_number,
                'building' => $p->building,
                'country' => $p->country,
                'state' => $p->state,
                'be_value' => $p->be_value,
                'pincode' => $p->pincode,
                'code' => $p->code,
                'customer_code' => $p->customeridentifier,
                'acon_debitor_code' => $p->acon_debitor_code,
                'kn_login_account' => $p->kn_login_account,
                'offering_type' => $p->offering_type,
                'carrier_grade' => $p->carrier_grade,
                'party_types' => $p->party_types,
                'party_type_id' => $p->party_type,
                'bpartner_partytys' => $typeNames,
            ];
        }

        $excelPreview = [];
        if ($request->has('upload_preview')) {
            $tmp = Storage::disk('public')->exists('businesspartners/customersexceldata.txt')
                ? Storage::disk('public')->get('businesspartners/customersexceldata.txt')
                : '';
            if ($tmp) {
                $excelPreview = json_decode($tmp, true) ?: [];
                Storage::disk('public')->delete('businesspartners/customersexceldata.txt');
            }
        }

        return response()->json([
            'status' => 'success',
            'data' => array_merge($meta, [
                'partners' => $outRows,
                'dynamic_countries' => array_values(array_unique($dynamicCountries)),
                'dynamic_business_entities' => array_values(array_unique($dynamicBusinessEntities)),
                'dynamic_types' => array_values(array_unique($dynamicTypes)),
                'pagination' => [
                    'page' => $page,
                    'per_page' => $perPage,
                    'total' => $partners->total(),
                ],
                'excel_preview' => $excelPreview,
            ]),
        ]);
    }

    /**
     * Display the specified partner
     */
    public function partnerShow(Request $request, $id)
    {
        if (empty($id)) {
            return response()->json([
                'status' => 'fail',
                'error' => 'Invalid partner ID'
            ], 422);
        }

        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        try {
            $result = [];
            $org_id = $user->org_id;

            $result['parttypes'] = SxPartyTypes::select('id', 'type_name as name', 'description')
                ->where(['org_id' => $org_id, 'status' => 1])
                ->get();

            $bpartners = SxPartyMembers::where(['id' => $id, 'org_id' => $org_id])->first();
            if (!$bpartners) {
                return response()->json([
                    'status' => 'fail',
                    'error' => 'Party not found'
                ], 404);
            }

            $partyidTypes = array_unique(array_merge(
                $bpartners->party_types ? explode(',', $bpartners->party_types) : [],
                [$bpartners->party_type]
            ));
            $result['party_type_names'] = SxPartyTypes::select('id', 'type_name as name')
                ->where(['org_id' => $org_id])
                ->whereIn('id', $partyidTypes)
                ->get();

            $categoryId = $bpartners->category_id ?? 0;
            /* $result['categorydata'] = $categoryId > 0
                ? ['id' => $categoryId, 'name' => CompanyCategory::where('id', $categoryId)->value('name')]
                : ['id' => '', 'name' => '']; */

            $result['auto_accept'] = 0;
            $result['tms_auto_accept'] = 0;
            if (SxPartyTypes::whereIn('id', $partyidTypes)->where('type_name', 'Carrier')->exists()) {
                /* $vendor = Vendor::select('auto_accept', 'tms_auto_accept')
                    ->where(['partyid' => $id, 'code' => $bpartners->code, 'status' => 1])
                    ->first();
                if ($vendor) {
                    $result['auto_accept'] = $vendor->auto_accept ?? 0;
                    $result['tms_auto_accept'] = $vendor->tms_auto_accept ?? 0;
                } */
            }

            $result['Tarifftype'] = 0;
            $result['customer_id'] = '';
            if (SxPartyTypes::whereIn('id', $partyidTypes)->where('type_name', 'Customer')->exists()) {
                $customer = SxPartyMembers::select('id', 'Tarifftype')
                    ->where(['partyid' => $id, 'code' => $bpartners->code, 'status' => 1])
                    ->first();
                if ($customer) {
                    $result['customer_id'] = $customer->id;
                    $result['Tarifftype'] = $customer->Tarifftype ?? 0;
                }
            }

            /* $custProfile = CustomerProfileList::where('party_id', $id)->first();
            $result['customer_profile_id'] = $custProfile->cust_profile_id ?? '';
            $result['cus_profile_id'] = $custProfile->cp_id ?? '';

            $vendorProfile = CustomerProfileList::where('profile_id', trim($bpartners->code))->first();
            $result['vendor_profile_id'] = $vendorProfile->vend_profile_id ?? '';
            $result['ven_profile_id'] = $vendorProfile->vp_id ?? ''; */

            if(isset($bpartners->region) && !empty($bpartners->region)){
                $region = Regions::select('region_name')->where('id', $bpartners->region ?? 0)->first();
                $result['region'] = $region->region_name ?? '';
            }

            $result = array_merge($result, [
                'name' => $bpartners->name ?? '',
                'email' => $bpartners->email ?? '',
                'mobile' => $bpartners->mobile ?? '',
                'location_id' => $bpartners->location_id ?? '',
                'division_name' => $bpartners->division_name ?? '',
                'sub_district' => $bpartners->sub_district ?? '',
                'street' => $bpartners->street ?? '',
                'street_2' => $bpartners->street_2 ?? '',
                'street_3' => $bpartners->street_3 ?? '',
                'house_number' => $bpartners->house_number ?? '',
                'building' => $bpartners->building ?? '',
                'state' => $bpartners->state ?? '',
                'pincode' => $bpartners->pincode ?? '',
                'country' => $bpartners->country ?? '',
                'extension' => $bpartners->extension ?? '',
                'latitude' => $bpartners->latitude ?? '',
                'longitude' => $bpartners->longitude ?? '',
                'homepage' => $bpartners->homepage ?? '',
                'category_type' => $bpartners->category_type ?? '',
                'time_zone_city' => $bpartners->time_zone_city ?? '',
                'time_zone_code' => $bpartners->time_zone_code ?? '',
                'time_zone_name' => $bpartners->time_zone_name ?? '',
                'code' => $bpartners->code ?? '',
                'org_id' => $bpartners->org_id ?? '',
                'be_value' => $bpartners->be_value ?? '',
                'customer_code' => $bpartners->customer_code ?? '',
                'acon_debitor_code' => $bpartners->acon_debitor_code ?? '',
                'kn_login_account' => $bpartners->kn_login_account ?? '',
                'password' => $bpartners->password ?? '',
                'offering_type' => $bpartners->offering_type ?? '',
                'carrier_grade' => $bpartners->carrier_grade ?? '',
                'max_capacity' => $bpartners->max_capacity ?? '',
                'rating' => $bpartners->rating ?? '',
                'cost_efficiency' => $bpartners->cost_efficiency ?? '',
                'certifications' => $bpartners->certifications ?? '',
                'autoaccept' => $bpartners->autoaccept ?? '',
                'cargo_limit' => $bpartners->cargo_limit ?? '',
                'limit_amount' => $bpartners->limit_amount ?? '',
                'currency' => $bpartners->currency ?? '',
                'credit_limit' => $bpartners->credit_limit ?? '',
                'credit_limit_amount' => $bpartners->credit_limit_amount ?? '',
                'credit_currency' => $bpartners->credit_currency ?? '',
                'vat_reg_no' => $bpartners->vat_reg_no ?? '',
                'tax_payer_no' => $bpartners->tax_payer_no ?? '',
                'contract_no' => $bpartners->contract_no ?? '',
                'contract_date' => $bpartners->contract_date ?? '',
                'invoice_type' => $bpartners->invoice_type ?? '',
                'partner_id' => $id,
                'geo_fence_radius' => $bpartners->geo_fence_radius ?? '',
            ]);

            $result['condata'] = SxPartnerContacts::where(['partner_id' => $id, 'status' => 1])->get();
            $result['refdata'] = SxPartyReferences::where(['partner_id' => $id, 'status' => 1])->get();
            $result['reftypes'] = ReferenceType::where('status', 1)->get();

            $result['visibilityMasterData'] = [];
            $result['integrationMasterData'] = [];
            $result['eBookingMasterData'] = [];
            foreach ($result['reftypes'] as $type) {
                $refType = $type->reference_type ?? '';
                if ($refType === 'eBooking') {
                    $result['eBookingMasterData'][] = ['id' => $type->id, 'name' => $type->name];
                } elseif ($refType === 'integration') {
                    $result['integrationMasterData'][] = ['id' => $type->id, 'name' => $type->name];
                } elseif ($refType === 'visibility') {
                    $result['visibilityMasterData'][] = ['id' => $type->id, 'name' => $type->name];
                }
            }

            $result['accountndata'] = SxPartnerAccounts::where(['partner_id' => $id, 'status' => 1])->get();
            $result['contractsdata'] = SxPartnerContract::where(['partner_id' => $id, 'status' => 1])->get();
            $result['ediapidata'] = SxPartnerEdiApi::where(['partner_id' => $id, 'status' => 1])->get();

            return response()->json([
                'status' => 'success',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            Log::error('Error fetching partner details: ' . $e->getMessage());
            return response()->json([
                'status' => 'fail',
                'error' => 'An error occurred while fetching partner details'
            ], 500);
        }
    }

    /**
     * Show the form for creating a new partner
     */
    public function partnerAdd(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $org_id = $user->org_id;
        $be_value = $user->be_value;
        $uid = $user->id;

        $data = [
            'org_id' => $org_id,
            'be_value' => $be_value,
            'reftypes' => [],
            'getparties' => [],
            'partners' => [],
            /* 'actions' => TransactionAction::select('id', 'name')->where('status', 1)->get(), */
            'ordertypes' => OrderType::select('id', 'type_name')->where(['user_id' => $uid, 'status' => 1])->get(),
            'products' => Product::select('id', 'name')->where('status', 1)->get(),
            'services' => ServiceMaster::select('id', 'name')->where('status', 1)->get(),
            'transmodes' => TransportMode::select('id', 'code')->where('status', 1)->get(),
            'editypes' => EdiType::select('id', 'name')->where('status', 1)->get(),
            'incoterms' => IncoTerms::select('id', 'name')->where('status', 1)->get(),
            'orderstatuses' => StatusMaster::select('id', 'status_name')->where('status', 1)->get(),
            'contracts' => [],
            'accounts' => [],
            'currencies' => [],
            'actual_currency' => '',
            'visibilityMasterData' => [],
            'integrationMasterData' => [],
            'eBookingMasterData' => [],
            'group_types' => SxPartyGroupTypes::select('id', 'type_category_name')
                ->where(['status' => 1])
                ->orderBy('id', 'DESC')
                ->get(),
            'regions' => Regions::select('id', 'region_name')
                ->where(['org_id' => $org_id, 'status' => 1])
                ->orderBy('id', 'DESC')
                ->get(),
        ];

        $getParties = SxPartyTypes::select('id', 'type_name as name', 'description')
            ->where(['org_id' => $org_id, 'status' => 1])
            ->get();
        foreach ($getParties as $gparty) {
            $data['getparties'][] = [
                'id' => $gparty->id,
                'name' => ucfirst(strtolower($gparty->name)),
                'description' => $gparty->description
            ];
        }

        $getCurrencies = CountryMaster::select('country_name')->where('status', 1)->get();
        $country_code = substr($org_id, 0, 2);
        /* foreach ($getCurrencies as $cury) {
            $data['currencies'][] = $cury->currency;
            if ($country_code == $cury->country_code) {
                $data['actual_currency'] = $cury->currency;
            }
        } */

        $getRefIds = ReferenceType::select('id', 'name', 'reference_type')->where('status', 1)->get();
        $reftypes = [];
        foreach ($getRefIds as $eachType) {
            $referenceType = $eachType->reference_type;
            if ($referenceType == 'eBooking') {
                $data['eBookingMasterData'][] = ['id' => $eachType->id, 'name' => $eachType->name];
            }
            if ($referenceType == 'integration') {
                $data['integrationMasterData'][] = ['id' => $eachType->id, 'name' => $eachType->name];
            }
            if ($referenceType == 'visibility') {
                $data['visibilityMasterData'][] = ['id' => $eachType->id, 'name' => $eachType->name];
            }
            $reftypes[] = ['id' => $eachType->id, 'name' => $eachType->name];
        }
        $data['reftypes'] = $reftypes;

        return response()->json([
            'status' => 'success',
            'data' => $data
        ]);
    }

    /**
     * Store a newly created partner
     */
    public function partnerCreate(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }
        $validator = Validator::make($request->all(), [
            'customercode' => 'required|string',
            'org_id' => 'required|integer',
            'partytypes' => 'required|array',
            'addrs_name' => 'required|string',
            'addrs_email' => 'required|email',
            'addrs_phone' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();
        $uid = $user->id;
        $custid = $user->cust_id ?? 0;
        $org_id = $data['org_id'];
        if ($org_id !== $user->org_id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized access to this organization'
            ], 403);
        }

        $curdt = now();
        $customer_code = trim($data['customercode']);
        $cid = trim($data['partner_cid'] ?? '');
        $be_value = $data['be_value'] ?? 0;
        $debitor_code = $data['partner_debitor_code'] ?? '';
        $kn_login = $data['kn_login'] ?? '';
        $bpassword = $data['password'] ?? '';
        if ($bpassword) {
            $bpassword = bcrypt($bpassword);
        }
        $partytypes = implode(',', $data['partytypes']);
        $offering_type = $data['offering_type'] ?? '';
        $carrier_grade = $data['carrier_grade'] ?? '';
        $max_capacity = $data['max_capacity'] ?? 0.00;
        $rating = $data['rating'] ?? 0.00;
        $cost_efficiency = $data['cost_efficiency'] ?? 0.00;
        $region = $data['region'] ?? '';
        $certifications = $data['certifications'] ?? '';
        $autoaccept = (int)($data['autoaccept'] ?? 0);
        $addrs_name = $data['addrs_name'] ?? '';
        $addrs_email = $data['addrs_email'] ?? '';
        $addrs_phone = $data['addrs_phone'] ?? '';
        $division_name = $data['division_name'] ?? '';
        $sub_district = $data['sub_district'] ?? '';
        $addrs_street = $data['addrs_street'] ?? '';
        $street_2 = $data['street_2'] ?? '';
        $street_3 = $data['street_3'] ?? '';
        $house_number = $data['house_number'] ?? '';
        $addrs_building = $data['addrs_building'] ?? '';
        $addrs_country = $data['addrs_country'] ?? '';
        $postal_code = $data['postal_code'] ?? '';
        $addrs_city = $data['addrs_city'] ?? '';
        $addrs_province = $data['addrs_province'] ?? '';
        $addrs_extension = $data['addrs_extension'] ?? '';
        $addrs_latitude = $data['addrs_latitude'] ?? '';
        $addrs_longitude = $data['addrs_longitude'] ?? '';
        $main_homepage = $data['main_homepage'] ?? '';
        $category_type = (int)($data['addrs_category'] ?? 0);
        $category_id = (int)($data['addrs_catid'] ?? 0);
        $zone_city = $data['zone_city'] ?? '';
        $zone_code = $data['zone_code'] ?? '';
        $zone_name = $data['zone_name'] ?? '';
        $cargo_limit = (int)($data['cargo_limit'] ?? 0);
        $limit_amount = (int)($data['limit_amount'] ?? 0);
        $currency = $data['currency'] ?? '';
        $credit_limit_amount = (int)($data['c_limit_amount'] ?? 0);
        $credit_currency = $data['c_currency'] ?? '';
        $invoice_type = (int)($data['invoice_type'] ?? 0);
        $cust_profile_id = (int)($data['cust_profile_id'] ?? 0);
        $ven_profile_id = (int)($data['ven_profile_id'] ?? 0);
        $vat_reg_no = $data['vat_reg_no'] ?? '';
        $tax_payer_no = $data['tax_payer_no'] ?? '';
        $contract_no = $data['contract_no'] ?? '';
        $contract_date = isset($data['contract_date']) ? date('Y-m-d', strtotime($data['contract_date'])) : null;
        $auto_trip = (int)($data['auto_trip'] ?? 0);
        $tms_auto_trip = (int)($data['tms_auto_trip'] ?? 0);
        $tariff_type = (int)($data['tariff_type'] ?? 0);
        $sendto_knlogin = (int)($data['sendto_knlogin'] ?? 0);
        $geo_fence_radius = (int)($data['geo_fence_radius'] ?? 0);
        $partnercontacts = $data['partnercontact'] ?? [];
        $partnerrefs = $data['partnerrefs'] ?? [];
        $partneraccounts = $data['partneraccounts'] ?? [];
        $partnercontracts = $data['partnercontracts'] ?? [];
        $savedediapiid = $data['savedediapiid'] ?? [];
        $eBooking = $data['ebooking'] ?? [];
        $integrations = $data['integration'] ?? [];
        $visibility = $data['visibility'] ?? [];
        $group_type = (int)($data['group_type'] ?? 0);

        try {
            DB::beginTransaction();

            $custsessioncode = '';
            if ($custid > 0) {
                $customer = SxPartyMembers::select('code')
                    ->where(['id' => $custid, 'status' => 1])
                    ->first();
                if ($customer) {
                    $custsessioncode = $customer->code;
                }
            }

            $partnerdata = [
                'party_type' => 0,
                'name' => $addrs_name,
                'email' => $addrs_email,
                'mobile' => $addrs_phone,
                'user_id' => $uid,
                'code' => $cid,
                'org_id' => $org_id,
                'be_value' => $be_value,
                'customer_code' => $custsessioncode,
                'parent_id' => 0,
                'location_id' => $addrs_city,
                'country' => $addrs_country,
                'state' => $addrs_province,
                'division_name' => $division_name,
                'sub_district' => $sub_district,
                'street' => $addrs_street,
                'pincode' => $postal_code,
                'street_2' => $street_2,
                'street_3' => $street_3,
                'building' => $addrs_building,
                'house_number' => $house_number,
                'party_types' => $partytypes,
                'extension' => $addrs_extension,
                'latitude' => $addrs_latitude,
                'longitude' => $addrs_longitude,
                'homepage' => $main_homepage,
                'category_type' => $category_type,
                'category_id' => $category_id,
                'time_zone_city' => $zone_city,
                'time_zone_code' => $zone_code,
                'time_zone_name' => $zone_name,
                'acon_debitor_code' => $debitor_code,
                'kn_login_account' => $kn_login,
                'password' => $bpassword,
                'offering_type' => $offering_type,
                'carrier_grade' => $carrier_grade,
                'max_capacity' => $max_capacity,
                'rating' => $rating,
                'cost_efficiency' => $cost_efficiency,
                'region' => $region,
                'certifications' => $certifications,
                'autoaccept' => $autoaccept,
                'cargo_limit' => $cargo_limit,
                'limit_amount' => $limit_amount,
                'currency' => $currency,
                'credit_limit_amount' => $credit_limit_amount,
                'credit_currency' => $credit_currency,
                'vat_reg_no' => $vat_reg_no,
                'tax_payer_no' => $tax_payer_no,
                'contract_no' => $contract_no,
                'contract_date' => $contract_date,
                'status' => 1,
                'created_at' => $curdt,
                'updated_at' => $curdt,
                'geo_fence_radius' => $geo_fence_radius
            ];

            $partner = SxPartyMembers::create($partnerdata);
            $partnerid = $partner->id;

            $ccode = substr($org_id, 0, 2);
            $idlength = strlen($partnerid);
            $id = $partnerid;
            if ($idlength == 1) {
                $id = "00000" . $id;
            } elseif ($idlength == 2) {
                $id = "0000" . $id;
            } elseif ($idlength == 3) {
                $id = "000" . $id;
            } elseif ($idlength == 4) {
                $id = "00" . $id;
            } elseif ($idlength == 5) {
                $id = "0" . $id;
            }
            $custcode = $ccode . $id;

            $address = $addrs_street;
            if ($street_2) $address .= ',' . $street_2;
            if ($street_3) $address .= ',' . $street_3;
            if ($house_number) $address .= ',' . $house_number;
            if ($addrs_city) $address .= ',' . $addrs_city;
            if ($addrs_province) $address .= ',' . $addrs_province;

            $partytypesary = explode(',', $partytypes);
            $customer_id = $vendor_id = 0;
            $customer_partyid = $vendor_partyid = 0;

            /* $carrier_party = SxPartyTypes::whereIn('id', $partytypesary)->where('type_name', 'Carrier')->first();
            if ($carrier_party) {
                $vendor_partyid = $carrier_party->id;
                $vendarr = [
                    'name' => $addrs_name,
                    'mobile' => $addrs_phone,
                    'location' => $addrs_city,
                    'address' => $address,
                    'pincode' => $postal_code,
                    'country' => $addrs_country,
                    'code' => $cid,
                    'offering_type' => $offering_type,
                    'carrier_grade' => $carrier_grade,
                    'max_capacity' => $max_capacity,
                    'rating' => $rating,
                    'cost_efficiency' => $cost_efficiency,
                    'region' => $region,
                    'certifications' => $certifications,
                    'email' => $addrs_email,
                    'password' => $bpassword,
                    'user_id' => $uid,
                    'partyid' => $partnerid,
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'status' => 1,
                    'auto_accept' => $auto_trip,
                    'tms_auto_accept' => $tms_auto_trip,
                    'updated_on' => $curdt,
                    'created_on' => $curdt
                ];

                $vendor = Vendor::where(['name' => $addrs_name, 'mobile' => $addrs_phone, 'code' => $cid, 'org_id' => $org_id, 'be_value' => $be_value])
                    ->first();
                if ($vendor) {
                    $vendor_id = $vendor->id;
                    Vendor::where('id', $vendor_id)->update($vendarr);
                    if ($ven_profile_id) {
                        $profile = VendorProfileList::where(['party_id' => $vendor_id])->first();
                        $pro_list = ['vp_id' => $ven_profile_id, 'profile_id' => $cid, 'party_id' => $vendor_id, 'status' => 1];
                        if ($profile) {
                            VendorProfileList::where('id', $profile->id)->update($pro_list);
                        } else {
                            VendorProfileList::create($pro_list);
                        }
                    }
                } else {
                    $vendor = Vendor::create($vendarr);
                    $vendor_id = $vendor->id;
                    if ($ven_profile_id) {
                        VendorProfileList::create(['vp_id' => $ven_profile_id, 'profile_id' => $cid, 'party_id' => $vendor_id, 'status' => 1]);
                    }
                }

                SxPartyGroups::create([
                    'vendor_id' => $vendor_id,
                    'type_id' => $group_type,
                    'org_id' => $org_id,
                    'status' => 1,
                    'be_value' => $be_value
                ]);
            }

            $customer_party = SxPartyTypes::whereIn('id', $partytypesary)->where('type_name', 'Customer')->first();
            if ($customer_party && $cid) {
                $customer_partyid = $customer_party->id;
                $customer = [
                    'partner_id' => $partnerid,
                    'name' => $addrs_name,
                    'phone' => $addrs_phone,
                    'street' => $addrs_street,
                    'location' => $addrs_city,
                    'pincode' => $postal_code,
                    'country' => $addrs_country,
                    'address' => $address,
                    'email_id' => $addrs_email,
                    'state' => $addrs_province,
                    'code' => $cid,
                    'password' => $bpassword,
                    'user_id' => $uid,
                    'updatedon' => $curdt,
                    'createdon' => $curdt,
                    'lat' => $addrs_latitude,
                    'lng' => $addrs_longitude,
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'Tarifftype' => $tariff_type,
                    'sendto_knlogin' => $sendto_knlogin,
                    'geo_fence_radius' => $geo_fence_radius,
                    'kn_login' => preg_match('/[A-Za-z].*[0-9]|[0-9].*[A-Za-z]/', $kn_login) && strlen($kn_login) > 9 && strlen($kn_login) < 16 ? 1 : 0
                ];

                $existing_customer = Customer::where(['code' => $cid, 'org_id' => $org_id, 'status' => 1])
                    ->first();
                if ($existing_customer) {
                    $customer_id = $existing_customer->id;
                    Customer::where('id', $customer_id)->update($customer);
                    if ($cust_profile_id) {
                        $profile = CustomerProfileList::where(['party_id' => $customer_id])->first();
                        $cus_pro_list = ['cp_id' => $cust_profile_id, 'profile_id' => $cid, 'party_id' => $customer_id, 'status' => 1];
                        if ($profile) {
                            CustomerProfileList::where('id', $profile->id)->update($cus_pro_list);
                        } else {
                            CustomerProfileList::create($cus_pro_list);
                        }
                    }
                } else {
                    $customer = Customer::create($customer);
                    $customer_id = $customer->id;
                    if ($cust_profile_id) {
                        CustomerProfileList::create(['cp_id' => $cust_profile_id, 'profile_id' => $cid, 'party_id' => $customer_id, 'status' => 1]);
                    }
                }
            } */

            /*───────────────────────────────*
            |  (A) CARRIER  →  sx_users
            *───────────────────────────────*/
            $carrier_party = SxPartyTypes::whereIn('id', $partytypesary)
                            ->where('type_name', 'Carrier')
                            ->first();

            if ($carrier_party) {
                $vendor_partyid = $carrier_party->id;
                /* User::create([
                    'username'           => $cid ?: ('carrier_' . $partnerid),
                    'emailid'            => $addrs_email,
                    'password'           => $bpassword ?: bcrypt(Str::random(12)),
                    'employee_id'        => null,
                    'employee_name'      => $addrs_name,
                    'effective_fromdate' => now()->toDateString(),
                    'effective_enddate'  => null,
                    'contact_num'        => $addrs_phone,
                    'theme_id'           => 1,
                    'currency'           => $currency ?: 'USD',
                    'number_format'      => '###,###.##',
                    'languages'          => json_encode(['en'], JSON_UNESCAPED_SLASHES),
                    'date_format'        => 'Y-m-d',
                    'default_org_id'     => $org_id,
                ]); */

                // keep Party-Group insert (if you still need it)
                SxPartyGroups::create([
                    'vendor_id' => $partnerid,  // or $vendor_id if you keep that logic
                    'type_id'   => $group_type,
                    'org_id'    => $org_id,
                    'status'    => 1,
                    'be_value'  => $be_value,
                ]);
            }

            /*───────────────────────────────*
            |  (B) CUSTOMER  →  sx_users
            *───────────────────────────────*/
            $customer_party = SxPartyTypes::whereIn('id', $partytypesary)
                            ->where('type_name', 'Customer')
                            ->first();

            if ($customer_party && $cid) {
                $customer_partyid = $customer_party->id;

                // -------- NEW: create user in sx_users --------
                /* User::create([
                    'username'           => $cid,
                    'emailid'            => $addrs_email,
                    'password'           => $bpassword ?: bcrypt(Str::random(12)),
                    'employee_id'        => null,
                    'employee_name'      => $addrs_name,
                    'effective_fromdate' => now()->toDateString(),
                    'effective_enddate'  => null,
                    'contact_num'        => $addrs_phone,
                    'theme_id'           => 1,
                    'currency'           => $currency ?: 'USD',
                    'number_format'      => '###,###.##',
                    'languages'          => json_encode(['en'], JSON_UNESCAPED_SLASHES),
                    'date_format'        => 'Y-m-d',
                    'default_org_id'     => $org_id,
                ]); */
            }


            $upd = ['customeridentifier' => $custcode];
            $upd['party_type'] = $customer_partyid ?: ($vendor_partyid ?: ($data['partytypes'][0] ?? 0));
            SxPartyMembers::where('id', $partnerid)->update($upd);

            if ($partnercontacts) {
                foreach ($partnercontacts as $contact) {
                    SxPartnerContacts::create([
                        'partner_id' => $partnerid,
                        'party_type_id' => $upd['party_type'],
                        'name' => $contact['name'] ?? '',
                        'email' => $contact['email'] ?? '',
                        'phone' => $contact['phone'] ?? '',
                        'uid' => $uid,
                        'createdon' => $curdt,
                        'status' => 1
                    ]);
                }
            }

            if ($partnerrefs) {
                foreach ($partnerrefs as $refs) {
                    $refs = explode('||', $refs);
                    SxPartyReferences::create([
                        'reference_type' => $refs[0] ?? '',
                        'partner_id' => $partnerid,
                        'name' => $refs[1] ?? '',
                        'value' => $refs[2] ?? '',
                        'description' => $refs[3] ?? '',
                        'gstin' => $refs[4] ?? '',
                        'user_id' => $uid,
                        'status' => 1,
                        'createdon' => $curdt,
                        'updatedon' => $curdt
                    ]);
                }
            }

            if ($partneraccounts) {
                foreach ($partneraccounts as $account) {
                    $account = explode('||', $account);
                    SxPartnerAccounts::create([
                        'partner_id' => $partnerid,
                        'account_number' => $account[0] ?? '',
                        'acon_environment' => $account[1] ?? '',
                        'org_id' => $account[2] ?? '',
                        'source_system' => $account[3] ?? '',
                        'billable' => $account[4] ?? '',
                        'pre_payment' => $account[5] ?? '',
                        'mutiple_invoices' => $account[6] ?? '',
                        'user_id' => $uid,
                        'status' => 1,
                        'created_on' => $curdt,
                        'updated_on' => $curdt
                    ]);
                }
            }

            if ($partnercontracts) {
                foreach ($partnercontracts as $contract) {
                    $contract = explode('||', $contract);
                    SxPartnerContract::create([
                        'partner_id' => $partnerid,
                        'org_id' => $contract[0] ?? '',
                        'be_value' => $contract[1] ?? '',
                        'contract_id' => $contract[2] ?? '',
                        'rate_derivation' => $contract[3] ?? '',
                        'user_id' => $uid,
                        'status' => 1,
                        'created_on' => $curdt,
                        'updated_on' => $curdt
                    ]);
                }
            }

            if ($savedediapiid) {
                foreach ($savedediapiid as $ediapiid) {
                    SxPartnerEdiApi::where('id', $ediapiid)->update(['partner_id' => $partnerid]);
                    if ($customer_id) {
                        $order_type = SxPartnerEdiApi::select('order_types.name as ordertype')
                            ->join('order_types', 'order_types.id', '=', 'partner_ediapi.order_type')
                            ->where('partner_ediapi.id', $ediapiid)
                            ->value('ordertype');
                        if ($order_type) {
                            $existing_ordtype = OrderType::where([
                                'type_name' => $order_type,
                                'org_id' => $org_id,
                                'be_value' => $be_value,
                                'customer_id' => $customer_id,
                                'status' => 1
                            ])->first();
                            if (!$existing_ordtype) {
                                OrderType::create([
                                    'type_name' => $order_type,
                                    'description' => $order_type,
                                    'org_id' => $org_id,
                                    'be_value' => $be_value,
                                    'customer_id' => $customer_id,
                                    'ordtype_code' => substr($order_type, 0, 1),
                                    'status' => 1,
                                    'createdon' => $curdt,
                                    'updatedon' => $curdt
                                ]);
                            }
                        }
                    }
                }
            }

            foreach ([$eBooking, $integrations, $visibility] as $ref_array) {
                if ($ref_array) {
                    foreach ($ref_array as $eachRow) {
                        $referenceName = ReferenceType::where('id', $eachRow)->value('name');
                        SxPartyReferences::create([
                            'reference_type' => $eachRow,
                            'partner_id' => $partnerid,
                            'name' => $referenceName ?? '',
                            'value' => 'Y',
                            'user_id' => $uid,
                            'status' => 1
                        ]);
                    }
                }
            }

            if ($org_id === 'COPELAND') {
                $ptypesData = SxPartyTypes::whereIn('id', explode(',', $partytypes))
                    ->where('status', 1)
                    ->pluck('type_name')
                    ->toArray();
                $partnerdata['partytypes'] = $ptypesData;
                // Placeholder for OTM integration
            }

            $this->logActivity([
                'description' => 'Partner created (id: ' . $partnerid . ')',
                'user_id' => $uid,
                'createdon' => $curdt
            ]);

            DB::commit();
            // Log::info('Partner created successfully for partner_id: ' . $partnerid);
            return response()->json([
                'status' => 'success',
                'partner_id' => $partnerid,
                'message' => 'Partner created successfully'
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            // Log::error('Failed to create partner: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create partner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified partner
     */
    public function partnerEdit(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $uid = $user->id;
        $org_id = $user->org_id;
        $be_value = $user->be_value;

        try {
            $partner = SxPartyMembers::where(['id' => $id, 'org_id' => $org_id])->first();
            if (!$partner) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Partner not found'
                ], 404);
            }

            $party_types = $partner->party_types ? explode(',', $partner->party_types) : [];
            $party_types[] = $partner->party_type;
            $partytypes = array_unique($party_types);

            $data = [
                'partner_id' => $id,
                'party_type_names' => SxPartyTypes::select('id', 'type_name as name')
                    ->where(['org_id' => $org_id])
                    ->whereIn('id', $partytypes)
                    ->get(),
                'partytypes' => $partytypes,
                'carrier_p_id' => 0,
                'cus_p_id' => 0,
                'auto_accept' => 0,
                'tms_auto_accept' => 0,
                'name' => $partner->name,
                'email' => $partner->email,
                'mobile' => $partner->mobile,
                'location_id' => $partner->location_id,
                'division_name' => $partner->division_name,
                'sub_district' => $partner->sub_district,
                'street' => $partner->street,
                'street_2' => $partner->street_2,
                'street_3' => $partner->street_3,
                'house_number' => $partner->house_number,
                'building' => $partner->building,
                'state' => $partner->state,
                'pincode' => $partner->pincode,
                'country' => $partner->country,
                'extension' => $partner->extension,
                'latitude' => $partner->latitude,
                'longitude' => $partner->longitude,
                'homepage' => $partner->homepage,
                'category_type' => $partner->category_type,
                /* 'categorydata' => $partner->category_id ? ['id' => $partner->category_id, 'name' => CompanyCategory::where('id', $partner->category_id)->value('name')] : [], */
                'time_zone_city' => $partner->time_zone_city,
                'time_zone_code' => $partner->time_zone_code,
                'time_zone_name' => $partner->time_zone_name,
                'code' => $partner->code,
                'org_id' => $partner->org_id,
                'be_value' => $partner->be_value,
                'customer_code' => $partner->customer_code,
                'acon_debitor_code' => $partner->acon_debitor_code,
                'kn_login_account' => $partner->kn_login_account,
                'offering_type' => $partner->offering_type,
                'carrier_grade' => $partner->carrier_grade,
                'max_capacity' => $partner->max_capacity,
                'rating' => $partner->rating,
                'cost_efficiency' => $partner->cost_efficiency,
                'region' => $partner->region,
                'certifications' => $partner->certifications,
                'autoaccept' => $partner->autoaccept,
                'cargo_limit' => $partner->cargo_limit,
                'limit_amount' => $partner->limit_amount,
                'currency' => $partner->currency,
                'credit_limit_amount' => $partner->credit_limit_amount,
                'credit_currency' => $partner->credit_currency,
                'vat_reg_no' => $partner->vat_reg_no,
                'tax_payer_no' => $partner->tax_payer_no,
                'contract_no' => $partner->contract_no,
                'contract_date' => $partner->contract_date,
                'invoice_type' => $partner->invoice_type,
                'cust_profile_id' => '',
                'customer_profile_id' => '',
                'ven_profile_id' => '',
                'vendor_profile_id' => '',
                'Tarifftype' => 0,
                'sendto_knlogin' => 0,
                'geo_fence_radius' => $partner->geo_fence_radius,
                'condata' => SxPartnerContacts::where(['partner_id' => $id, 'status' => 1])->get(),
                'refdata' => SxPartyReferences::where(['partner_id' => $id, 'status' => 1])->get(),
                'accountndata' => SxPartnerAccounts::where(['partner_id' => $id, 'status' => 1])->get(),
                'contractsdata' => SxPartnerContract::where(['partner_id' => $id, 'status' => 1])->get(),
                'ediapidata' => SxPartnerEdiApi::where(['partner_id' => $id, 'status' => 1])->get(),
                'getparties' => SxPartyTypes::select('id', 'type_name as name', 'description')
                    ->where(['org_id' => $org_id, 'status' => 1])
                    ->get(),
                /* 'actions' => TransactionAction::select('id', 'name')->where('status', 1)->get(), */
                'ordertypes' => OrderType::select('id', 'type_name')->where(['user_id' => $uid, 'status' => 1])->get(),
                'products' => Product::select('id', 'name')->where('status', 1)->get(),
                'services' => ServiceMaster::select('id', 'name')->where('status', 1)->get(),
                'transmodes' => TransportMode::select('id', 'code')->where('status', 1)->get(),
                'editypes' => EdiType::select('id', 'name')->where('status', 1)->get(),
                'incoterms' => IncoTerms::select('id', 'name')->where('status', 1)->get(),
                'orderstatuses' => StatusMaster::select('id', 'status_name')->where('status', 1)->get(),
                /* 'currencies' => CountryMaster::select('currency')->where('status', 1)->distinct()->pluck('currency'), */
                'reftypes' => ReferenceType::select('id', 'name', 'reference_type')->where('status', 1)->get(),
                'eBookingMasterData' => ReferenceType::select('id', 'name')->where(['reference_type' => 'eBooking', 'status' => 1])->get(),
                'integrationMasterData' => ReferenceType::select('id', 'name')->where(['reference_type' => 'integration', 'status' => 1])->get(),
                'visibilityMasterData' => ReferenceType::select('id', 'name')->where(['reference_type' => 'visibility', 'status' => 1])->get(),
                'group_types' => SxPartyGroupTypes::select('id', 'type_category_name')->where(['status' => 1])->orderBy('id', 'DESC')->get(),
                'regions' => Regions::select('id', 'region_name')->where(['org_id' => $org_id, 'status' => 1])->orderBy('id', 'DESC')->get(),
                'group_type_id' => 0
            ];

            /* $carrier_party = SxPartyTypes::whereIn('id', $partytypes)->where('type_name', 'Carrier')->first();
            if ($carrier_party) {
                $vendor = Vendor::select('id', 'auto_accept', 'tms_auto_accept')
                    ->where(['partyid' => $id, 'code' => trim($partner->code), 'status' => 1])
                    ->first();
                if ($vendor) {
                    $data['carrier_p_id'] = $vendor->id;
                    $data['auto_accept'] = $vendor->auto_accept;
                    $data['tms_auto_accept'] = $vendor->tms_auto_accept;
                    $vendor_profile = VendorProfileList::where(['profile_id' => trim($partner->code)])->first();
                    if ($vendor_profile) {
                        $data['ven_profile_id'] = $vendor_profile->vp_id;
                        $data['vendor_profile_id'] = $vendor_profile->vend_profile_id;
                    }
                    $group_type = PartyGroup::select('type_id')->where(['org_id' => $org_id, 'vendor_id' => $vendor->id, 'status' => 1])->first();
                    $data['group_type_id'] = $group_type->type_id ?? 0;
                }
            }

            $customer_party = PartyTypes::whereIn('id', $partytypes)->where('type_name', 'Customer')->first();
            if ($customer_party) {
                $customer = Customer::select('id', 'sendto_knlogin', 'Tarifftype')
                    ->where(['code' => trim($partner->code), 'org_id' => $org_id, 'status' => 1])
                    ->first();
                if ($customer) {
                    $data['cus_p_id'] = $customer->id;
                    $data['sendto_knlogin'] = $customer->sendto_knlogin;
                    $data['Tarifftype'] = $customer->Tarifftype;
                    $customer_profile = CustomerProfileList::where(['partner_id' => $id])->first();
                    if ($customer_profile) {
                        $data['cust_profile_id'] = $customer_profile->cp_id;
                        $data['customer_profile_id'] = $customer_profile->cust_profile_id;
                    }
                }
            } */

            Log::info('Partner edit data retrieved for partner_id: ' . $id);
            return response()->json([
                'status' => 'success',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve partner edit data: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve partner edit data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified partner
     */
    public function partnerUpdate(Request $request, $id)
    {
        // Temporary static user for testing
        $user = (object) [
            'id' => 1, // Example user ID
            'org_id' => 1, // Must match the org_id in the payload
            'cust_id' => 0, // Optional, set as needed
            'be_value' => 1 // Optional, set as needed
        ];
        // $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $uid = $user->id;
        $org_id = $user->org_id;
        $be_value = $user->be_value;

        $validator = Validator::make($request->all(), [
            'customer_code' => 'required|string',
            'company_code' => 'required|integer',
            'partytypes' => 'required|array',
            'addrs_name' => 'required|string',
            'addrs_email' => 'required|email',
            'addrs_phone' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();
        if ($data['company_code'] !== $org_id) {
            return response()->json([
                'status' => 'error',
                'message' => 'Unauthorized access to this organization'
            ], 403);
        }

        $curdt = now();
        $customer_code = trim($data['customer_code']);
        $cid = trim($data['partner_cid'] ?? '');
        $be_value = $data['be_value'] ?? '';
        $debitor_code = $data['partner_debitor_code'] ?? '';
        $departmentcode = $data['departmentcode'] ?? '';
        $kn_login = $data['kn_login'] ?? '';
        $bpassword = $data['password'] ?? '';
        if ($bpassword && !password_get_info($bpassword)['algo']) {
            $bpassword = bcrypt($bpassword);
        }
        $partytypes = implode(',', $data['partytypes']);
        $offering_type = $data['offering_type'] ?? '';
        $carrier_grade = $data['carrier_grade'] ?? '';
        $max_capacity = $data['max_capacity'] ?? '';
        $rating = $data['rating'] ?? '';
        $cost_efficiency = $data['cost_efficiency'] ?? '';
        $region = $data['region'] ?? '';
        $certifications = $data['certifications'] ?? '';
        $autoaccept = (int)($data['autoaccept'] ?? 0);
        $addrs_name = $data['addrs_name'] ?? '';
        $addrs_email = $data['addrs_email'] ?? '';
        $addrs_phone = $data['addrs_phone'] ?? '';
        $division_name = $data['division_name'] ?? '';
        $sub_district = $data['sub_district'] ?? '';
        $addrs_street = $data['addrs_street'] ?? '';
        $street_2 = $data['street_2'] ?? '';
        $street_3 = $data['street_3'] ?? '';
        $house_number = $data['house_number'] ?? '';
        $addrs_building = $data['addrs_building'] ?? '';
        $addrs_country = $data['addrs_country'] ?? '';
        $postal_code = $data['postal_code'] ?? '';
        $addrs_city = $data['addrs_city'] ?? '';
        $addrs_province = $data['addrs_province'] ?? '';
        $addrs_extension = $data['addrs_extension'] ?? '';
        $addrs_latitude = $data['addrs_latitude'] ?? '';
        $addrs_longitude = $data['addrs_longitude'] ?? '';
        $main_homepage = $data['main_homepage'] ?? '';
        $category_type = (int)($data['addrs_category'] ?? 0);
        $category_id = (int)($data['addrs_catid'] ?? 0);
        $zone_city = $data['zone_city'] ?? '';
        $zone_code = $data['zone_code'] ?? '';
        $zone_name = $data['zone_name'] ?? '';
        $cargo_limit = (int)($data['cargo_limit'] ?? 0);
        $limit_amount = (int)($data['limit_amount'] ?? 0);
        $currency = $data['currency'] ?? '';
        $credit_limit_amount = (int)($data['c_limit_amount'] ?? 0);
        $credit_currency = $data['c_currency'] ?? '';
        $invoice_type = (int)($data['invoice_type'] ?? 0);
        $cust_profile_id = (int)($data['cust_profile_id'] ?? 0);
        $ven_profile_id = (int)($data['ven_profile_id'] ?? 0);
        $vat_reg_no = $data['vat_reg_no'] ?? '';
        $tax_payer_no = $data['tax_payer_no'] ?? '';
        $contract_no = $data['contract_no'] ?? '';
        $contract_date = $data['contract_date'] ? date('Y-m-d', strtotime($data['contract_date'])) : null;
        $auto_trip = (int)($data['auto_trip'] ?? 0);
        $tms_auto_trip = (int)($data['tms_auto_trip'] ?? 0);
        $tariff_type = (int)($data['tariff_type'] ?? 0);
        $sendto_knlogin = (int)($data['sendto_knlogin'] ?? 0);
        $geo_fence_radius = (int)($data['geo_fence_radius'] ?? 0);
        $partnercontacts = $data['partnercontact'] ?? [];
        $partnerrefs = $data['partnerrefs'] ?? [];
        $partneraccounts = $data['partneraccounts'] ?? [];
        $partnercontracts = $data['partnercontracts'] ?? [];
        $savedediapiid = $data['savedediapiid'] ?? [];
        $eBooking = $data['ebooking'] ?? [];
        $integrations = $data['integration'] ?? [];
        $visibility = $data['visibility'] ?? [];
        $group_type = (int)($data['group_type'] ?? 0);

        try {
            DB::beginTransaction();

            $partner = SxPartyMembers::where(['id' => $id, 'org_id' => $org_id])->first();
            if (!$partner) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Partner not found'
                ], 404);
            }

            $custsessioncode = '';
            /* if ($custid = $user->cust_id) {
                $customer = Customer::select('code')
                    ->where(['id' => $custid, 'status' => 1])
                    ->first();
                if ($customer) {
                    $custsessioncode = $customer->code;
                }
            } */

            $partnerdata = [
                'party_type' => 0,
                'name' => $addrs_name,
                'email' => $addrs_email,
                'mobile' => $addrs_phone,
                'user_id' => $uid,
                'code' => $cid,
                'org_id' => $org_id,
                'be_value' => $be_value,
                'department_code' => $departmentcode,
                'customer_code' => $custsessioncode,
                'parent_id' => 0,
                'location_id' => $addrs_city,
                'country' => $addrs_country,
                'state' => $addrs_province,
                'division_name' => $division_name,
                'sub_district' => $sub_district,
                'street' => $addrs_street,
                'pincode' => $postal_code,
                'street_2' => $street_2,
                'street_3' => $street_3,
                'building' => $addrs_building,
                'house_number' => $house_number,
                'party_types' => $partytypes,
                'extension' => $addrs_extension,
                'latitude' => $addrs_latitude,
                'longitude' => $addrs_longitude,
                'homepage' => $main_homepage,
                'category_type' => $category_type,
                'category_id' => $category_id,
                'time_zone_city' => $zone_city,
                'time_zone_code' => $zone_code,
                'time_zone_name' => $zone_name,
                'acon_debitor_code' => $debitor_code,
                'kn_login_account' => $kn_login,
                'password' => $bpassword,
                'offering_type' => $offering_type,
                'carrier_grade' => $carrier_grade,
                'max_capacity' => $max_capacity,
                'rating' => $rating,
                'cost_efficiency' => $cost_efficiency,
                'region' => $region,
                'certifications' => $certifications,
                'autoaccept' => $autoaccept,
                'cargo_limit' => $cargo_limit,
                'limit_amount' => $limit_amount,
                'currency' => $currency,
                'credit_limit_amount' => $credit_limit_amount,
                'credit_currency' => $credit_currency,
                'vat_reg_no' => $vat_reg_no,
                'tax_payer_no' => $tax_payer_no,
                'contract_no' => $contract_no,
                'contract_date' => $contract_date,
                'status' => 1,
                'updated_at' => $curdt,
                'geo_fence_radius' => $geo_fence_radius
            ];

            $partner->update($partnerdata);

            $ccode = substr($org_id, 0, 2);
            $idlength = strlen($id);
            $pid = $id;
            if ($idlength == 1) {
                $pid = "00000" . $id;
            } elseif ($idlength == 2) {
                $pid = "0000" . $id;
            } elseif ($idlength == 3) {
                $pid = "000" . $id;
            } elseif ($idlength == 4) {
                $pid = "00" . $id;
            } elseif ($idlength == 5) {
                $pid = "0" . $id;
            }
            $custcode = $ccode . $pid;

            $address = $addrs_street;
            if ($street_2) $address .= ',' . $street_2;
            if ($street_3) $address .= ',' . $street_3;
            if ($house_number) $address .= ',' . $house_number;
            if ($addrs_city) $address .= ',' . $addrs_city;
            if ($addrs_province) $address .= ',' . $addrs_province;

            $partytypesary = explode(',', $partytypes);
            $customer_id = $vendor_id = 0;
            $customer_partyid = $vendor_partyid = 0;

            /* $carrier_party = SxPartyTypes::whereIn('id', $partytypesary)->where('type_name', 'Carrier')->first();
            if ($carrier_party) {
                $vendor_partyid = $carrier_party->id;
                $vendarr = [
                    'name' => $addrs_name,
                    'mobile' => $addrs_phone,
                    'location' => $addrs_city,
                    'address' => $address,
                    'pincode' => $postal_code,
                    'country' => $addrs_country,
                    'code' => $cid,
                    'offering_type' => $offering_type,
                    'carrier_grade' => $carrier_grade,
                    'max_capacity' => $max_capacity,
                    'rating' => $rating,
                    'cost_efficiency' => $cost_efficiency,
                    'region' => $region,
                    'certifications' => $certifications,
                    'email' => $addrs_email,
                    'password' => $bpassword,
                    'user_id' => $uid,
                    'partyid' => $id,
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'status' => 1,
                    'auto_accept' => $auto_trip,
                    'tms_auto_accept' => $tms_auto_trip,
                    'updated_on' => $curdt
                ];

                $vendor = Vendor::where(['name' => $addrs_name, 'mobile' => $addrs_phone, 'code' => $cid, 'org_id' => $org_id, 'be_value' => $be_value])
                    ->first();
                if ($vendor) {
                    $vendor_id = $vendor->id;
                    Vendor::where('id', $vendor_id)->update($vendarr);
                    if ($ven_profile_id) {
                        $profile = VendorProfileList::where(['party_id' => $vendor_id])->first();
                        $pro_list = ['vp_id' => $ven_profile_id, 'profile_id' => $cid, 'party_id' => $vendor_id, 'status' => 1];
                        if ($profile) {
                            VendorProfileList::where('id', $profile->id)->update($pro_list);
                        } else {
                            VendorProfileList::create($pro_list);
                        }
                    }
                } else {
                    $vendor = Vendor::create($vendarr);
                    $vendor_id = $vendor->id;
                    if ($ven_profile_id) {
                        VendorProfileList::create(['vp_id' => $ven_profile_id, 'profile_id' => $cid, 'party_id' => $vendor_id, 'status' => 1]);
                    }
                }

                $vendor_rule = VendorRule::where(['vendor_id' => $vendor_id, 'org_id' => $org_id, 'be_value' => $be_value])->first();
                if ($vendor_rule) {
                    VendorRule::where('id', $vendor_rule->id)->update([
                        'autoaccept' => $autoaccept,
                        'updatedon' => $curdt
                    ]);
                } else {
                    VendorRule::create([
                        'vendor_id' => $vendor_id,
                        'org_id' => $org_id,
                        'be_value' => $be_value,
                        'user_id' => $uid,
                        'createdon' => $curdt,
                        'rule_status' => 'Direct',
                        'status' => 1,
                        'autoaccept' => $autoaccept
                    ]);
                }

                $party_group = SxPartyGroups::where(['vendor_id' => $vendor_id, 'org_id' => $org_id, 'status' => 1])->first();
                if ($party_group) {
                    SxPartyGroups::where('id', $party_group->id)->update([
                        'type_id' => $group_type,
                        'be_value' => $be_value
                    ]);
                } else {
                    SxPartyGroups::create([
                        'vendor_id' => $vendor_id,
                        'type_id' => $group_type,
                        'org_id' => $org_id,
                        'status' => 1,
                        'be_value' => $be_value
                    ]);
                }
            }

            $customer_party = SxPartyTypes::whereIn('id', $partytypesary)->where('type_name', 'Customer')->first();
            if ($customer_party && $cid) {
                $customer_partyid = $customer_party->id;
                $customer = [
                    'partner_id' => $id,
                    'name' => $addrs_name,
                    'phone' => $addrs_phone,
                    'street' => $addrs_street,
                    'location' => $addrs_city,
                    'pincode' => $postal_code,
                    'country' => $addrs_country,
                    'address' => $address,
                    'email_id' => $addrs_email,
                    'state' => $addrs_province,
                    'code' => $cid,
                    'password' => $bpassword,
                    'user_id' => $uid,
                    'updatedon' => $curdt,
                    'lat' => $addrs_latitude,
                    'lng' => $addrs_longitude,
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'Tarifftype' => $tariff_type,
                    'sendto_knlogin' => $sendto_knlogin,
                    'geo_fence_radius' => $geo_fence_radius,
                    'kn_login' => preg_match('/[A-Za-z].*[0-9]|[0-9].*[A-Za-z]/', $kn_login) && strlen($kn_login) > 9 && strlen($kn_login) < 16 ? 1 : 0
                ];

                $existing_customer = Customer::where(['code' => $cid, 'org_id' => $org_id, 'status' => 1])
                    ->first();
                if ($existing_customer) {
                    $customer_id = $existing_customer->id;
                    Customer::where('id', $customer_id)->update($customer);
                    if ($cust_profile_id) {
                        $profile = CustomerProfileList::where(['party_id' => $customer_id])->first();
                        $cus_pro_list = ['cp_id' => $cust_profile_id, 'profile_id' => $cid, 'party_id' => $customer_id, 'status' => 1];
                        if ($profile) {
                            CustomerProfileList::where('id', $profile->id)->update($cus_pro_list);
                        } else {
                            CustomerProfileList::create($cus_pro_list);
                        }
                    }
                } else {
                    $customer = Customer::create($customer);
                    $customer_id = $customer->id;
                    if ($cust_profile_id) {
                        CustomerProfileList::create(['cp_id' => $cust_profile_id, 'profile_id' => $cid, 'party_id' => $customer_id, 'status' => 1]);
                    }
                }
            } */
           /*───────────────────────────────*
            |  (A) CARRIER  →  sx_users
            *───────────────────────────────*/
            $carrier_party = SxPartyTypes::whereIn('id', $partytypesary)
                            ->where('type_name', 'Carrier')
                            ->first();

            if ($carrier_party) {
                $vendor_partyid = $carrier_party->id;
                /* $existing_customer = User::where(['code' => $cid, 'org_id' => $org_id, 'status' => 1])
                    ->first();
                if ($existing_customer) {
                    $customer_id = $existing_customer->id;
                    User::where('id', $customer_id)->update([
                    'username'           => $cid ?: ('carrier_' . $partnerid),
                    'emailid'            => $addrs_email,
                    'password'           => $bpassword ?: bcrypt(Str::random(12)),
                    'employee_id'        => null,
                    'employee_name'      => $addrs_name,
                    'effective_fromdate' => now()->toDateString(),
                    'effective_enddate'  => null,
                    'contact_num'        => $addrs_phone,
                    'theme_id'           => 1,
                    'currency'           => $currency ?: 'USD',
                    'number_format'      => '###,###.##',
                    'languages'          => json_encode(['en'], JSON_UNESCAPED_SLASHES),
                    'date_format'        => 'Y-m-d',
                    'default_org_id'     => $org_id,
                        ]);
                    if ($cust_profile_id) {
                        $profile = CustomerProfileList::where(['party_id' => $customer_id])->first();
                        $cus_pro_list = ['cp_id' => $cust_profile_id, 'profile_id' => $cid, 'party_id' => $customer_id, 'status' => 1];
                        if ($profile) {
                            CustomerProfileList::where('id', $profile->id)->update($cus_pro_list);
                        } else {
                            CustomerProfileList::create($cus_pro_list);
                        }
                    }
                } else {
                    $customer = SxUser::create([
                    'username'           => $cid ?: ('carrier_' . $partnerid),
                    'emailid'            => $addrs_email,
                    'password'           => $bpassword ?: bcrypt(Str::random(12)),
                    'employee_id'        => null,
                    'employee_name'      => $addrs_name,
                    'effective_fromdate' => now()->toDateString(),
                    'effective_enddate'  => null,
                    'contact_num'        => $addrs_phone,
                    'theme_id'           => 1,
                    'currency'           => $currency ?: 'USD',
                    'number_format'      => '###,###.##',
                    'languages'          => json_encode(['en'], JSON_UNESCAPED_SLASHES),
                    'date_format'        => 'Y-m-d',
                    'default_org_id'     => $org_id,
                        ]); 
                    $customer_id = $customer->id;
                    if ($cust_profile_id) {
                        CustomerProfileList::create(['cp_id' => $cust_profile_id, 'profile_id' => $cid, 'party_id' => $customer_id, 'status' => 1]);
                    }
                } */

                // keep Party-Group insert (if you still need it)
                SxPartyGroups::create([
                    'vendor_id' => $id,  // or $vendor_id if you keep that logic
                    'type_id'   => $group_type,
                    'org_id'    => $org_id,
                    'status'    => 1,
                    'be_value'  => $be_value,
                ]);
            }

            /*───────────────────────────────*
            |  (B) CUSTOMER  →  sx_users
            *───────────────────────────────*/
            $customer_party = SxPartyTypes::whereIn('id', $partytypesary)
                            ->where('type_name', 'Customer')
                            ->first();

            if ($customer_party && $cid) {
                $customer_partyid = $customer_party->id;

                // -------- NEW: create user in sx_users --------
                /* User::create([
                    'username'           => $cid,
                    'emailid'            => $addrs_email,
                    'password'           => $bpassword ?: bcrypt(Str::random(12)),
                    'employee_id'        => null,
                    'employee_name'      => $addrs_name,
                    'effective_fromdate' => now()->toDateString(),
                    'effective_enddate'  => null,
                    'contact_num'        => $addrs_phone,
                    'theme_id'           => 1,
                    'currency'           => $currency ?: 'USD',
                    'number_format'      => '###,###.##',
                    'languages'          => json_encode(['en'], JSON_UNESCAPED_SLASHES),
                    'date_format'        => 'Y-m-d',
                    'default_org_id'     => $org_id,
                ]); */
            }

            $upd = ['customeridentifier' => $custcode];
            $upd['party_type'] = $customer_partyid ?: ($vendor_partyid ?: ($data['partytypes'][0] ?? 0));
            SxPartyMembers::where('id', $id)->update($upd);

            SxPartnerContacts::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
            if ($partnercontacts) {
                foreach ($partnercontacts as $contact) {
                    SxPartnerContacts::create([
                        'partner_id' => $id,
                        'party_type_id' => $upd['party_type'],
                        'name' => $contact['name'] ?? '',
                        'email' => $contact['email'] ?? '',
                        'phone' => $contact['phone'] ?? '',
                        'uid' => $uid,
                        'createdon' => $curdt,
                        'status' => 1
                    ]);
                }
            }

            SxPartyReferences::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
            if ($partnerrefs) {
                foreach ($partnerrefs as $refs) {
                    $refs = explode('||', $refs);
                    SxPartyReferences::create([
                        'reference_type' => $refs[0] ?? '',
                        'partner_id' => $id,
                        'name' => $refs[1] ?? '',
                        'value' => $refs[2] ?? '',
                        'description' => $refs[3] ?? '',
                        'gstin' => $refs[4] ?? '',
                        'user_id' => $uid,
                        'status' => 1,
                        'createdon' => $curdt,
                        'updatedon' => $curdt
                    ]);
                }
            }

            if ($partneraccounts) {
                SxPartnerAccounts::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
                foreach ($partneraccounts as $account) {
                    $account = explode('||', $account);
                    SxPartnerAccounts::create([
                        'partner_id' => $id,
                        'account_number' => $account[0] ?? '',
                        'acon_environment' => $account[1] ?? '',
                        'org_id' => $account[2] ?? '',
                        'source_system' => $account[3] ?? '',
                        'billable' => $account[4] ?? '',
                        'pre_payment' => $account[5] ?? '',
                        'mutiple_invoices' => $account[6] ?? '',
                        'user_id' => $uid,
                        'status' => 1,
                        'created_on' => $curdt,
                        'updated_on' => $curdt
                    ]);
                }
            }

            if ($partnercontracts) {
                SxPartnerContract::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
                foreach ($partnercontracts as $contract) {
                    $contract = explode('||', $contract);
                    SxPartnerContract::create([
                        'partner_id' => $id,
                        'org_id' => $contract[0] ?? '',
                        'be_value' => $contract[1] ?? '',
                        'contract_id' => $contract[2] ?? '',
                        'rate_derivation' => $contract[3] ?? '',
                        'user_id' => $uid,
                        'status' => 1,
                        'created_on' => $curdt,
                        'updated_on' => $curdt
                    ]);
                }
            }

            if ($savedediapiid) {
                SxPartnerEdiApi::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
                foreach ($savedediapiid as $ediapiid) {
                    SxPartnerEdiApi::where('id', $ediapiid)->update(['partner_id' => $id, 'status' => 1]);
                    if ($customer_id) {
                        $order_type = SxPartnerEdiApi::select('order_types.name as ordertype')
                            ->join('order_types', 'order_types.id', '=', 'partner_ediapi.order_type')
                            ->where('partner_ediapi.id', $ediapiid)
                            ->value('ordertype');
                        if ($order_type) {
                            $existing_ordtype = OrderType::where([
                                'type_name' => $order_type,
                                'org_id' => $org_id,
                                'be_value' => $be_value,
                                'customer_id' => $customer_id,
                                'status' => 1
                            ])->first();
                            if (!$existing_ordtype) {
                                OrderType::create([
                                    'type_name' => $order_type,
                                    'description' => $order_type,
                                    'org_id' => $org_id,
                                    'be_value' => $be_value,
                                    'customer_id' => $customer_id,
                                    'ordtype_code' => substr($order_type, 0, 1),
                                    'status' => 1,
                                    'createdon' => $curdt,
                                    'updatedon' => $curdt
                                ]);
                            }
                        }
                    }
                }
            }

            foreach ([$eBooking, $integrations, $visibility] as $ref_array) {
                if ($ref_array) {
                    foreach ($ref_array as $eachRow) {
                        $referenceName = ReferenceType::where('id', $eachRow)->value('name');
                        SxPartyReferences::create([
                            'reference_type' => $eachRow,
                            'partner_id' => $id,
                            'name' => $referenceName ?? '',
                            'value' => 'Y',
                            'user_id' => $uid,
                            'status' => 1,
                            'createdon' => $curdt,
                            'updatedon' => $curdt
                        ]);
                    }
                }
            }

            if ($org_id === 'COPELAND') {
                $ptypesData = SxPartyTypes::whereIn('id', explode(',', $partytypes))
                    ->where('status', 1)
                    ->pluck('type_name')
                    ->toArray();
                $partnerdata['partytypes'] = $ptypesData;
                // Placeholder for OTM integration
            }

            $this->logActivity([
                'description' => 'Partner updated (id: ' . $id . ')',
                'user_id' => $uid,
                'createdon' => $curdt
            ]);

            DB::commit();
            Log::info('Partner updated successfully for partner_id: ' . $id);
            return response()->json([
                'status' => 'success',
                'partner_id' => $id,
                'message' => 'Partner updated successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to update partner: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update partner: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified partner
     */
    public function partnerDestroy(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $uid = $user->id;
        $org_id = $user->org_id;
        $curdt = now();

        try {
            DB::beginTransaction();

            $partner = SxPartyMembers::where(['id' => $id, 'org_id' => $org_id])->first();
            if (!$partner) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Partner not found'
                ], 404);
            }

            SxPartyMembers::where('id', $id)->update(['status' => 0, 'deleted_at' => $curdt]);
            /* Vendor::where(['partyid' => $id, 'org_id' => $org_id])->update(['status' => 0, 'deleted_at' => $curdt]);
            Customer::where(['partner_id' => $id, 'org_id' => $org_id])->update(['status' => 0, 'deleted_at' => $curdt]);
            CustomerProfileList::where(['partner_id' => $id])->update(['status' => 0]);
            VendorProfileList::where(['party_id' => Vendor::where(['partyid' => $id, 'org_id' => $org_id])->value('id')])->update(['status' => 0]); */
            SxPartnerContacts::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
            SxPartyReferences::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
            SxPartnerAccounts::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
            SxPartnerContract::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
            SxPartnerEdiApi::where(['partner_id' => $id, 'status' => 1])->update(['status' => 0]);
            /* VendorRule::where(['vendor_id' => Vendor::where(['partyid' => $id, 'org_id' => $org_id])->value('id'), 'org_id' => $org_id])->update(['status' => 0]); */
            SxPartyGroups::where(['vendor_id' => Vendor::where(['partyid' => $id, 'org_id' => $org_id])->value('id'), 'org_id' => $org_id, 'status' => 1])->update(['status' => 0]);

            $this->logActivity([
                'description' => 'Partner deleted (id: ' . $id . ')',
                'user_id' => $uid,
                'createdon' => $curdt
            ]);

            DB::commit();
            Log::info('Partner deleted successfully for partner_id: ' . $id);
            return response()->json([
                'status' => 'success',
                'message' => 'Partner deleted successfully'
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to delete partner: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete partner: ' . $e->getMessage()
            ], 500);
        }
    }

}