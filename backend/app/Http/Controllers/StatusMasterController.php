<?php

namespace App\Http\Controllers;

use App\Models\SxPartyMembers;
use App\Models\StatusMaster;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;

class StatusMasterController extends Controller
{
    public function __construct()
    {
        // Authentication will be handled in each method
    }
    /**
     * Display a listing of the status master.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $org_id = $user->org_id ?? null;
            $be_value = $user->be_value ?? null;

            $where1 = [];
            $whr = ['sm.status' => true];

            if ($org_id === 'RUKN') {
                $whr['sm.org_id'] = $org_id;
            } else {
                $whr['sm.org_id'] = $org_id;
                $whr['sm.be_value'] = $be_value;
            }

            if ($request->isMethod('post') && $request->input('searchsubmit') === 'Searchs') {
                $input = $request->only(['status_code', 'status_name', 'customer_id']);
                $where1 = [];

                if (!empty($input['status_code'])) {
                    $where1['sm.status_code'] = $input['status_code'];
                }
                if (!empty($input['status_name'])) {
                    $where1['sm.status_name'] = $input['status_name'];
                }
                if (!empty($input['customer_id'])) {
                    $where1['m.name'] = $input['customer_id'];
                }
            } elseif ($request->isMethod('post') && $request->input('searchsubmita') === 'Search') {
                $input = $request->only(['status_code', 'status_name', 'org_id', 'be_value', 'status']);
                $where1 = [];

                if (!empty($input['status_code'])) {
                    $where1['sm.status_code'] = $input['status_code'];
                }
                if (!empty($input['status_name'])) {
                    $where1['sm.status_name'] = $input['status_name'];
                }
                if (!empty($input['customer_id'])) {
                    $where1['m.name'] = $input['customer_id'];
                }
                if (!empty($input['status'])) {
                    $where1['sm.status'] = $input['status'];
                    unset($whr['sm.status']);
                }
            }

            // Create a new query builder instance for StatusMaster
            $query = DB::table('status_master');
            $statusMaster = new StatusMaster();
            $statusm = $statusMaster->StatusList($query, $where1, $whr)->toArray();

            $datatable_headers = [
                Lang::get('leftmenu_status'),
                Lang::get('bpartner_desc'),
                Lang::get('status_type'),
                Lang::get('status_code'),
                Lang::get('general_org_id'),
                Lang::get('general_be_value'),
                Lang::get('leftmenu_status')
            ];

            $data = [
                'page_title' => Lang::get('leftmenu_status'),
                'sub_title' => Lang::get('general_search'),
                'statusm' => $statusm,
                'postData' => $request->all(),
                'datatable_headers' => $datatable_headers,
                'column_visibility' => array_fill(0, count($datatable_headers), true),
                'datatable_header_sequence' => [],
                'datatable_header_toggle' => [],
                'datatable_header_sequence_index' => array_merge([0, 1], range(2, count($datatable_headers) + 1)),
            ];

            $settings = DB::table('datatable_settings')
                ->where('controller_name', class_basename(self::class))
                ->where('method_name', 'index')
                ->where('org_id', $org_id)
                ->first();

            if ($settings) {
                if ($settings->sequence_data) {
                    $data['datatable_header_sequence'] = $datatable_header_sequence = unserialize($settings->sequence_data);
                    $datatable_header_sequence_index = [0, 1];

                    foreach ($datatable_header_sequence as $value) {
                        $index = array_search($value, $datatable_headers);
                        if ($index !== false) {
                            $datatable_header_sequence_index[] = $index + 2;
                        }
                    }

                    $datatable_header_sequence_index = array_filter($datatable_header_sequence_index);
                    $datatable_header_sequence_index = array_values($datatable_header_sequence_index);

                    foreach ($datatable_headers as $key => $header) {
                        $index = $key + 2;
                        if (!in_array($index, $datatable_header_sequence_index)) {
                            $datatable_header_sequence_index[] = $index;
                        }
                    }

                    $data['datatable_header_sequence_index'] = $datatable_header_sequence_index;
                }

                if ($settings->toggle_data) {
                    $data['datatable_header_toggle'] = unserialize($settings->toggle_data);
                    $data['column_visibility'] = array_map(function ($item) use ($data) {
                        return in_array($item, $data['datatable_header_toggle']);
                    }, $datatable_headers);
                }
            }

            return response()->json([
                'status' => true,
                'message' => 'Status master data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve status master data: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function add(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Handle case when user is not authenticated
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }

            $org_id = $user->org_id ?? $request->input('org_id');
            $be_value = $user->be_value ?? $request->input('be_value');
            $user_id = $user->id ?? 1; // Default user ID if not authenticated

            // Get business entities for the organization
            $businessEntities = [];
            if ($org_id) {
                $businessEntities = DB::table('sx_business_entity_value')
                    ->select('be_value', 'description')
                    ->where('org_id', $org_id)
                    ->where('status', true)
                    ->orderBy('be_value')
                    ->get()
                    ->toArray();
            }

            // Get status types for dropdown
            $statusTypes = [
                ['value' => 'active', 'label' => 'Active'],
                ['value' => 'inactive', 'label' => 'Inactive'],
                ['value' => 'pending', 'label' => 'Pending'],
                ['value' => 'completed', 'label' => 'Completed'],
                ['value' => 'cancelled', 'label' => 'Cancelled'],
                ['value' => 'approved', 'label' => 'Approved'],
                ['value' => 'rejected', 'label' => 'Rejected'],
                ['value' => 'processing', 'label' => 'Processing'],
                ['value' => 'shipped', 'label' => 'Shipped'],
                ['value' => 'delivered', 'label' => 'Delivered']
            ];

            // Get customers/party members
            $whr = [
                'status' => true,
            ];

            if ($org_id === 'RUKN') {
                $whr['org_id'] = $org_id;
            } else {
                $whr['user_id'] = $user_id;
            }

            // Get party members for customer selection
            $cusname = SxPartyMembers::where($whr)
                ->select(['id', 'name', 'code', 'email'])
                ->orderBy('name')
                ->get()
                ->toArray();

            // Get organizations for dropdown
            $organizations = DB::table('sx_organization')
                ->select('id', 'org_id', 'org_name')
                ->where('status', true)
                ->orderBy('org_name')
                ->get()
                ->toArray();

            $data = [
                'page_title' => Lang::get('leftmenu_status'),
                'sub_title' => Lang::get('general_add'),
                'be_value' => $be_value,
                'org_id' => $org_id,
                'user_id' => $user_id,
                'cusname' => $cusname,
                'business_entities' => $businessEntities,
                'status_types' => $statusTypes,
                'organizations' => $organizations,
                'form_fields' => [
                    'status_name' => [
                        'type' => 'text',
                        'label' => 'Status Name',
                        'required' => true,
                        'placeholder' => 'Enter status name'
                    ],
                    'description' => [
                        'type' => 'textarea',
                        'label' => 'Description',
                        'required' => false,
                        'placeholder' => 'Enter description'
                    ],
                    'status_type' => [
                        'type' => 'select',
                        'label' => 'Status Type',
                        'required' => true,
                        'options' => $statusTypes
                    ],
                    'status_code' => [
                        'type' => 'text',
                        'label' => 'Status Code',
                        'required' => true,
                        'placeholder' => 'Enter status code'
                    ],
                    'customer_id' => [
                        'type' => 'select',
                        'label' => 'Customer',
                        'required' => false,
                        'options' => $cusname
                    ],
                    'org_id' => [
                        'type' => 'select',
                        'label' => 'Organization',
                        'required' => true,
                        'options' => $organizations
                    ],
                    'be_value' => [
                        'type' => 'select',
                        'label' => 'Business Entity',
                        'required' => false,
                        'options' => $businessEntities
                    ]
                ]
            ];

            return response()->json([
                'status' => true,
                'message' => 'Add status master data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve add status master data: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Set default values for user data
            $user_id = $user ? $user->id : 1;
            $org_id = $user ? $user->org_id : ($request->input('org_id') ?: 0);
            $be_value = $user ? $user->be_value : ($request->input('be_value') ?: 0);

            // Generate unique status name and code if not provided
            $status_name = $request->input('status_name');
            if (empty($status_name)) {
                $timestamp = time();
                $status_name = "Status_{$timestamp}";
            }

            $status_code = $request->input('status_code');
            if (empty($status_code)) {
                $timestamp = time();
                $status_code = "CODE_{$timestamp}";
            }

            // Ensure unique status name and code
            $counter = 1;
            $original_status_name = $status_name;
            $original_status_code = $status_code;
            
            while (StatusMaster::where('status_name', $status_name)->exists()) {
                $status_name = $original_status_name . "_{$counter}";
                $counter++;
            }
            
            $counter = 1;
            while (StatusMaster::where('status_code', $status_code)->exists()) {
                $status_code = $original_status_code . "_{$counter}";
                $counter++;
            }

            // Prepare data with proper type casting
            $data = [
                'status_name' => $status_name,
                'description' => $request->input('description') ?: '',
                'status_type' => $request->input('status_type') ?: 'active',
                'status_code' => $status_code,
                'customer_id' => $request->input('customer_id') ? (int) $request->input('customer_id') : null,
                'user_id' => (int) $user_id,
                'org_id' => (int) $org_id,
                'be_value' => (int) $be_value,
                'status' => true,
            ];

            // Log the data being created
            Log::info('Creating StatusMaster with data:', $data);

            // Create the status master record
            $statusMaster = StatusMaster::create($data);

            Log::info('StatusMaster created successfully:', ['id' => $statusMaster->id]);

            return response()->json([
                'status' => true,
                'message' => 'Status master created successfully',
                'data' => $statusMaster
            ], 201);
        } catch (\Exception $e) {
            Log::error('Failed to create status master:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return response()->json([
                'status' => false,
                'message' => 'Failed to create status master: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function edit(int $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $org_id = $user->org_id ?? null;
            $be_value = $user->be_value ?? null;
            $user_id = $user->id ?? 1; // Default user ID if not authenticated

            // Create a new query builder instance for StatusMaster
            $query = DB::table('status_master');
            $statusMaster = new StatusMaster();
            $edit_d = $statusMaster->StatusView($query, ['sm.id' => $id]);

            if (!$edit_d) {
                return response()->json([
                    'status' => false,
                    'message' => 'Status master not found',
                    'data' => []
                ], 404);
            }

            $whr = [
                'status' => true,
            ];

            if ($org_id === 'RUKN') {
                $whr['org_id'] = $org_id;
            } else {
                $whr['user_id'] = $user_id;
            }

            // Use SxPartyMembers instead of Customer
            $cusname = SxPartyMembers::where($whr)
                ->select(['id', 'name'])
                ->get()
                ->toArray();

            $data = [
                'page_title' => Lang::get('leftmenu_status'),
                'sub_title' => Lang::get('general_edit'),
                'org_id' => $org_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'edit_d' => $edit_d,
                'cusname' => $cusname,
            ];

            return response()->json([
                'status' => true,
                'message' => 'Status master edit data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve status master edit data: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            $statusMaster = StatusMaster::find($id);

            if (!$statusMaster) {
                return response()->json([
                    'status' => false,
                    'message' => 'Status master not found',
                    'data' => []
                ], 404);
            }

            $data = [
                'status_name' => $request->input('status_name', ''),
                'description' => $request->input('description', ''),
                'status_type' => $request->input('status_type', 'no'),
                'status_code' => $request->input('status_code', ''),
                'customer_id' => $request->input('customer_id', 0),
                'org_id' => $request->input('org_id', ''),
                'be_value' => $request->input('be_value', ''),
                'status' => $request->input('status', false),
                'updatedon' => now(),
            ];

            $statusMaster->update($data);

            return response()->json([
                'status' => true,
                'message' => 'Status master updated successfully',
                'data' => $statusMaster
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to update status master: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function view(int $id): JsonResponse
    {
        try {
            // Create a new query builder instance for StatusMaster
            $query = DB::table('status_master');
            $statusMaster = new StatusMaster();
            $edit_d = $statusMaster->StatusView($query, ['sm.id' => $id]);

            if (!$edit_d) {
                return response()->json([
                    'status' => false,
                    'message' => 'Status master not found',
                    'data' => []
                ], 404);
            }

            $data = [
                'page_title' => Lang::get('leftmenu_status'),
                'sub_title' => Lang::get('general_view'),
                'edit_d' => $edit_d,
            ];

            return response()->json([
                'status' => true,
                'message' => 'Status master view data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve status master view data: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function delete(int $id): JsonResponse
    {
        try {
            $statusMaster = StatusMaster::find($id);

            if (!$statusMaster) {
                return response()->json([
                    'status' => false,
                    'message' => 'Status master not found',
                    'data' => []
                ], 404);
            }

            $statusMaster->update(['status' => false]);

            return response()->json([
                'status' => true,
                'message' => 'Status master deleted successfully',
                'data' => ['id' => $id]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete status master: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function checkName(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $org_id = $user->org_id ?? null;
            $be_value = $user->be_value ?? null;

            $id = $request->input('id', '');
            $status_name = $request->input('status_name', '');
            $where = ['status' => true];

            if ($org_id === 'RUKN') {
                $where['org_id'] = $org_id;
            } else {
                $where['org_id'] = $org_id;
                $where['be_value'] = $be_value;
            }

            $where['status_name'] = $status_name;

            if ($id) {
                $where['id !='] = $id;
            }

            $exists = StatusMaster::where($where)->select('status_name')->first();

            return response()->json([
                'status' => true,
                'message' => $exists ? 'Status name already exists' : 'Status name is available',
                'data' => ['exists' => $exists ? 1 : 2]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to check status name: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function checkCode(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $org_id = $user->org_id ?? null;
            $be_value = $user->be_value ?? null;

            $id = $request->input('id', '');
            $status_code = $request->input('status_code', '');
            $where = ['status' => true];

            if ($org_id === 'RUKN') {
                $where['org_id'] = $org_id;
            } else {
                $where['org_id'] = $org_id;
                $where['be_value'] = $be_value;
            }

            $where['status_code'] = $status_code;

            if ($id) {
                $where['id !='] = $id;
            }

            $exists = StatusMaster::where($where)->select('status_code')->first();

            return response()->json([
                'status' => true,
                'message' => $exists ? 'Status code already exists' : 'Status code is available',
                'data' => ['exists' => $exists ? 1 : 2]
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => 'Failed to check status code: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function uploadStatusMasterExcel(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $org_id = $user->org_id ?? null;
            $be_value = $user->be_value ?? null;
            $user_id = $user->id ?? 1; // Default user ID if not authenticated
            $created_on = now();

            if (!$request->hasFile('import_file')) {
                return response()->json([
                    'status' => false,
                    'message' => 'No file uploaded. Please upload a CSV file.',
                    'data' => []
                ], 400);
            }

            $file = $request->file('import_file');
            $allowedTypes = ['text/csv', 'text/plain'];

            if (!in_array($file->getMimeType(), $allowedTypes)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Invalid file type. Please upload a CSV file.',
                    'data' => []
                ], 400);
            }

            $excel_orders = [];
            $recordExists = 0;
            $checkColumns = 0;

            // Read CSV file
            $handle = fopen($file->getPathname(), 'r');
            if (!$handle) {
                return response()->json([
                    'status' => false,
                    'message' => 'Failed to read the uploaded file.',
                    'data' => []
                ], 400);
            }

            $row = 0;
            while (($data = fgetcsv($handle)) !== false) {
                $row++;
                if ($row < 2) continue; // Skip headers

                $status_name = $data[0] ?? '';
                $description = $data[1] ?? '';
                $status_type = $data[2] ?? '';
                $status_code = $data[3] ?? '';
                $org_id_input = $data[4] ?? '';
                $be_value_input = $data[5] ?? '';
                $status_field = $data[6] ?? '';
                $status = ($status_field === 'Active') ? true : false;

                if ($org_id === $org_id_input) {
                    if (!empty($status_name) && !empty($status_type) && !empty($status_code) && !empty($org_id_input) && !empty($be_value_input)) {
                        $existing = StatusMaster::where([
                            'status_name' => $status_name,
                            'org_id' => $org_id_input
                        ])->first();

                        if ($existing) {
                            $recordExists = 1;
                            $excel_orders[] = [
                                'status_name' => $status_name,
                                'org_id' => $org_id_input,
                                'upload_status' => 'Already Exists'
                            ];
                        } else {
                            $statusMaster = StatusMaster::create([
                                'status_name' => $status_name,
                                'description' => $description,
                                'status_type' => $status_type,
                                'status_code' => $status_code,
                                'customer_id' => 0,
                                'user_id' => $user_id,
                                'org_id' => $org_id_input,
                                'be_value' => $be_value_input,
                                'status' => $status,
                                'createdon' => $created_on,
                                'updatedon' => $created_on
                            ]);

                            $excel_orders[] = [
                                'status_name' => $status_name,
                                'upload_status' => 'Inserted Successfully'
                            ];
                        }
                    } else {
                        $checkColumns = 1;
                        $excel_orders[] = [
                            'status_name' => $status_name,
                            'upload_status' => 'Please Check CSV Columns'
                        ];
                    }
                } else {
                    $excel_orders[] = [
                        'status_name' => $status_name,
                        'upload_status' => 'Please Check Organization ID'
                    ];
                }
            }
            fclose($handle);

            $message = 'CSV processed successfully.';
            if ($recordExists > 0) {
                $message = 'CSV processed successfully with some existing records.';
            } elseif ($checkColumns > 0) {
                $message = 'CSV processed successfully with some column issues.';
            }

            if (empty($excel_orders)) {
                return response()->json([
                    'status' => false,
                    'message' => 'No data to process.',
                    'data' => []
                ], 400);
            }

            return response()->json([
                'status' => true,
                'message' => $message,
                'data' => ['excel_orders' => $excel_orders]
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to process CSV upload: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to process the uploaded file: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function deleteStatus(Request $request): JsonResponse
    {
        try {
            if ($request->isMethod('post')) {
                $deleteIds = $request->input('deleteids', []);

                if (empty($deleteIds)) {
                    return response()->json([
                        'status' => false,
                        'message' => 'No IDs provided for deletion',
                        'data' => []
                    ], 400);
                }

                $deleted = StatusMaster::whereIn('id', $deleteIds)->delete();

                if ($deleted) {
                    return response()->json([
                        'status' => true,
                        'message' => 'Status master records deleted successfully',
                        'data' => ['deleted_ids' => $deleteIds]
                    ], 200);
                } else {
                    return response()->json([
                        'status' => false,
                        'message' => 'Failed to delete status master records',
                        'data' => []
                    ], 500);
                }
            }

            return response()->json([
                'status' => false,
                'message' => 'Invalid request method. POST required.',
                'data' => []
            ], 405);
        } catch (\Exception $e) {
            Log::error('Failed to delete status master records: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete status master records: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }
}