<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;
use App\Models\SxPrevilleges;
use App\Models\SxPrevililegeTypes;
use App\Models\SxStructureMaster;
use App\Models\SxUserOrganization;
use App\Models\SxPartyTypes;

class CustomersController extends Controller
{
    public function __construct()
    {
        // Authentication will be handled in each method
    }
 
    /**
     * Get the customers for the current user.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getCustomers(Request $request): JsonResponse
    {
        try {
            $user = Auth::guard('api')->user();
            $userOrganization = SxUserOrganization::where('user_id', $user->id)->where('org_id', $user->default_org_id)->first();
            if (!$userOrganization) {
                return response()->json(['status' => 'fail', 'message' => 'User organization not found', 'status' => 0], 404);
            }
            $defaultOrgPrivilegeIds = json_decode($userOrganization->roles, true) ?? [];

            $defaultOrgPrivileges = SxPrevilleges::whereIn('id', $defaultOrgPrivilegeIds)->whereNull('deleted_at')->orderBy('id', 'desc')->get()->toArray();
            if (empty($defaultOrgPrivileges)) {
                return response()->json(['status' => 'fail', 'message' => 'Privileges not found', 'status' => 0], 404);
            }

            $defaultOrgStructures = SxStructureMaster::where('org_id', $user->default_org_id)->where('id', $userOrganization->structure_id)->whereNull('deleted_at')->select('id')->get();
            if (empty($defaultOrgStructures)) {
                return response()->json(['status' => 'fail', 'message' => 'Organization structure not found', 'status' => 0], 404);
            }

            $isSuperUser = false;
            foreach ($defaultOrgPrivileges as $privilege) {
                if (strtolower(str_replace(' ', '', $privilege['previllege_name'])) === 'superuser') {
                    $isSuperUser = true;
                    break;
                }
            }
dd($defaultOrgPrivileges);
            $previlegeType = $defaultOrgPrivileges[0]['previlege_type'];
            $partyTypeId = $defaultOrgPrivileges[0]['party_type'];
            $privilegeTypeData = SxPrevililegeTypes::where('id', $previlegeType)->select('type_name')->first();
            $usertype = $privilegeTypeData ? strtolower($privilegeTypeData->type_name) : null;
            $sxPartyType = '';
            if ($usertype && $usertype === 'party') {
                $partyTypeData = SxPartyTypes::where('status', 1)->where('id', $partyTypeId)->select('type_name')->first();
                $sxPartyType = $partyTypeData ? $partyTypeData->type_name : '';
            }
return response()->json([
                'previlegeType' => $previlegeType,
                'partyTypeId' => $partyTypeId,
                'usertype' => $usertype,
                'sxPartyType' => $sxPartyType,
                'defaultOrgPrivileges' => $defaultOrgPrivileges,
            ], 200);
            // Get search parameter if provided
            // $customersCode = $request->input('search', '');

            // // Laravel Query Builder equivalent
            // $query = DB::table('tb_customers')
            //     ->select('id AS value', 'name AS label')
            //     ->where('user_id', $user->id)
            //     ->where('status', 1);

            // // Add search filter if search term is provided
            // if (!empty($customersCode)) {
            //     $query->where('name', 'LIKE', '%' . $customersCode . '%');
            // }

            // $data = $query->get()->toArray();

            // return response()->json([
            //     'status' => 'success',
            //     'message' => 'Customer data retrieved successfully.',
            //     'data' => $data,
            // ], 200);
        } catch (\Exception $e) {
            Log::error('Error getting customer data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get customer data.',
            ], 500);
        }
    }
}
