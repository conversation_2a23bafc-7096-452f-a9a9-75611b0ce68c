<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\BillStatusMaster;
use App\Models\BillGroupMaster;
use App\Models\ReferenceMaster;
use App\Models\SxPartyMembers;
use App\Models\XborderCountry;
use App\Models\Order;
use App\Models\Shipment;
use App\Models\Revenue;
use App\Models\ShiporderStopSequence;
use App\Models\Bill;
use App\Models\Charge;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;


class BillingController extends Controller
{
    private function getAuthenticatedUser()
    {
        return Auth::user();
    }

    public function index(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $data = [
                'org_id' => $user->org_id ?? '',
                'be_value' => $user->be_value ?? '',
                'bstatus' => [],
                'bgroups' => [],
                'bills' => [],
                'master_billtype' => '',
                'postData' => $request->all(),
            ];

            $post = $request->input('searchsubmit') === 'Search' ? $request->all() : $request->all();
            $orderJfr = $post['order_jfr'] ?? '';
            $billIds = $this->getBillIdsByOrderJfr($orderJfr);
            $where = !empty($post) ? $this->searchbills($post) : [];
            $secondWhere = !empty($post['search_billparty']) ? ['recipient_name' => $post['search_billparty']] : [];
            $toBeBilled = $post['search_billed'] ?? 0;

            $userId = $user->user_id ?? $post['user_id'];
            if (empty($userId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }

            $data['bstatus'] = $this->getBillStatus();
            $data['bgroups'] = $this->getBillGroups();
            $getBills = $this->getBillsData($toBeBilled, $orderJfr, $userId, $billIds, $where, $secondWhere);

            if (!empty($getBills)) {
                $data['bills'] = $this->processBills($getBills, $toBeBilled);
            }

            $data['master_billtype'] = 'ShipmentX';
            Log::info("index data: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'Bill data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in index: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving bill data',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getBillIdsByOrderJfr(string $orderJfr): array
    {
        if (strlen($orderJfr) !== 14) {
            return [0];
        }

        $getBillingIds = Revenue::where([
            'type' => '0',
            'debtor_jfr' => $orderJfr,
            'invoice_status' => '>',
            'status' => '1',
            'bill_id' => '>',
        ])->pluck('bill_id')->toArray();

        return array_merge([0], $getBillingIds);
    }

    private function searchbills(array $post): array
    {
        $whr = [];
        if (!empty($post['search_billparty'])) {
            $whr['bill_party'] = $post['search_billparty'];
        }
        if (!empty($post['adv_billparty'])) {
            $whr['bill_party'] = $post['adv_billparty'];
        }
        if (!empty($post['search_billtype'])) {
            $whr['bill_type'] = $post['search_billtype'];
        }
        if (!empty($post['search_invoiceno'])) {
            $whr['invoice_no'] = $post['search_invoiceno'];
        }
        if (!empty($post['fromdate'])) {
            $whr['invoice_date >='] = date('Y-m-d', strtotime($post['fromdate']));
        }
        if (!empty($post['todate'])) {
            $whr['invoice_date <='] = date('Y-m-d', strtotime($post['todate']));
        }
        if (!empty($post['adv_shipmentno'])) {
            $whr['tracking_no'] = $post['adv_shipmentno'];
        }
        if (!empty($post['adv_billtype'])) {
            $whr['bill_type'] = $post['adv_billtype'];
        }
        if (!empty($post['bill_group'])) {
            $whr['bill_group'] = $post['bill_group'];
        }
        if (!empty($post['adv_invoiceno'])) {
            $whr['invoice_no'] = $post['adv_invoiceno'];
        }
        if (!empty($post['bill_status'])) {
            $whr['invoice_status'] = $post['bill_status'];
        }
        if (!empty($post['customer_code'])) {
            $whr['customer_code'] = $post['customer_code'];
        }
        if (!empty($post['cid'])) {
            $whr['code'] = $post['cid'];
        }
        if (!empty($post['debitor_code'])) {
            $whr['acon_code'] = $post['debitor_code'];
        }
        if (!empty($post['company_code'])) {
            $whr['org_id'] = $post['company_code'];
        }
        if (!empty($post['branch_code'])) {
            $whr['be_value'] = $post['branch_code'];
        }
        return $whr;
    }

    private function getBillStatus(): array
    {
        return BillStatusMaster::where('status', 1)
            ->select('id', 'name')
            ->get()
            ->map(fn($status) => ['id' => $status->id, 'name' => $status->name])
            ->toArray();
    }

    private function getBillGroups(): array
    {
        return BillGroupMaster::where('status', 1)
            ->select('id', 'name')
            ->get()
            ->map(fn($group) => ['id' => $group->id, 'name' => $group->name])
            ->toArray();
    }

    private function getBillsData(int $toBeBilled, string $orderJfr, int $userId, array $billIds, array $where, array $secondWhere): array
    {
        if ($toBeBilled === 1) {
            $billedId = BillStatusMaster::where('name', 'Billed')->value('id');
            if ($billedId) {
                $where['invoice_status'] = $billedId;
            }
        }

        if ($toBeBilled === 2) {
            return $this->getToBeBilledNew($userId, $secondWhere);
        }

        return $this->getBillData($userId, $orderJfr ? $billIds : [], $where);
    }

    private function processBills(array $getBills, int $toBeBilled): array
    {
        $bills = [];
        if ($toBeBilled !== 2) {
            $billIds = array_column($getBills, 'id');
            $revenuesByBillId = $this->getRevenuesByBillId($billIds);
            $orderIdsByBillId = $this->getOrderIdsByBillId($billIds);

            foreach ($getBills as $bill) {
                $bills[] = $this->formatBillData($bill, $revenuesByBillId, $orderIdsByBillId);
            }
        } else {
            foreach ($getBills as $bill) {
                $bills[] = $this->formatToBeBilledData($bill);
            }
        }
        return $bills;
    }

    private function getToBeBilledNew(int $userid, array $whr, array $whr2 = [], ?string $be_value = null, ?string $org_id = null): array
    {
        $doc = $whr2['doc'] ?? '';
        $statuscode = !empty($whr2['stop_type']) ? ['stop_type' => $whr2['stop_type'], 'status_code' => $whr2['status_code']] : [];
        $pickup = $whr2['pickup'] ?? [];
        $delivery = $whr2['delivery'] ?? [];
        $bookingid = $whr2['bookingid'] ?? [];
        $dates = $whr2['dates'] ?? [];
        $ord_ref = !empty($whr2['order_reftype']) ? ['reference_id' => $whr2['order_reftype'], 'ref_value' => $whr2['ref_val']] : [];

        $query = Revenue::select([
            'reveneus.id as revcosid',
            'reveneus.recipient_role',
            'reveneus.recipient_code',
            'reveneus.recipient_name',
            'reveneus.debtor_jfr',
            'reveneus.amount',
            'reveneus.currency',
            'reveneus.invoice_status',
            'reveneus.status',
            'orders.order_id',
            'orders.pickup_company as shipper',
            'orders.pickup_city',
            'orders.delivery_city',
            'orders.delivery_company as consignee',
            'orders.pickup_country',
            'orders.delivery_country',
            'orders.pickup_pincode',
            'orders.delivery_pincode',
            'orders.transport_mode',
            'orders.org_id',
            'orders.be_value',
            'orders.shift_id',
            'orders.trip_id',
            'orders.created_at',
        ])
            ->join('orders', 'reveneus.order_id', '=', 'orders.id')
            ->join('order_details', 'orders.id', '=', 'order_details.order_row_id')
            ->join('order_references', 'orders.id', '=', 'order_references.order_id')
            ->join('shipment', 'orders.shift_id', '=', 'shipment.id')
            ->join('stop_status', 'shipment.id', '=', 'stop_status.shipment_id')
            ->where('orders.status', '!=', 0)
            ->where('reveneus.invoice_status', 1)
            ->where('reveneus.status', 1)
            ->where($whr);

        if (!empty($ord_ref)) {
            $query->where('order_references.reference_id', $ord_ref['reference_id'])
                ->where('order_references.ref_value', 'like', "%{$ord_ref['ref_value']}%");
        }
        if (!empty($bookingid)) {
            $query->whereIn('orders.order_id', $bookingid);
        }
        if ($doc) {
            $query->whereRaw($doc);
        }
        if (!empty($dates)) {
            foreach ($dates as $key => $value) {
                $query->whereRaw($key, $value);
            }
        }
        if (!empty($statuscode)) {
            $query->where($statuscode);
        } elseif (!empty($pickup) || !empty($delivery)) {
            $query->where(function ($q) use ($pickup, $delivery) {
                if (!empty($pickup)) {
                    $q->where($pickup);
                }
                if (!empty($delivery)) {
                    $q->orWhere($delivery);
                }
            });
        }
        if ($org_id === 'RUKN') {
            $query->where('orders.org_id', $org_id);
            if ($be_value) {
                $query->where('orders.be_value', $be_value);
            }
        } else {
            $query->where('orders.user_id', $userid);
        }

        return $query->groupBy('reveneus.id')
            ->orderBy('reveneus.createdon', 'DESC')
            ->get()
            ->toArray();
    }

    private function getBillData(int $uid, array $bill_ids, array $whr): array
    {
        $org_id = $this->getAuthenticatedUser()->org_id ?? '';
        $query = Bill::select([
            'bills.*',
            'billgroup_master.name as billgroup',
            'billstatus_master.name as billstatus',
        ])
            ->leftJoin('billgroup_master', 'billgroup_master.id', '=', 'bills.bill_group')
            ->leftJoin('billstatus_master', 'billstatus_master.id', '=', 'bills.invoice_status')
            ->where($whr)
            ->whereIn('bills.status', [1, 2, 3, 4])
            ->orderBy('bills.id', 'DESC');


        $query->where('bills.user_id', $uid);
        if (!empty($bill_ids)) {
            $query->whereIn('bills.id', $bill_ids);
        }

        return $query->get()->toArray();
    }

    private function getRevenuesByBillId(array $billIds): array
    {
        $revenuesByBillId = [];
        if (!empty($billIds)) {
            $revenues = Revenue::selectRaw('SUM(amount) as amount, bill_id')
                ->whereIn('bill_id', $billIds)
                ->where('status', '1')
                ->groupBy('bill_id')
                ->get();

            foreach ($revenues as $row) {
                $revenuesByBillId[$row->bill_id] = $row->amount;
            }
        }
        return $revenuesByBillId;
    }

    private function getOrderIdsByBillId(array $billIds): array
    {
        $orderIdsByBillId = [];
        if (!empty($billIds)) {
            $orders = Revenue::select('order_id', 'bill_id')
                ->where('type', '0')
                ->whereIn('bill_id', $billIds)
                ->where('status', '!=', '0')
                ->groupBy('order_id', 'bill_id')
                ->get();

            foreach ($orders as $row) {
                $orderIdsByBillId[$row->bill_id][] = $row->order_id;
            }
        }
        return $orderIdsByBillId;
    }

    private function formatBillData(array $bill, array $revenuesByBillId, array $orderIdsByBillId): array
    {
        $aconStatus = ((int)($bill['acon_invoice_status'] ?? 0) === 1 || (int)($bill['acon_accrual_status'] ?? 0) === 1) ? 1 : 0;
        $billsType = (int)$bill['bill_type'] === 1 ? 'Single' : ((int)$bill['bill_type'] === 2 ? 'Bulk' : '');
        $totalRevAmount = $revenuesByBillId[$bill['id']] ?? 0;
        $accrualRevtblSts = (int)($bill['acon_accrual_status'] ?? 0) === 0
            ? $this->getCostStatusFromRevenueTbl($orderIdsByBillId[$bill['id']] ?? [])
            : '';

        return [
            'id' => $bill['id'],
            'tracking_no' => $bill['tracking_no'],
            'bill_party' => $bill['bill_party'],
            'bill_type' => $billsType,
            'bill_group' => $bill['billgroup'],
            'recipient_type' => $bill['recipient_type'],
            'invoice_no' => $bill['invoice_no'],
            'invoice_date' => $bill['invoice_date'],
            'invoice_status' => $bill['billstatus'],
            'acon_code' => $bill['acon_code'],
            'currency' => $bill['currency'],
            'customer_code' => $bill['customer_code'],
            'code' => $bill['code'],
            'org_id' => $bill['org_id'],
            'be_value' => $bill['be_value'],
            'status' => $bill['status'],
            'acon_status' => $aconStatus,
            'billedtype' => '0',
            'acon_accrual_status' => $bill['acon_accrual_status'] ?? '0',
            'acon_invoice_status' => $bill['acon_invoice_status'] ?? '0',
            'accrual_revtbl_sts' => $accrualRevtblSts,
            'totalRevAmount' => $totalRevAmount,
        ];
    }

    private function getCostStatusFromRevenueTbl(array $orderIds): string
    {
        $sent = [];
        $notsent = [];

        if (!empty($orderIds)) {
            $revenues = Revenue::select('id', 'invoice_status')
                ->where('type', '1')
                ->where('recipient_role', 'like', 'Carrier')
                ->where('status', '!=', '0')
                ->whereIn('order_id', $orderIds)
                ->get();

            foreach ($revenues as $cst) {
                if ($cst->invoice_status == '2') {
                    $sent[] = $cst->id;
                } else {
                    $notsent[] = $cst->id;
                }
            }
        }

        if (!empty($sent) && !empty($notsent)) {
            return 'Accrual Partially sent to ACON';
        }
        if (!empty($sent) && empty($notsent)) {
            return 'Accrual Sent to ACON';
        }
        if (empty($sent) && !empty($notsent)) {
            return 'Accrual Not sent to ACON';
        }
        return 'No Accruals';
    }

    private function formatToBeBilledData(array $bill): array
    {
        return [
            'id' => $bill['revcosid'],
            'tracking_no' => $bill['order_id'],
            'bill_party' => $bill['recipient_name'],
            'bill_type' => '',
            'bill_group' => '',
            'recipient_type' => $bill['recipient_role'],
            'invoice_no' => '',
            'invoice_date' => '',
            'invoice_status' => '',
            'acon_code' => $bill['debtor_jfr'],
            'currency' => $bill['currency'],
            'customer_code' => '',
            'code' => '',
            'org_id' => $bill['org_id'],
            'be_value' => $bill['be_value'],
            'status' => $bill['status'],
            'acon_status' => 1,
            'billedtype' => '2',
            'acon_invoice_status' => '0',
            'acon_accrual_status' => '0',
            'accrual_revtbl_sts' => '',
            'totalRevAmount' => $bill['amount'],
        ];
    }

    public function newBill(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => []
            ], 401);
        }

        $post = $request->all() ?? [];

        $orgId = $user->org_id ?? $post['org_id'] ?? '';
        $beValue = $user->be_value ?? $post['be_value'] ?? '';

        try {
            $billStatuses = BillStatusMaster::select('id', 'name')
                ->where('status', 1)
                ->get()
                ->map(function ($status) {
                    return [
                        'id' => $status->id,
                        'name' => $status->name
                    ];
                })->toArray();

            $billGroups = BillGroupMaster::select('id', 'name')
                ->where('status', 1)
                ->whereIn('name', ['Invoice', 'Bill'])
                ->get()
                ->map(function ($group) {
                    return [
                        'id' => $group->id,
                        'name' => $group->name
                    ];
                })->toArray();

            $data = [
                'bstatus' => $billStatuses,
                'bgroups' => $billGroups,
                'ref_names_arr' => $this->getRefNums()
            ];

            Log::info("newBill data: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'Bill configuration data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in newBill: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving bill configuration data',
                'data' => [],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getRefNums()
    {
        $result = cache()->remember('referenceMaster', 60 * 60, function () {
            return ReferenceMaster::select('name')
                ->distinct()
                ->where('status', 1)
                ->orderBy('name', 'ASC')
                ->pluck('name')
                ->toArray();
        });

        return $result;
    }

    public function viewRoleTypeList(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => []
            ], 401);
        }

        $post = $request->all() ?? [];
        $type = $post['type'] ?? '';

        if (empty($type)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Type is required',
                'data' => []
            ], 422);
        }

        $orgId = $user->org_id ?? $post['org_id'] ?? 0;
        $userId = $user->user_id ?? $post['user_id'] ?? 0;

        try {
            $roleTypeList = $this->getRoleTypeList($userId, $orgId, $type);
            Log::info("roleTypeList: " . json_encode($roleTypeList));

            if ($roleTypeList->isNotEmpty()) {
                $data = $roleTypeList->map(function ($list) use ($orgId) {
                    return [
                        'check' => "<input class='twopartieslist' type='radio' name='selectparties' id='twopartieslist_{$list->id}' value='{$list->code}' onchange=selectpartiesbyid({$list->id})> <input type='hidden' id='customer_org_{$list->id}' name='customer_org_{$list->id}' value='{$orgId}'>",
                        'id' => $list->id,
                        'code' => $list->code,
                        'name' => $list->name,
                        'email_id' => $list->email,
                        'org_id' => $orgId,
                        'be_value' => $list->be_value,
                    ];
                })->toArray();

                return response()->json([
                    'status' => 'success',
                    'message' => 'Role type list retrieved successfully',
                    'data' => $data
                ], 200);
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'No records found for the specified type and organization',
                    'data' => []
                ], 200);
            }
        } catch (\Exception $e) {
            Log::error("Error in viewRoleTypeList: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving the role type list',
                'data' => [],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getRoleTypeList(int $userId, string $orgId, string $type)
    {
        $xborder = XborderCountry::select('xborder_code')
            ->where('org_id', $orgId)
            ->first();

        $query = SxPartyMembers::select('sx_party_members.id', 'sx_party_members.name', 'sx_party_members.email', 'sx_party_members.code', 'sx_party_members.customeridentifier', 'sx_party_members.org_id', 'sx_party_members.be_value')
            ->leftJoin('sx_party_types', 'sx_party_types.id', '=', 'sx_party_members.party_type')
            ->where('sx_party_types.type_name', $type)
            ->where('sx_party_members.org_id', $orgId);

        if ($xborder && !empty($xborder->xborder_code)) {
            $xborderCompanies = explode(',', $xborder->xborder_code);
            $xborderCompanies[] = $orgId;
            $query->whereIn('sx_party_members.org_id', $xborderCompanies);
        }

        $query->where('sx_party_members.org_id', $orgId);


        return $query->get();
    }

    public function getPartnerDetailsById(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => []
            ], 401);
        }

        $post = $request->all() ?? [];
        $code = $post['id'] ?? '';
        $user_id = $user->user_id ?? $post['user_id'] ?? 0;
        $type = $post['type'] ?? '';
        $beValue = $post['be_value'] ?? '';
        $org_id = $user->org_id ?? $post['org_id'] ?? 0;

        try {
            $data = ['tobebilled' => []];

            $partnerDetails = $this->getPartnerDetailsByCode($code, $org_id);
            if ($partnerDetails) {
                $data = [
                    'id' => $partnerDetails->id,
                    'name' => $partnerDetails->name,
                    'code' => $partnerDetails->code,
                    'acon' => $partnerDetails->acon_debitor_code,
                    'org_id' => $partnerDetails->org_id,
                    'be_value' => $beValue,
                    'customer_code' => $partnerDetails->customeridentifier,
                    'invoice_type' => $partnerDetails->invoice_type ?? 0,
                    'tobebilled' => []
                ];
            } else {
                return response()->json([
                    'status' => 'success',
                    'message' => 'No partner details found for the specified code',
                    'data' => ['tobebilled' => []]
                ], 200);
            }

            $toBeBilled = $this->getToBeBilled($user_id, [
                'recipient_code' => $code,
                'recipient_role' => $type,
            ], [], $beValue);

            $toBeBilledData = [];
            foreach ($toBeBilled as $res) {
                $tripStatus = '';
                $chkStatus = ($res->invoice_status == 1 && $res->status == 1) ? 1 : 0;
                $tripId = $res->trip_id ?? 0;
                $shiftId = $res->shift_id ?? 0;

                if ($shiftId > 0) {
                    $shipment = Shipment::select('id')
                        ->where('shift_leg_id', $shiftId)
                        ->get();

                    if ($shipment->isNotEmpty()) {
                        $legIds = $shipment->pluck('id')->toArray();
                        $tripIds = $this->checkDriverAcceptsForNewTrips($legIds);

                        $acceptance = 1;
                        $newTripId = 0;
                        foreach ($tripIds as $trip) {
                            $tripRowId = $trip->trip_id;
                            if ($tripRowId > 0) {
                                $newTripId = $tripRowId;
                            } else {
                                $acceptance = 0;
                            }
                        }

                        $tripStatus = $acceptance == 0
                            ? "<input type='hidden' name='noorder_trip_sts' id='{$res->revcosid}' class='noorder_trip_sts' value='{$newTripId}'>"
                            : "<input type='hidden' name='order_trip_sts' id='{$res->revcosid}' class='order_trip_sts' value='{$newTripId}'>";
                    } else {
                        $tripStatus = $tripId == 0
                            ? "<input type='hidden' name='noorder_trip_sts' id='{$res->revcosid}' class='noorder_trip_sts' value='{$tripId}'>"
                            : "<input type='hidden' name='order_trip_sts' id='{$res->revcosid}' class='order_trip_sts' value='{$tripId}'>";
                    }
                } else {
                    $tripStatus = "<input type='hidden' name='noorder_trip_sts' id='{$res->revcosid}' class='noorder_trip_sts' value='{$tripId}'>";
                }

                if ($chkStatus == 1) {
                    $concatId = "{$res->revcosid}_{$res->recipient_role}_{$res->currency}";
                    $tdValue = "<input type='hidden' form='bill_data' name='recipienttype' class='recipienttype' value='{$res->recipient_role}' ><input type='hidden' form='bill_data' name='currency' class='currency' value='{$res->currency}' ><input type='checkbox' form='bill_data' name='tracking_no[]' class='tracking_no' id='{$res->revcosid}' value='{$concatId}' style='display:none;'><input type='radio' form='bill_data' name='trackingno' class='trackingno' value='{$res->revcosid}' >{$tripStatus}";

                    $toBeBilledData[] = [
                        'rev_cosid' => $res->revcosid,
                        'tdvalue' => $tdValue,
                        'order_id' => $res->order_id,
                        'shipper' => $res->shipper,
                        'pickup_city' => $res->pickup_city,
                        'pickup_pincode' => $res->pickup_pincode,
                        'pickup_country' => $res->pickup_country,
                        'consignee' => $res->consignee,
                        'delivery_city' => $res->delivery_city,
                        'delivery_pincode' => $res->delivery_pincode,
                        'delivery_country' => $res->delivery_country,
                        'transport_mode' => $res->transport_mode,
                        'amount' => $res->amount,
                        'currency' => $res->currency,
                        'createdon' => $res->createdon->format('Y-m-d'),
                    ];
                }
            }
            $data['tobebilled'] = $toBeBilledData;
            Log::info("getPartnerDetailsById data: " . json_encode($data));
            return response()->json([
                'status' => 'success',
                'message' => 'Partner details and to-be-billed data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getPartnerDetailsById: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving partner details',
                'data' => ['tobebilled' => []],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getPartnerDetailsByCode(string $code, string $org_id)
    {
        return SxPartyMembers::select('id', 'name', 'code', 'org_id', 'be_value', 'acon_debitor_code', 'customeridentifier')
            ->where('code', $code)
            ->where('org_id', $org_id)
            ->where('status', 1)
            ->first();
    }

    private function getToBeBilled(int $userId, array $where, array $where2 = [], string $beValue = '')
    {
        $query = Order::select(
            'reveneus.id as revcosid',
            'reveneus.recipient_role',
            'reveneus.recipient_code',
            'reveneus.recipient_name',
            'reveneus.debtor_jfr',
            'reveneus.amount',
            'reveneus.currency',
            'reveneus.invoice_status',
            'reveneus.status',
            'orders.order_id',
            'orders.pickup_company as shipper',
            'orders.pickup_city',
            'orders.delivery_city',
            'orders.delivery_company as consignee',
            'orders.pickup_country',
            'orders.delivery_country',
            'orders.pickup_pincode',
            'orders.delivery_pincode',
            'orders.transport_mode',
            'orders.org_id',
            'orders.be_value',
            'orders.shift_id',
            'orders.trip_id',
            'orders.created_at'
        )
            ->join('reveneus', 'orders.id', '=', 'reveneus.order_id')
            ->join('order_details', 'orders.id', '=', 'order_details.order_row_id')
            ->join('order_references', 'orders.id', '=', 'order_references.order_id')
            ->join('shipment', 'orders.shift_id', '=', 'shipment.id')
            ->join('stop_status', 'shipment.id', '=', 'stop_status.shipment_id')
            ->where('orders.status', '!=', 0)
            ->where('reveneus.invoice_status', 1)
            ->where('reveneus.status', 1)
            ->where($where);

        if (!empty($where2)) {
            if (isset($where2['doc'])) {
                $query->whereRaw($where2['doc']);
            }
            if (isset($where2['stop_type'])) {
                $query->where([
                    'stop_status.stop_type' => $where2['stop_type'],
                    'stop_status.status_code' => $where2['status_code'],
                ]);
            }
            if (isset($where2['pickup'])) {
                $query->where($where2['pickup']);
            }
            if (isset($where2['delivery'])) {
                $query->where($where2['delivery']);
            }
            if (isset($where2['bookingid'])) {
                $query->whereIn('orders.order_id', $where2['bookingid']);
            }
            if (isset($where2['dates'])) {
                $query->where($where2['dates']);
            }
            if (isset($where2['order_reftype'])) {
                $query->where([
                    'order_references.reference_id' => $where2['order_reftype'],
                    'order_references.ref_value' => $where2['ref_val'],
                ]);
            }
        }


        $query->where('orders.user_id', $userId);

        return $query->groupBy('reveneus.id')
            ->orderBy('reveneus.createdon', 'DESC')
            ->get();
    }

    public function billsfilter(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json(['status' => 'error', 'message' => 'Invalid or missing token', 'data' => ['tobebilled' => []]], 401);
        }

        $post = $request->all() ?? [];
        $code = $post['customercode'] ?? '';
        $type = $post['type'] ?? '';
        $billid = $post['billid'] ?? '';
        if ($billid === 'undefined') {
            $billid = '';
        }
        $bill_type = $post['bill_type'] ?? '';
        $earlypickupfrom = $post['earlypickupfrom'] ?? '';
        $earlypickupto = $post['earlypickupto'] ?? '';
        $earlydeliveryfrom = $post['earlydeliveryfrom'] ?? '';
        $earlydeliveryto = $post['earlydeliveryto'] ?? '';
        $pickupfrom = $post['pickupfrom'] ?? '';
        $pickupto = $post['pickupto'] ?? '';
        $deliveryfrom = $post['deliveryfrom'] ?? '';
        $deliveryto = $post['deliveryto'] ?? '';
        $order_status = $post['order_status'] ?? '';
        $doc_status = $post['doc_status'] ?? '';
        $order_reftype = $post['order_reftype'] ?? '';
        $ref_val = $post['ref_val'] ?? '';
        $userId = $user->user_id ?? $post['user_id'] ?? 0;
        $orgId = $user->org_id ?? $post['org_id'] ?? 0;
        $custId = $user->cust_id ?? $post['cust_id'] ?? 0;
        $bookingid = !empty($post['bookingid']) ? (array)$post['bookingid'] : [];

        if (empty($code) && empty($billid)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Either customercode or billid is required',
                'data' => ['tobebilled' => []]
            ], 422);
        }

        try {
            $data = ['tobebilled' => []];

            if ($billid !== '' && is_numeric($billid) && $billid > 0) {
                $billdata = [
                    'code' => $code,
                    'type' => $type,
                    'week' => 'All',
                    'bill_id' => $billid,
                    'status' => 1, // Assuming status for to-be-billed
                    'bill_type' => $bill_type,
                    'pickupfrom' => $pickupfrom,
                    'pickupto' => $pickupto,
                    'deliveryfrom' => $deliveryfrom,
                    'deliveryto' => $deliveryto,
                    'order_status' => $order_status,
                    'doc_status' => $doc_status,
                    'order_reftype' => $order_reftype,
                    'ref_val' => $ref_val,
                    'earlypickupfrom' => $earlypickupfrom,
                    'earlypickupto' => $earlypickupto,
                    'earlydeliveryfrom' => $earlydeliveryfrom,
                    'earlydeliveryto' => $earlydeliveryto,
                    'bookingid' => $bookingid,
                ];
                $tobebilled = $this->getbilledandtobebilled($userId, $custId ?? 0, [], $billdata, $orgId ?? '');
            } elseif ($code !== '' && $billid === '') {
                $whr = [
                    'recipient_code' => $code,
                    'recipient_role' => $type,
                ];
                $whr2 = [];
                $dates = [];

                if ($earlypickupfrom !== '') {
                    $earlypickupfrom = date('Y-m-d', strtotime($earlypickupfrom));
                    $dates["pickup_datetime"] = ['>=', $earlypickupfrom];
                }
                if ($earlypickupto !== '') {
                    $earlypickupto = date('Y-m-d', strtotime($earlypickupto));
                    $dates["pickup_datetime"] = array_merge($dates["pickup_datetime"] ?? [], ['<=', $earlypickupto]);
                }
                if ($earlydeliveryfrom !== '') {
                    $earlydeliveryfrom = date('Y-m-d', strtotime($earlydeliveryfrom));
                    $dates["delivery_datetime"] = ['>=', $earlydeliveryfrom];
                }
                if ($earlydeliveryto !== '') {
                    $earlydeliveryto = date('Y-m-d', strtotime($earlydeliveryto));
                    $dates["delivery_datetime"] = array_merge($dates["delivery_datetime"] ?? [], ['<=', $earlydeliveryto]);
                }
                if ($pickupfrom !== '') {
                    $pickupfrom = date('Y-m-d', strtotime($pickupfrom));
                    $dates["stop_createdon"] = ['>=', $pickupfrom];
                    $whr2['pickup'] = ['stop_type' => 'P', 'status_code' => '0500'];
                }
                if ($pickupto !== '') {
                    $pickupto = date('Y-m-d', strtotime($pickupto));
                    $dates["stop_createdon"] = array_merge($dates["stop_createdon"] ?? [], ['<=', $pickupto]);
                    $whr2['pickup'] = ['stop_type' => 'P', 'status_code' => '0500'];
                }
                if ($deliveryfrom !== '') {
                    $deliveryfrom = date('Y-m-d', strtotime($deliveryfrom));
                    $dates["stop_createdon"] = ['>=', $deliveryfrom];
                    $whr2['delivery'] = ['stop_type' => 'D', 'status_code' => '2300'];
                }
                if ($deliveryto !== '') {
                    $deliveryto = date('Y-m-d', strtotime($deliveryto));
                    $dates["stop_createdon"] = array_merge($dates["stop_createdon"] ?? [], ['<=', $deliveryto]);
                    $whr2['delivery'] = ['stop_type' => 'D', 'status_code' => '2300'];
                }
                if ($order_status !== '') {
                    $whr2['stop_type'] = $order_status === 'pickup' ? 'P' : 'D';
                    $whr2['status_code'] = $order_status === 'pickup' ? '0500' : '2300';
                }
                if ($doc_status === 'yes') {
                    $whr2['doc'] = 'docs_received_datetime IS NOT NULL AND docs_received_datetime != ""';
                } elseif ($doc_status === 'no') {
                    $whr2['doc'] = 'docs_received_datetime IS NULL OR docs_received_datetime = ""';
                }
                if ($order_reftype !== '' && $ref_val !== "'") {
                    $whr2['order_reftype'] = $order_reftype;
                    $whr2['ref_val'] = $ref_val;
                }
                if (!empty($bookingid)) {
                    $whr2['bookingid'] = $bookingid;
                }
                $whr2['dates'] = $dates;

                $tobebilled = $this->gettobebilled($userId, $whr, $whr2, $post['be_value'] ?? '', $orgId ?? '');
            }

            $tobebilledData = [];
            foreach ($tobebilled as $res) {
                $chk_status = ($res->invoice_status == 1 && $res->status == 1) ? 1 : 0;
                if ($res->recipient_code == $code && $chk_status == 1) {
                    $concatid = "{$res->revcosid}_{$res->recipient_role}_{$res->currency}";
                    $tdvalue = "<input type='hidden' form='bill_data' name='recipienttype' class='recipienttype' value='{$res->recipient_role}'><input type='hidden' form='bill_data' name='currency' class='currency' value='{$res->currency}'><input type='checkbox' form='bill_data' name='tracking_no[]' class='tracking_no' value='{$concatid}' style='display:none;'><input type='radio' form='bill_data' name='trackingno' class='trackingno' value='{$res->revcosid}'>";
                    $tobebilledData[] = [
                        'rev_cosid' => $res->revcosid,
                        'tdvalue' => $tdvalue,
                        'order_id' => $res->order_id,
                        'shipper' => $res->shipper,
                        'pickup_city' => $res->pickup_city,
                        'pickup_pincode' => $res->pickup_pincode,
                        'pickup_country' => $res->pickup_country,
                        'consignee' => $res->consignee,
                        'delivery_city' => $res->delivery_city,
                        'delivery_pincode' => $res->delivery_pincode,
                        'delivery_country' => $res->delivery_country,
                        'transport_mode' => $res->transport_mode,
                        'amount' => $res->amount,
                        'currency' => $res->currency,
                        'createdon' => $res->createdon->format('Y-m-d'),
                    ];
                }
            }

            $data['tobebilled'] = $tobebilledData;
            Log::info("billsfilter data: " . json_encode($data));
            return response()->json([
                'status' => 'success',
                'message' => 'To-be-billed data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in billsfilter: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving to-be-billed data',
                'data' => ['tobebilled' => []],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getbilledandtobebilled(int $userid, int $custid, array $countryuids, array $whr, string $org_id)
    {
        $billid = $whr['bill_id'] ?? 0;
        $revstatus = $whr['status'] ?? 1;
        $code = $whr['code'] ?? '';

        $query = Revenue::select([
            'reveneus.id as revcosid',
            'reveneus.recipient_role',
            'reveneus.recipient_code',
            'reveneus.recipient_name',
            'reveneus.debtor_jfr',
            'reveneus.amount',
            'reveneus.currency',
            'reveneus.invoice_status',
            'reveneus.bill_id',
            'orders.order_id',
            'orders.pickup_company as shipper',
            'orders.pickup_city',
            'orders.delivery_city',
            'orders.delivery_company as consignee',
            'orders.pickup_country',
            'orders.delivery_country',
            'orders.pickup_pincode',
            'orders.delivery_pincode',
            'orders.transport_mode',
            'orders.org_id',
            'orders.be_value',
            'orders.shift_id',
            'orders.trip_id',
            'orders.created_at',
        ])
            ->join('orders', 'reveneus.order_id', '=', 'orders.id')
            ->join('order_details', 'orders.id', '=', 'order_details.order_row_id')
            ->join('order_references', 'orders.id', '=', 'order_references.order_id')
            ->join('shipment', 'orders.shift_id', '=', 'shipment.id')
            ->join('stop_status', 'shipment.id', '=', 'stop_status.shipment_id')
            ->where('orders.status', '!=', 0)
            ->where('reveneus.recipient_code', $code)
            ->where('reveneus.status', 1);

        if (!empty($whr['bookingid'])) {
            $query->whereIn('orders.order_id', (array)$whr['bookingid']);
        }

        if (isset($whr['earlypickupfrom']) && $whr['earlypickupfrom'] !== '') {
            $query->whereDate('orders.pickup_datetime', '>=', date('Y-m-d', strtotime($whr['earlypickupfrom'])));
        }
        if (isset($whr['earlypickupto']) && $whr['earlypickupto'] !== '') {
            $query->whereDate('orders.pickup_datetime', '<=', date('Y-m-d', strtotime($whr['earlypickupto'])));
        }
        if (isset($whr['earlydeliveryfrom']) && $whr['earlydeliveryfrom'] !== '') {
            $query->whereDate('orders.delivery_datetime', '>=', date('Y-m-d', strtotime($whr['earlydeliveryfrom'])));
        }
        if (isset($whr['earlydeliveryto']) && $whr['earlydeliveryto'] !== '') {
            $query->whereDate('orders.delivery_datetime', '<=', date('Y-m-d', strtotime($whr['earlydeliveryto'])));
        }
        if (isset($whr['pickupfrom']) && $whr['pickupfrom'] !== '') {
            $query->whereDate('stop_status.createdon', '>=', date('Y-m-d', strtotime($whr['pickupfrom'])))
                ->where('stop_status.stop_type', 'P')
                ->where('stop_status.status_code', '0500');
        }
        if (isset($whr['pickupto']) && $whr['pickupto'] !== '') {
            $query->whereDate('stop_status.createdon', '<=', date('Y-m-d', strtotime($whr['pickupto'])))
                ->where('stop_status.stop_type', 'P')
                ->where('stop_status.status_code', '0500');
        }
        if (isset($whr['deliveryfrom']) && $whr['deliveryfrom'] !== '') {
            $query->whereDate('stop_status.createdon', '>=', date('Y-m-d', strtotime($whr['deliveryfrom'])))
                ->where('stop_status.stop_type', 'D')
                ->where('stop_status.status_code', '2300');
        }
        if (isset($whr['deliveryto']) && $whr['deliveryto'] !== '') {
            $query->whereDate('stop_status.createdon', '<=', date('Y-m-d', strtotime($whr['deliveryto'])))
                ->where('stop_status.stop_type', 'D')
                ->where('stop_status.status_code', '2300');
        }
        if (isset($whr['order_status']) && $whr['order_status'] !== '') {
            $stop_type = $whr['order_status'] === 'pickup' ? 'P' : 'D';
            $status_code = $whr['order_status'] === 'pickup' ? '0500' : '2300';
            $query->where('stop_status.stop_type', $stop_type)
                ->where('stop_status.status_code', $status_code);
        }
        if (isset($whr['doc_status']) && $whr['doc_status'] !== '') {
            if ($whr['doc_status'] === 'yes') {
                $query->whereNotNull('order_details.docs_received_datetime');
            } elseif ($whr['doc_status'] === 'no') {
                $query->whereNull('order_details.docs_received_datetime');
            }
        }
        if (isset($whr['order_reftype']) && $whr['order_reftype'] !== '' && isset($whr['ref_val']) && $whr['ref_val'] !== '') {
            $query->where('order_references.reference_id', $whr['order_reftype'])
                ->where('order_references.ref_value', 'LIKE', '%' . $whr['ref_val'] . '%');
        }

        if ($revstatus == 1) {
            $query->whereIn('reveneus.bill_id', [$billid, 0])
                ->whereIn('reveneus.invoice_status', [1, 2]);
        } else {
            $query->where('reveneus.bill_id', $billid)
                ->where('reveneus.invoice_status', 2);
        }

        $query->where('orders.user_id', $userid);

        return $query->groupBy('reveneus.id')
            ->orderBy('reveneus.invoice_status', 'DESC')
            ->get();
    }

    public function insertbilldata(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        $post = $request->all() ?? [];
        $data = ['status' => 0];
        $curdt = Carbon::now()->format('Y-m-d H:i:s');
        $uid = $user->user_id ?? $post['user_id'];
        $bill_type = $post['bill_type'] ?? 0;
        $bill_group = $post['bill_group'] ?? 0;
        $customercode = $post['customercode'] ?? '';
        $org_id = $post['org_id'] ?? $user->org_id ?? '';
        $be_value = $post['be_value'] ?? '';
        $partner_cid = $post['partner_cid'] ?? '';
        $debitor_code = $post['partner_debitor_code'] ?? '';
        $invoice_no = $post['invoice_no'] ?? 1;
        $invoice_date = !empty($post['invoice_date']) ? date('Y-m-d', strtotime($post['invoice_date'])) : null;
        $bill_status = $post['bill_status'] ?? 0;
        $trackingno = $post['trackingno'] ?? 0;
        $tracking_no = !empty($post['tracking_no']) ? (array)$post['tracking_no'] : [];
        $recipienttype = $post['recipienttype'] ?? '';
        $currency = $post['currency'] ?? '';
        $status = $post['status'] ?? 1;
        $name = $post['name'] ?? '';
        $bill_id = $post['billid'] ?? ''; // For credit and debit note
        $full_amount = !empty($post['full_amount']) ? $post['full_amount'] : 0;
        $invoice_description = $post['invoice_description'] ?? '';

        if (empty($bill_type)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Bill type is required',
                'data' => ['status' => 0]
            ], 422);
        }
        if (empty($bill_group)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Bill group is required',
                'data' => ['status' => 0]
            ], 422);
        }
        if (empty($customercode)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Customer code is required',
                'data' => ['status' => 0]
            ], 422);
        }
        if ($trackingno == 0 && empty($tracking_no) && empty($bill_id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Either trackingno or tracking_no is required for new bills',
                'data' => ['status' => 0]
            ], 422);
        }

        try {
            $groupname = '';
            if ($bill_group > 0) {
                $groupname = $this->getgroupname($bill_group);
            }

            $invoice_statusname = '';
            $billStatus = BillStatusMaster::where('id', $bill_status)->first();
            if ($billStatus) {
                $invoice_statusname = $billStatus->name;
            }

            if ($status > 1 && $invoice_statusname === 'Open') {
                $billedStatus = BillStatusMaster::where('name', 'Billed')->first();
                if ($billedStatus) {
                    $bill_status = $billedStatus->id;
                }
            }

            $billid = $bill_id;
            if (empty($bill_id)) {
                if ($trackingno > 0 || !empty($tracking_no)) {
                    $insdata = [
                        'revenue_id' => 0,
                        'tracking_no' => '0',
                        'bill_party' => $name,
                        'bill_type' => $bill_type,
                        'bill_group' => $bill_group,
                        'recipient_type' => $groupname,
                        'invoice_date' => $invoice_date,
                        'invoice_status' => $bill_status,
                        'acon_code' => $debitor_code,
                        'total_amount' => 0,
                        'currency' => $currency,
                        'customer_code' => $customercode,
                        'code' => $partner_cid,
                        'org_id' => $org_id,
                        'be_value' => $be_value,
                        'user_id' => $uid,
                        'status' => $status,
                        'created_at' => $curdt,
                        'updated_at' => $curdt,
                        'invoice_description' => $invoice_description,
                    ];
                    $bill = Bill::create($insdata);
                    $billid = $bill->id;
                    $invoiceno = $this->generatecountryinvoicenos($billid, $uid);
                    $bill->update(['invoice_no' => $invoiceno]);
                    $data['status'] = 1;
                }
            } elseif ($bill_id > 0) {
                $chkparentbillid = $this->chkparentbillid($bill_id);
                if ($chkparentbillid > 0) {
                    $insdata = [
                        'revenue_id' => 0,
                        'tracking_no' => '0',
                        'bill_party' => $name,
                        'bill_type' => $bill_type,
                        'bill_group' => $bill_group,
                        'recipient_type' => $groupname,
                        'invoice_date' => $invoice_date,
                        'invoice_status' => $bill_status,
                        'acon_code' => $debitor_code,
                        'total_amount' => 0,
                        'currency' => $currency,
                        'customer_code' => $customercode,
                        'code' => $partner_cid,
                        'org_id' => $org_id,
                        'be_value' => $be_value,
                        'parent_billid' => $bill_id,
                        'user_id' => $uid,
                        'status' => $status,
                        'created_at' => $curdt,
                        'updated_at' => $curdt,
                        'invoice_description' => $invoice_description,
                    ];
                    $bill = Bill::create($insdata);
                    $billid = $bill->id;
                    $invoiceno = $this->generatecountryinvoicenos($billid, $uid);
                    $bill->update(['invoice_no' => $invoiceno]);
                    Bill::where('id', $bill_id)->update(['status' => 3]);
                    $data['status'] = 1;
                } else {
                    Bill::where('id', $billid)->update([
                        'invoice_date' => $invoice_date,
                        'status' => $status,
                    ]);
                    $invoiceno = $this->getinvoiceno($billid);
                }
            }

            if ($billid > 0) {
                if (empty($bill_id)) {
                    $updata = [
                        'invoice_number' => $invoiceno,
                        'invoice_date' => $invoice_date,
                        'invoice_creation_date' => $curdt,
                        'invoice_status' => 2,
                        'bill_id' => $billid,
                    ];
                    if ($bill_type == '1' && $trackingno > 0) {
                        Revenue::where('id', $trackingno)->update($updata);
                    } elseif ($bill_type == '2' && !empty($tracking_no)) {
                        $ntrackingno = array_map(function ($ex) {
                            return explode('_', $ex)[0];
                        }, $tracking_no);
                        Revenue::whereIn('id', $ntrackingno)->update($updata);
                    }
                } elseif ($bill_id > 0 && in_array($status, [3, 4])) {
                    $info = [
                        'uid' => $uid,
                        'bill_id' => $bill_id,
                        'invoiceno' => $invoiceno,
                        'name' => $name,
                        'billid' => $billid,
                        'status' => $status,
                    ];
                    $this->generateCreditDebitnote($info);
                }
            }
            Log::info("insertbilldata data: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'Bill data inserted successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in insertbilldata: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while inserting bill data',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getgroupname(int $bill_group): string
    {
        $groupname = '';
        $billGroup = BillGroupMaster::where('id', $bill_group)->first();
        if ($billGroup) {
            $name = $billGroup->name;
            if (in_array(strtolower($name), ['invoice', 'debit note'])) {
                $groupname = 'Carrier';
            } elseif (in_array(strtolower($name), ['bill', 'credit note'])) {
                $groupname = 'Customer';
            }
        }
        return $groupname;
    }

    private function generatecountryinvoicenos(int $id, int $uid): string
    {
        $invoiceno = '';
        $btype = 'I';
        $bill = Bill::where('id', $id)->first();
        if ($bill && $bill->bill_group > 0) {
            $billGroup = BillGroupMaster::where('id', $bill->bill_group)->where('status', 1)->first();
            if ($billGroup) {
                $type = strtolower($billGroup->name);
                if ($type === 'invoice' || $type === 'bill') {
                    $btype = 'I';
                } elseif ($type === 'credit note') {
                    $btype = 'C';
                } elseif ($type === 'debit note') {
                    $btype = 'D';
                }
            }
        }

        $org_id = $bill->org_id ?? '';
        $be_value = $bill->be_value ?? '';
        if ($org_id) {
            $cntry_code = substr($org_id, 0, 2);
            $branch_officeno = str_pad(mt_rand(0, 99), 2, '0', STR_PAD_LEFT);
            /*  $branch_officeno = '00';
            $branch = BranchMaster::where('branch_code', $be_value)->where('status', 1)->first();
            if ($branch && $branch->branch_officeno) {
                $branch_officeno = substr($branch->branch_officeno, -2);
            } */
            $bno = $branch_officeno . $btype;
            $year = date('y');
            $mnth = date('m');
            $general = $cntry_code . $bno . $year . $mnth;

            $chk_recent = Bill::where('id', '<', $id)
                ->where('invoice_no', 'LIKE', $general . '%')
                ->where('org_id', $org_id)
                ->orderBy('id', 'DESC')
                ->first();

            $series = '00001';
            if ($chk_recent && $chk_recent->invoice_no) {
                $prev_series = substr($chk_recent->invoice_no, 9, 5);
                $nozeros = (int)ltrim($prev_series, '0') + 1;
                $series = str_pad($nozeros, 5, '0', STR_PAD_LEFT);
            }

            $invoiceno = $general . $series;
            $chkinv = Bill::where('invoice_no', $invoiceno)->where('org_id', $org_id)->first();
            if ($chkinv) {
                $prev_series = substr($chkinv->invoice_no, 9, 5);
                $nozeros = (int)ltrim($prev_series, '0') + 1;
                $series = str_pad($nozeros, 5, '0', STR_PAD_LEFT);
                $invoiceno = $general . $series;
            }
        }

        return $invoiceno;
    }

    private function chkparentbillid(int $id): int
    {
        $bill = Bill::where('id', $id)->where('parent_billid', 0)->first();
        return $bill ? $bill->id : 0;
    }

    private function getinvoiceno(int $id): string
    {
        $bill = Bill::where('id', $id)->first();
        return $bill->invoice_no ?? '0';
    }

    private function generateCreditDebitnote(array $info): bool
    {
        $curdt = Carbon::now()->format('Y-m-d H:i:s');
        $user_currency = $this->getAuthenticatedUser()->currency ?? 'USD'; // Assuming currency is stored in user model
        $bill_id = $info['bill_id'];
        $uid = $info['uid'];
        $invoiceno = $info['invoiceno'];
        $name = $info['name'];
        $billid = $info['billid'];
        $status = $info['status'];
        $org_id = $this->getAuthenticatedUser()->org_id ?? '';

        $getparentbillid = $this->getparentbillid($bill_id);
        if ($getparentbillid > 0) {
            $bill_id = $getparentbillid;
        }

        $getbilled = $this->getbilleddata($bill_id, $uid);
        if (empty($getbilled)) {
            return false;
        }

        $recipient_type = $this->gettbldata(['id' => $bill_id], 'recipient_type', 'bills', 0, 0)['recipient_type'] ?? 'Customer';
        $type = $recipient_type === 'Customer' ? 0 : 1;

        $revenue = $this->gettbldata([
            'type' => $type,
            'bill_id' => $bill_id,
            'invoice_status' => 2,
            'status' => 1,
        ], 'id,order_id,debtor_jfr,exchange_rate,foreign_currency', 'reveneus', 0, 0);

        if (empty($revenue)) {
            return false;
        }

        foreach ($revenue as $res) {
            $amount = 0;
            $order_id = $res['order_id'];
            $parent_revid = $res['id'];
            $foreign_currency = $res['foreign_currency'];
            $exchange_rate = $res['exchange_rate'];
            $recipient_type = $getbilled['recipient_type'];
            $desc = $recipient_type === 'Customer' ? 'Credit note' : 'Debit note';

            $revenueamt = 0;
            $revenue_id = 0;
            $getcharges = $this->gettbldata(['revenue_id' => $res['id']], 'id,charge_code,description,quantity_unit,value,rate_id,revised_amount,currency,local_currency,vat_percentage,cat_id,cat_val', 'charges', 0, 0);

            if (!empty($getcharges)) {
                foreach ($getcharges as $index => $charge) {
                    $parent_chrgid = $charge['id'];
                    $charge_currency = $charge['currency'];
                    if ($charge['revised_amount'] > 0) {
                        if ($index === 0) {
                            $rev_amount = round($amount, 4);
                            if ($user_currency === $charge_currency) {
                                $rev_amount = round($amount);
                            }
                            $check_rev = $this->gettbldata(['parent_id' => $parent_revid], 'id', 'reveneus', 0, 0);
                            $revenue_ar = [
                                'type' => $type,
                                'order_id' => $order_id,
                                'recipient_role' => $recipient_type,
                                'recipient_code' => $getbilled['code'],
                                'recipient_name' => $name,
                                'debtor_jfr' => $res['debtor_jfr'],
                                'invoice_number' => $getbilled['invoice_no'],
                                'credit_note_number' => $invoiceno,
                                'invoice_date' => $getbilled['invoice_date'],
                                'invoice_creation_date' => $curdt,
                                'invoice_receivdon_date' => $curdt,
                                'amount' => $rev_amount,
                                'currency' => $getbilled['currency'],
                                'invoice_status' => 2,
                                'bill_id' => $billid,
                                'parent_id' => $parent_revid,
                                'status' => 1,
                                'user_id' => $uid,
                                'foreign_currency' => $foreign_currency,
                                'exchange_rate' => $exchange_rate,
                                'createdon' => $curdt,
                                'updatedon' => $curdt,
                            ];
                            if (!empty($check_rev)) {
                                $revenue_id = $check_rev['id'];
                                Revenue::where('id', $revenue_id)->update($revenue_ar);
                            } else {
                                $revenue = Revenue::create($revenue_ar);
                                $revenue_id = $revenue->id;
                            }
                        }
                        $revisedamount = $charge['revised_amount'] * -1;
                        $vat_amt = $charge['vat_percentage'] > 0 ? $charge['vat_percentage'] * $revisedamount * 0.01 : 0;
                        $each_chrg_tot = $revisedamount + $vat_amt;

                        $charge_revisedamount = round($revisedamount, 2);
                        $charge_vatamount = round($vat_amt, 4);
                        $charge_eachtotal = round($each_chrg_tot, 4);

                        if ($user_currency === $charge_currency) {
                            $charge_revisedamount = round($revisedamount);
                            $charge_eachtotal = round($each_chrg_tot);
                        }

                        $local_revised_amount = $charge_revisedamount;
                        $local_vat_amount = $charge_vatamount;
                        $local_total_amount = $charge_eachtotal;

                        if (is_numeric($exchange_rate) && $exchange_rate != 0 && strtoupper($charge['currency']) === strtoupper($foreign_currency)) {
                            $local_revised_amount = $exchange_rate * $revisedamount;
                            $local_vat_amount = $charge_vatamount * $exchange_rate;
                            $local_total_amount = $local_revised_amount + $local_vat_amount;
                            $each_chrg_tot = $local_revised_amount + $local_vat_amount;
                        }

                        // if ($org_id === 'VNKN') {
                        //     $local_revised_amount = round($local_revised_amount);
                        //     $local_total_amount = round($local_total_amount);
                        // }

                        $revenueamt += $each_chrg_tot;

                        $charge_ar = [
                            'revenue_id' => $revenue_id,
                            'charge_code' => $charge['charge_code'],
                            'description' => $charge['description'],
                            'quantity_unit' => $charge['quantity_unit'],
                            'value' => $charge['value'],
                            'rate_id' => $charge['rate_id'],
                            'amount' => $charge_revisedamount,
                            'currency' => $charge['currency'],
                            'local_amount' => $local_revised_amount,
                            'local_currency' => $charge['local_currency'],
                            'local_total_amount' => $local_total_amount,
                            'local_vat_amount' => $local_vat_amount,
                            'vat_percentage' => $charge['vat_percentage'],
                            'vat_amount' => $charge_vatamount,
                            'total_amount' => $charge_eachtotal,
                            'cat_id' => $charge['cat_id'],
                            'cat_val' => $charge['cat_val'],
                            'parent_id' => $parent_chrgid,
                            'status' => 1,
                            'user_id' => $uid,
                            'createdon' => $curdt,
                            'updatedon' => $curdt,
                        ];

                        $check_chrg = Charge::where('parent_id', $parent_chrgid)->first();
                        if ($check_chrg) {
                            Charge::where('id', $check_chrg->id)->update($charge_ar);
                        } else {
                            Charge::create($charge_ar);
                        }
                    } else {
                        $check_chrg = $this->gettbldata(['parent_id' => $parent_chrgid, 'status' => 1], 'id,revenue_id', 'charges', 0, 0);
                        if (!empty($check_chrg)) {
                            $chargid = $check_chrg['id'];
                            $revid = $check_chrg['revenue_id'];
                            $check_chrg_count = $this->gettbldata(['revenue_id' => $revid, 'status' => 1], 'id,revenue_id', 'charges', 0, 0);
                            if (!empty($check_chrg_count) && count($check_chrg_count) === 1) {
                                Revenue::where('id', $revid)->update(['status' => 0]);
                                Charge::where('id', $chargid)->update(['status' => 0]);
                            } else {
                                Charge::where('id', $chargid)->update(['status' => 0]);
                            }
                        }
                    }
                }
                if ($revenue_id > 0) {
                    $final_amount = round($revenueamt, 4);
                    // if ($org_id === 'VNKN') {
                    //     $final_amount = round($revenueamt);
                    // }
                    Revenue::where('id', $revenue_id)->update(['amount' => $final_amount]);
                } else {
                    Revenue::where('parent_id', $parent_revid)->update(['amount' => 0, 'status' => 0]);
                }
            }
        }

        return true;
    }

    private function getparentbillid(int $id): int
    {
        $bill = Bill::where('id', $id)->where('parent_billid', '!=', 0)->first();
        return $bill ? $bill->parent_billid : 0;
    }

    private function gettbldata(array $where, string $select, string $table, int $limit, int $start): array
    {
        $modelMap = [
            'bills' => Bill::class,
            'billgroup_master' => BillGroupMaster::class,
            'charges' => Charge::class,
            'reveneus' => Revenue::class,
        ];

        if (!isset($modelMap[$table])) {
            return [];
        }

        $query = $modelMap[$table]::select(DB::raw($select))->where($where);

        if ($table === 'consolidation_rules') {
            $query->orderBy('id', 'DESC');
        } elseif ($table === 'shipment_locations') {
            $query->orderBy('id', 'ASC');
        }

        if ($limit > 0) {
            $query->take($limit)->skip($start);
        } else {
            $query->take(1);
        }

        $result = $query->get()->toArray();
        return $limit === 0 && count($result) === 1 ? $result[0] : $result;
    }

    public function getchargeswithorder(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => []
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $rev_costids = $post['rev_costids'] ?? [];
            $org_id = $user->org_id ?? $post['org_id'] ?? 0;

            if (empty($rev_costids)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Revenue cost IDs are required',
                    'data' => []
                ], 422);
            }

            $result = [];
            if (!empty($rev_costids)) {
                $rev_costids = array_unique((array)$rev_costids);
                $getcharges = $this->getchargeswithorder_ids($rev_costids);

                foreach ($getcharges as $charge) {
                    $local_amount = $charge->local_amount;
                    $final_amount = $charge->amount;
                    $final_currency = $charge->currency;
                    $order_id = $charge->order_id;

                    if ($local_amount > 0) {
                        $final_amount = $local_amount;
                        $final_currency = $charge->local_currency;
                    }

                    if ($charge->amount > 0) {
                        $result[] = [
                            'order_id' => $order_id,
                            'charge_code' => $charge->charge_code,
                            'description' => $charge->description,
                            'amount' => $final_amount,
                            'currency' => $final_currency,
                        ];
                    }

                    if ($charge->vat_percentage > 0 && $charge->vat_amount > 0) {
                        $final_amount = $charge->local_vat_amount > 0 ? $charge->local_vat_amount : $charge->vat_amount;
                        $description = $charge->charge_code . " " . $charge->vat_percentage . " %";
                        $vat_name = $org_id === 'SGKN' ? 'GST' : 'VAT';
                        $result[] = [
                            'order_id' => $order_id,
                            'charge_code' => $vat_name,
                            'description' => $description,
                            'amount' => $final_amount,
                            'currency' => $final_currency,
                        ];
                    }
                }
            }

            Log::info("getchargeswithorder result: " . json_encode($result));

            return response()->json([
                'status' => 'success',
                'message' => 'Charge data retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getchargeswithorder: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving charge data',
                'data' => [],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getchargeswithorder_ids(array $ids): \Illuminate\Database\Eloquent\Collection
    {
        if (empty($ids)) {
            return collect([]);
        }

        return Charge::select([
            'charges.description',
            'charges.amount',
            'charges.revised_amount',
            'charges.currency',
            'charges.local_amount',
            'charges.local_currency',
            'charges.local_vat_amount',
            'charges.local_total_amount',
            'charges.vat_percentage',
            'charges.vat_amount',
            'charges.total_amount',
            'charges.charge_code as charges_chargecode',
            'charge_codes.id as charge_codes_id',
            'charge_codes.charge_code',
            'orders.order_id',
        ])
            ->leftJoin('charge_codes', function ($join) {
                $join->on('charge_codes.id', '=', DB::raw('CAST(charges.charge_code AS INTEGER)'));
            })
            ->leftJoin('reveneus', 'reveneus.id', '=', 'charges.revenue_id')
            ->leftJoin('orders', 'orders.id', '=', 'reveneus.order_id')
            ->whereIn('charges.revenue_id', $ids)
            ->where('charges.status', 1)
            ->get();
    }

    public function getcharges(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => []
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $rev_costid = $post['rev_costid'] ?? null;
            $org_id = $user->org_id ?? $post['org_id'] ?? 0;

            if (empty($rev_costid)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Revenue cost ID is required',
                    'data' => []
                ], 422);
            }

            $result = [];
            if (!empty($rev_costid)) {
                $rev_costids = [(int)$rev_costid];
                $getcharges = $this->getchargeswithorder_ids($rev_costids);

                foreach ($getcharges as $charge) {
                    $local_amount = $charge->local_amount;
                    $final_amount = $charge->amount;
                    $final_currency = $charge->currency;
                    $order_id = $charge->order_id;

                    if ($local_amount > 0) {
                        $final_amount = $local_amount;
                        $final_currency = $charge->local_currency;
                    }

                    if ($charge->amount > 0) {
                        $result[] = [
                            'charge_code' => $charge->charge_code,
                            'description' => $charge->description,
                            'amount' => $final_amount,
                            'currency' => $final_currency,
                            'order_id' => $order_id,
                        ];
                    }

                    if ($charge->vat_percentage > 0 && $charge->vat_amount > 0) {
                        $final_amount = $charge->local_vat_amount > 0 ? $charge->local_vat_amount : $charge->vat_amount;
                        $description = $charge->charge_code . " " . $charge->vat_percentage . " %";
                        $vat_name = $org_id === 'SGKN' ? 'GST' : 'VAT';
                        $result[] = [
                            'charge_code' => $vat_name,
                            'description' => $description,
                            'amount' => $final_amount,
                            'currency' => $final_currency,
                            'order_id' => $order_id,
                        ];
                    }
                }
            }

            Log::info("getcharges result: " . json_encode($result));
            return response()->json([
                'status' => 'success',
                'message' => 'Charge data retrieved successfully',
                'data' => $result
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getcharges: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving charge data',
                'data' => [],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function editbill(Request $request, $id)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => []
            ], 401);
        }

        if (empty($id) || !is_numeric($id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid bill ID',
                'data' => []
            ], 400);
        }

        try {
            $post = $request->all() ?? [];
            $orgId =  $user->org_id ?? $post['user_id'] ?? 0;
            $data = [
                'org_id' => $orgId,
                'be_value' => $user->be_value ?? $post['be_value'] ?? 0,
                'bstatus' => [],
                'bgroups' => [],
                'tobebilled' => [],
                'billed' => [],
                'ref_names_arr' => $this->getrefnums(),
            ];

            $userid = $user->user_id ?? $post['user_id'] ?? 0;
            $custid = $user->cust_id ?? $post['cust_id'] ?? 0;
            $country_userids = $user->country_user_ids ?? $post['country_user_ids'] ?? [];
            $billtype = 1;
            $status = 1;

            $data['bstatus'] = BillStatusMaster::where('status', 1)
                ->select('id', 'name')
                ->get()
                ->map(fn($status) => ['id' => $status->id, 'name' => $status->name])
                ->toArray();

            $data['bgroups'] = BillGroupMaster::where('status', 1)
                ->whereIn('name', ['Invoice', 'Bill'])
                ->select('id', 'name')
                ->get()
                ->map(fn($group) => ['id' => $group->id, 'name' => $group->name])
                ->toArray();

            $getbilled = $this->getbilleddata($id, $userid);
            $code = $getbilled['code'] ?? '';
            if (!empty($getbilled)) {
                $status = $getbilled['status'];
                $billtype = $getbilled['bill_type'];
                $data['billed'] = [
                    'billid' => $getbilled['id'],
                    'revenue_id' => $getbilled['revenue_id'],
                    'tracking_no' => $getbilled['tracking_no'],
                    'bill_party' => $getbilled['bill_party'],
                    'bill_type' => $getbilled['bill_type'],
                    'bill_group' => $getbilled['bill_group'],
                    'recipient_type' => $getbilled['recipient_type'],
                    'invoice_no' => $getbilled['invoice_no'],
                    'invoice_date' => $getbilled['invoice_date'],
                    'invoice_status' => $getbilled['invoice_status'],
                    'acon_code' => $getbilled['acon_code'],
                    'customer_code' => $getbilled['customer_code'],
                    'code' => $code,
                    'org_id' => $getbilled['org_id'],
                    'be_value' => $getbilled['be_value'],
                    'status' => $status,
                    'invoice_description' => $getbilled['invoice_description'],
                ];
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Bill not found',
                    'data' => $data
                ], 404);
            }

            // Fetch to-be-billed and billed data
            $whr = ['status' => $status, 'bill_id' => $id, 'code' => $code];
            $gettobebilled = $this->gettobebilledandbilled($userid, $custid, $country_userids, $whr, $orgId);

            foreach ($gettobebilled as $res) {
                $tripsts = '';
                $cross_leg_id = 0;
                $trip_id = $res['trip_id'] ?? 0;
                $shift_id = $res['shift_id'] ?? 0;

                if ($trip_id == 0) {
                    $getshift = $this->gettbldata(['shift_leg_id' => $shift_id], 'id', 'shipment', 0, 0);
                    if (!empty($getshift)) {
                        $leg_ids = array_column($getshift, 'id');
                        if (!empty($leg_ids)) {
                            $cross_leg_id = 1;
                            $gettripsids = $this->checkdriveracceptsfornewtrips($leg_ids);
                            $acceptence = 1;
                            $new_trip_id = 0;
                            foreach ($gettripsids as $trps) {
                                $trip_row_id = $trps['trip_id'];
                                if ($trip_row_id > 0) {
                                    $new_trip_id = $trip_row_id;
                                } else {
                                    $acceptence = 0;
                                }
                            }
                            $tripsts = $acceptence == 0
                                ? "<input type='hidden' name='noorder_trip_sts' id='{$res['revcosid']}' class='noorder_trip_sts' value='{$new_trip_id}'>"
                                : "<input type='hidden' name='order_trip_sts' id='{$res['revcosid']}' class='order_trip_sts' value='{$new_trip_id}'>";
                        }
                    }
                }

                $item = [
                    'revcosid' => $res['revcosid'],
                    'recipient_role' => $res['recipient_role'],
                    'billtype' => $billtype,
                    'order_id' => $res['order_id'],
                    'shipper' => $res['shipper'],
                    'pickup_city' => $res['pickup_city'],
                    'pickup_pincode' => $res['pickup_pincode'],
                    'pickup_country' => $res['pickup_country'],
                    'consignee' => $res['consignee'],
                    'delivery_city' => $res['delivery_city'],
                    'delivery_pincode' => $res['delivery_pincode'],
                    'delivery_country' => $res['delivery_country'],
                    'transport_mode' => $res['transport_mode'],
                    'amount' => $res['amount'],
                    'currency' => $res['currency'],
                    'bill_id' => $res['bill_id'],
                    'trip_id' => $trip_id,
                    'createdon' => date('Y-m-d', strtotime($res['createdon'])),
                    'cross_leg_id' => $cross_leg_id,
                    'tripsts' => $tripsts,
                ];

                if ($res['bill_id'] == $id || ($res['bill_id'] == 0 && $res['invoice_status'] == 1)) {
                    $data['tobebilled'][] = $item;
                }
            }

            // Log data for debugging
            Log::info("editbill data for bill ID {$id}: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'Bill data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in editbill for bill ID {$id}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving bill data',
                'data' => [],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getbillmasters(string $table): array
    {
        $modelMap = [
            'billstatus_master' => BillStatusMaster::class,
            'billgroup_master' => BillGroupMaster::class,
        ];

        if (!isset($modelMap[$table])) {
            return [];
        }

        return $modelMap[$table]::where('status', 1)
            ->select('id', 'name')
            ->get()
            ->toArray();
    }

    private function getbilleddata(int $id, int $userid): array
    {
        $org_id = $this->getAuthenticatedUser()->org_id ?? '';
        $query = Bill::where('id', $id)->where('status', '!=', 0);
        if ($org_id === 'RUKN') {
            $query->where('org_id', $org_id);
        } else {
            $query->where('user_id', $userid);
        }
        $bill = $query->select([
            'id',
            'revenue_id',
            'tracking_no',
            'bill_party',
            'bill_type',
            'bill_group',
            'recipient_type',
            'invoice_no',
            'invoice_date',
            'invoice_status',
            'acon_code',
            'total_amount',
            'currency',
            'customer_code',
            'code',
            'org_id',
            'be_value',
            'parent_billid',
            'status',
            'invoice_description',
        ])->first();

        return $bill ? $bill->toArray() : [];
    }

    private function gettobebilledandbilled(int $userid, ?int $custid, array $countryuids, array $whr, ?string $org_id): array
    {
        $billid = $whr['bill_id'];
        $revstatus = $whr['status'];
        $code = $whr['code'];
        $dates = [];
        $pickup = [];
        $delivery = [];
        $bookingid = $whr['bookingid'] ?? [];
        $ord_ref = isset($whr['order_reftype']) && $whr['order_reftype'] && $whr['ref_val'] ? ['reference_id' => $whr['order_reftype'], 'ref_value' => $whr['ref_val']] : [];
        $statuscodes = [];

        if (!empty($whr['earlypickupfrom'])) {
            $dates['DATE_FORMAT(orders.pickup_datetime, "%Y-%m-%d") >='] = date('Y-m-d', strtotime($whr['earlypickupfrom']));
        }
        if (!empty($whr['earlypickupto'])) {
            $dates['DATE_FORMAT(orders.pickup_datetime, "%Y-%m-%d") <='] = date('Y-m-d', strtotime($whr['earlypickupto']));
        }
        if (!empty($whr['earlydeliveryfrom'])) {
            $dates['DATE_FORMAT(orders.delivery_datetime, "%Y-%m-%d") >='] = date('Y-m-d', strtotime($whr['earlydeliveryfrom']));
        }
        if (!empty($whr['earlydeliveryto'])) {
            $dates['DATE_FORMAT(orders.delivery_datetime, "%Y-%m-%d") <='] = date('Y-m-d', strtotime($whr['earlydeliveryto']));
        }
        if (!empty($whr['pickupfrom'])) {
            $dates['DATE_FORMAT(stop_status.createdon, "%Y-%m-%d") >='] = date('Y-m-d', strtotime($whr['pickupfrom']));
            $pickup = ['stop_type' => 'P', 'status_code' => '0500'];
        }
        if (!empty($whr['pickupto'])) {
            $dates['DATE_FORMAT(stop_status.createdon, "%Y-%m-%d") <='] = date('Y-m-d', strtotime($whr['pickupto']));
            $pickup = ['stop_type' => 'P', 'status_code' => '0500'];
        }
        if (!empty($whr['deliveryfrom'])) {
            $dates['DATE_FORMAT(stop_status.createdon, "%Y-%m-%d") >='] = date('Y-m-d', strtotime($whr['deliveryfrom']));
            $delivery = ['stop_type' => 'D', 'status_code' => '2300'];
        }
        if (!empty($whr['deliveryto'])) {
            $dates['DATE_FORMAT(stop_status.createdon, "%Y-%m-%d") <='] = date('Y-m-d', strtotime($whr['deliveryto']));
            $delivery = ['stop_type' => 'D', 'status_code' => '2300'];
        }
        if (!empty($whr['order_status'])) {
            $statuscodes = $whr['order_status'] === 'pickup'
                ? ['stop_type' => 'P', 'status_code' => '0500']
                : ['stop_type' => 'D', 'status_code' => '2300'];
        }

        $query = Revenue::select([
            'reveneus.id as revcosid',
            'reveneus.recipient_role',
            'reveneus.recipient_code',
            'reveneus.recipient_name',
            'reveneus.debtor_jfr',
            'reveneus.amount',
            'reveneus.currency',
            'reveneus.invoice_status',
            'reveneus.bill_id',
            'orders.order_id',
            'orders.pickup_company as shipper',
            'orders.pickup_city',
            'orders.delivery_city',
            'orders.delivery_company as consignee',
            'orders.pickup_country',
            'orders.delivery_country',
            'orders.pickup_pincode',
            'orders.delivery_pincode',
            'orders.transport_mode',
            'orders.shift_id',
            'orders.trip_id',
            'orders.created_at',
        ])
            ->join('orders', 'reveneus.order_id', '=', 'orders.id')
            ->join('order_details', 'orders.id', '=', 'order_details.order_row_id')
            ->join('order_references', 'orders.id', '=', 'order_references.order_id')
            ->join('shipment', 'orders.shift_id', '=', 'shipment.id')
            ->join('stop_status', 'shipment.id', '=', 'stop_status.shipment_id')
            ->where('orders.status', '!=', 0)
            ->where('reveneus.recipient_code', $code)
            ->where('reveneus.status', 1);

        if (!empty($bookingid)) {
            $query->whereIn('orders.order_id', $bookingid);
        }
        if (!empty($ord_ref)) {
            $query->where('order_references.reference_id', $ord_ref['reference_id'])
                ->where('order_references.ref_value', 'like', "%{$ord_ref['ref_value']}%");
        }
        if ($revstatus == 1) {
            $query->whereIn('reveneus.bill_id', [$billid, 0])
                ->whereIn('reveneus.invoice_status', [1, 2]);
        } else {
            $query->where('reveneus.bill_id', $billid)
                ->where('reveneus.invoice_status', 2);
        }
        if ($org_id === 'RUKN') {
            $query->where('orders.org_id', $org_id);
        } else {
            $query->where('orders.user_id', $userid);
        }
        if (!empty($whr['doc_status'])) {
            $query->where('order_details.docs_received_datetime', $whr['doc_status'] === 'yes' ? '!=' : '=', null);
        }
        foreach ($dates as $key => $value) {
            $query->whereRaw($key, $value);
        }
        if (!empty($statuscodes)) {
            $query->where($statuscodes);
        } elseif (!empty($pickup) || !empty($delivery)) {
            $query->where(function ($q) use ($pickup, $delivery) {
                if (!empty($pickup)) {
                    $q->where($pickup);
                }
                if (!empty($delivery)) {
                    $q->orWhere($delivery);
                }
            });
        }

        // return $query->groupBy('reveneus.id')
        return $query->orderBy('reveneus.invoice_status', 'DESC')->get()->toArray();
    }

    private function checkdriveracceptsfornewtrips(array $legids): array
    {
        if (empty($legids)) {
            return [];
        }

        return ShiporderStopSequence::whereIn('shift_id', $legids)
            ->where('status', 1)
            ->select('trip_id')
            ->get()
            ->toArray();
    }

    public function updatebill(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $data = ['status' => 0];
            $curdt = Carbon::now()->format('Y-m-d H:i:s');
            $uid = $user->user_id ?? $post['user_id'] ?? 0;
            $bill_type = $post['bill_type'] ?? 0;
            $bill_group = $post['bill_group'] ?? 0;
            $customercode = $post['customercode'] ?? '';
            $org_id = $user->org_id ?? $post['org_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;
            $partner_cid = $post['partner_cid'] ?? '';
            $debitor_code = $post['partner_debitor_code'] ?? '';
            $invoice_no = $post['invoice_no'] ?? 1;
            $invoice_date = !empty($post['invoice_date']) && strtotime($post['invoice_date'])
                ? date('Y-m-d', strtotime($post['invoice_date']))
                : '';
            $bill_status = $post['bill_status'] ?? 0;
            $tracking_no = !empty($post['tracking_no']) ? (array)$post['tracking_no'] : [];
            $recipienttype = $post['recipienttype'] ?? '';
            $currency = $post['currency'] ?? '';
            $countrynocode = $user->usr_tzone['phone_code'] ?? '';
            $name = $post['name'] ?? '';
            $billid = $post['billid'] ?? 0;
            $status = $post['status'] ?? 1;
            $invoice_description = $post['invoice_description'] ?? '';

            if ($uid <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if ($billid <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Bill ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if ($bill_type <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Bill type is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if ($bill_group <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Bill group is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if (empty($invoice_no) || $invoice_no == 1) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Valid invoice number is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if (empty($tracking_no)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Tracking number(s) are required',
                    'data' => ['status' => 0]
                ], 422);
            }

            if ($billid > 0 && $invoice_no == 1) {
                $checkbill = $this->getTableRowDataSubUsers(['id' => $billid], 'invoice_no', 'bills', 0, 0);
                if (!empty($checkbill)) {
                    $invoice_no = $checkbill['invoice_no'];
                }
            }

            $groupname = $bill_group > 0 ? $this->getGroupName($bill_group) : '';
            $invoice_statusname = BillStatusMaster::where('id', $bill_status)->value('name') ?? '';

            if ($status > 1 && $invoice_statusname === 'Open') {
                $bill_status = BillStatusMaster::where('name', 'Billed')->value('id') ?? $bill_status;
            }

            $insdata = [
                'revenue_id' => 0,
                'tracking_no' => 0,
                'bill_party' => $name,
                'bill_type' => $bill_type,
                'bill_group' => $bill_group,
                'recipient_type' => $groupname,
                'invoice_date' => $invoice_date,
                'invoice_status' => $bill_status,
                'acon_code' => $debitor_code,
                'total_amount' => 0,
                'currency' => $currency,
                'customer_code' => $customercode,
                'code' => $partner_cid,
                'org_id' => $org_id,
                'be_value' => $be_value,
                'user_id' => $uid,
                'status' => $status,
                'updated_at' => $curdt,
                'invoice_description' => $invoice_description,
            ];

            $update = Bill::where('id', $billid)->update($insdata);
            if ($update) {
                Revenue::where('bill_id', $billid)->update([
                    'invoice_number' => '',
                    'invoice_date' => null,
                    'invoice_creation_date' => null,
                    'invoice_status' => 1,
                    'bill_id' => 0,
                ]);

                if ($bill_type == '1' && !empty($tracking_no)) {
                    $tracking_id = explode('_', $tracking_no[0])[0]; // Take first tracking number for bill_type 1
                    Revenue::where('id', $tracking_id)->update([
                        'invoice_number' => $invoice_no,
                        'invoice_date' => $invoice_date,
                        'invoice_creation_date' => $curdt,
                        'invoice_status' => 2,
                        'bill_id' => $billid,
                    ]);
                } elseif ($bill_type == '2' && !empty($tracking_no)) {
                    $ntrackingno = array_map(fn($ex) => explode('_', $ex)[0], $tracking_no);
                    Revenue::whereIn('id', $ntrackingno)->update([
                        'invoice_number' => $invoice_no,
                        'invoice_date' => $invoice_date,
                        'invoice_creation_date' => $curdt,
                        'invoice_status' => 2,
                        'bill_id' => $billid,
                    ]);
                }

                $data['status'] = 1;
                Log::info("updatebill successful for bill ID {$billid}: " . json_encode($insdata));

                return response()->json([
                    'status' => 'success',
                    'message' => 'Bill updated successfully',
                    'data' => $data
                ], 200);
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to update bill',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("Error in updatebill for bill ID {$billid}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while updating bill data',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    private function getTableRowDataSubUsers(array $where, string $select, string $table, int $limit, int $start): array
    {
        $modelMap = [
            'bills' => Bill::class,
            'reveneus' => Revenue::class,
            'charges' => Charge::class,
        ];

        if (!isset($modelMap[$table])) {
            return [];
        }

        $query = $modelMap[$table]::selectRaw($select)->where($where)->limit(1);
        $result = $query->first();
        return $result ? $result->toArray() : [];
    }

    public function viewbill(Request $request, $id)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => []
            ], 401);
        }

        if (empty($id) || !is_numeric($id)) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid bill ID',
                'data' => []
            ], 400);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $data = [
                'org_id' =>  $orgId,
                'be_value' => $user->be_value ?? '',
                'bstatus' => [],
                'bgroups' => [],
                'tobebilled' => [],
                'billed' => [],
            ];

            $userid = $user->user_id ?? $post['user_id'] ?? 0;
            $custid = $user->cust_id ?? $post['cust_id'] ?? 0;
            $country_userids = $user->country_user_ids ?? [];
            $billtype = 1;
            $status = 1;
            $parent_billid = 0;

            $data['bstatus'] = BillStatusMaster::where('status', 1)
                ->select('id', 'name')
                ->get()
                ->map(fn($status) => ['id' => $status->id, 'name' => $status->name])
                ->toArray();

            $data['bgroups'] = BillGroupMaster::where('status', 1)
                ->select('id', 'name')
                ->get()
                ->map(fn($group) => ['id' => $group->id, 'name' => $group->name])
                ->toArray();

            $getbilled = $this->getbilleddata($id, $userid);
            $code = $getbilled['code'] ?? '';
            if (!empty($getbilled)) {
                $status = $getbilled['status'];
                $billtype = $getbilled['bill_type'];
                $parent_billid = $getbilled['parent_billid'] ?: $id;
                $data['billed'] = [
                    'billid' => $getbilled['id'],
                    'revenue_id' => $getbilled['revenue_id'],
                    'tracking_no' => $getbilled['tracking_no'],
                    'bill_party' => $getbilled['bill_party'],
                    'bill_type' => $getbilled['bill_type'],
                    'bill_group' => $getbilled['bill_group'],
                    'recipient_type' => $getbilled['recipient_type'],
                    'invoice_no' => $getbilled['invoice_no'],
                    'invoice_date' => $getbilled['invoice_date'],
                    'invoice_status' => $getbilled['invoice_status'],
                    'acon_code' => $getbilled['acon_code'],
                    'customer_code' => $getbilled['customer_code'],
                    'code' => $code,
                    'org_id' => $getbilled['org_id'],
                    'be_value' => $getbilled['be_value'],
                    'status' => $status,
                    'invoice_description' => $getbilled['invoice_description'],
                ];
            } else {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Bill not found',
                    'data' => $data
                ], 404);
            }

            $whr = ['status' => $status, 'bill_id' => $parent_billid, 'code' => $code];
            $gettobebilled = $this->gettobebilledandbilled($userid, $custid, $country_userids, $whr, $orgId);

            foreach ($gettobebilled as $res) {
                if ($res['bill_id'] == $parent_billid) {
                    $data['tobebilled'][] = [
                        'revcosid' => $res['revcosid'],
                        'recipient_role' => $res['recipient_role'],
                        'billtype' => $billtype,
                        'order_id' => $res['order_id'],
                        'shipper' => $res['shipper'],
                        'pickup_city' => $res['pickup_city'],
                        'pickup_pincode' => $res['pickup_pincode'],
                        'pickup_country' => $res['pickup_country'],
                        'consignee' => $res['consignee'],
                        'delivery_city' => $res['delivery_city'],
                        'delivery_pincode' => $res['delivery_pincode'],
                        'delivery_country' => $res['delivery_country'],
                        'transport_mode' => $res['transport_mode'],
                        'amount' => $res['amount'],
                        'currency' => $res['currency'],
                        'bill_id' => $res['bill_id'],
                        'createdon' => date('Y-m-d', strtotime($res['createdon'])),
                    ];
                }
            }

            Log::info("viewbill data for bill ID {$id}: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'Bill data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in viewbill for bill ID {$id}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving bill data',
                'data' => [],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function deletebill(Request $request, $id)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        if (empty($id) || !is_numeric($id) || $id <= 0) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid bill ID',
                'data' => ['status' => 0]
            ], 400);
        }

        try {
            $chk = Bill::where(['id' => $id, 'parent_billid' => 0])->select('id')->first();
            $chkparent = Bill::where('id', $id)->where('parent_billid', '>', 0)->select('id', 'parent_billid')->first();

            if ($chk) {
                $upd = Bill::where('id', $id)->update(['status' => 0]);
                $upd_revenue = Revenue::where('bill_id', $id)->update([
                    'invoice_number' => 0,
                    'invoice_date' => null,
                    'invoice_creation_date' => null,
                    'invoice_receivdon_date' => null,
                    'invoice_status' => 1,
                    'bill_id' => 0,
                ]);

                if ($upd) {
                    Log::info("deletebill successful for parent bill ID {$id}");
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Parent bill deleted successfully',
                        'data' => ['status' => 1]
                    ], 200);
                } else {
                    Log::error("deletebill failed to update parent bill ID {$id}");
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Failed to delete parent bill',
                        'data' => ['status' => 0]
                    ], 500);
                }
            } elseif ($chkparent) {
                $parent_billid = $chkparent->parent_billid;
                $upd_parent = Bill::where('id', $parent_billid)->update(['status' => 2]);
                $upd = Bill::where('id', $id)->update(['parent_billid' => 0, 'status' => 0]);
                $upd_revenue = Revenue::where('bill_id', $id)->update(['status' => 0]);

                if ($upd) {
                    Log::info("deletebill successful for child bill ID {$id} with parent bill ID {$parent_billid}");
                    return response()->json([
                        'status' => 'success',
                        'message' => 'Child bill deleted successfully',
                        'data' => ['status' => 1]
                    ], 200);
                } else {
                    Log::error("deletebill failed to update child bill ID {$id} with parent bill ID {$parent_billid}");
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Failed to delete child bill',
                        'data' => ['status' => 0]
                    ], 500);
                }
            }

            Log::warning("deletebill: Bill ID {$id} not found");
            return response()->json([
                'status' => 'error',
                'message' => 'Bill not found',
                'data' => ['status' => 0]
            ], 404);
        } catch (\Exception $e) {
            Log::error("Error in deletebill for bill ID {$id}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while deleting bill data',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }
}
