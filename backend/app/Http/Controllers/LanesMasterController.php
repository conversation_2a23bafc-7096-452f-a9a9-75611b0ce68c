<?php

namespace App\Http\Controllers;

use App\Models\LanesMaster;
use App\Models\GeoMaster;
use App\Models\CountryMaster;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Lang;
use Illuminate\Support\Facades\Log;

class LanesMasterController extends Controller
{
    public function __construct()
    {
        // $this->middleware('auth:api');
        
    }
    /**
     * Display a listing of the lanes.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Comment out authentication check for direct API access
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }
            
            // Use default values if user is not authenticated
            $user_id = $user ? $user->id : 1; // Default user ID
            $org_id = $user ? ($user->org_id ?? null) : 1; // Default org ID

            $where1 = [];
            $whr = ['tl.status' => true, 'tl.user_id' => $user_id];

            // FIX: Use correct method to get geo_master and country_master lists
            $geo_master = GeoMaster::select(['id', 'name'])->where('status', 1)->orderBy('name', 'ASC')->get()->toArray();
            $country_master = CountryMaster::select('country_name')->where('status', 1)->orderBy('country_name', 'ASC')->get()->pluck('country_name')->toArray();

            if ($request->isMethod('post') && $request->input('searchsubmit') === 'Search') {
                $where = $request->only(['name1', 'lane_id1', 'fromdate', 'todate']);

                if (!empty($where['fromdate'])) {
                    $whr["tl.createdon >= "] = date('Y-m-d', strtotime($where['fromdate']));
                }
                if (!empty($where['todate'])) {
                    $whr["tl.createdon <= "] = date('Y-m-d', strtotime($where['todate']));
                }
                if (!empty($where['name1'])) {
                    $where1['tl.lane_name'] = $where['name1'];
                }
                if (!empty($where['lane_id1'])) {
                    $where1['tl.lane_id'] = $where['lane_id1'];
                }
            } elseif ($request->isMethod('post') && $request->input('searchsubmita') === 'Search') {
                $where = $request->only(['lane_id', 'lane_name1', 'source_geo', 'source', 'destination_geo', 'destination', 'statusa']);

                if (!empty($where['lane_id'])) {
                    $where1['tl.lane_id'] = $where['lane_id'];
                }
                if (!empty($where['lane_name1'])) {
                    $where1['tl.lane_name'] = $where['lane_name1'];
                }
                if (!empty($where['source_geo'])) {
                    $whr['tl.source_geo'] = $where['source_geo'];
                }
                if (!empty($where['source'])) {
                    $where1['tl.source'] = $where['source'];
                }
                if (!empty($where['destination_geo'])) {
                    $whr['tl.destination_geo'] = $where['destination_geo'];
                }
                if (!empty($where['destination'])) {
                    $where1['tl.destination'] = $where['destination'];
                }
                if (!empty($where['statusa'])) {
                    $where1['tl.status'] = $where['statusa'];
                }
            }

            $lanes_list = LanesMaster::getSearchData($where1, $whr)->toArray();

            $datatable_headers = [
                Lang::get('lane_id'),
                Lang::get('lane_name'),
                Lang::get('source_geo'),
                Lang::get('source_country'),
                Lang::get('general_source'),
                Lang::get('destination_geo'),
                Lang::get('destination_country'),
                Lang::get('general_destination'),
                Lang::get('leftmenu_status'),
                Lang::get('general_org_id')
            ];

            $data = [
                'page_title' => Lang::get('bpartner_lanes'),
                'sub_title' => Lang::get('general_search'),
                'lanes_list' => $lanes_list,
                'geo_master' => $geo_master,
                'country_master' => $country_master,
                'user_id' => $user_id,
                'postData' => $request->all(),
                'datatable_headers' => $datatable_headers,
                'column_visibility' => array_fill(0, count($datatable_headers), true),
                'datatable_header_sequence' => [],
                'datatable_header_toggle' => [],
                'datatable_header_sequence_index' => array_merge([0, 1], range(2, count($datatable_headers) + 1)),
            ];

            $settings = DB::table('datatable_settings')
                ->where('controller_name', class_basename(self::class))
                ->where('method_name', 'index')
                ->where('org_id', $org_id)
                ->first();

            if ($settings) {
                if ($settings->sequence_data) {
                    $data['datatable_header_sequence'] = $datatable_header_sequence = unserialize($settings->sequence_data);
                    $datatable_header_sequence_index = [0, 1];

                    foreach ($datatable_header_sequence as $value) {
                        $index = array_search($value, $datatable_headers);
                        if ($index !== false) {
                            $datatable_header_sequence_index[] = $index + 2;
                        }
                    }

                    $datatable_header_sequence_index = array_filter($datatable_header_sequence_index);
                    $datatable_header_sequence_index = array_values($datatable_header_sequence_index);

                    foreach ($datatable_headers as $key => $header) {
                        $index = $key + 2;
                        if (!in_array($index, $datatable_header_sequence_index)) {
                            $datatable_header_sequence_index[] = $index;
                        }
                    }

                    $data['datatable_header_sequence_index'] = $datatable_header_sequence_index;
                }

                if ($settings->toggle_data) {
                    $data['datatable_header_toggle'] = unserialize($settings->toggle_data);
                    $data['column_visibility'] = array_map(function ($item) use ($data) {
                        return in_array($item, $data['datatable_header_toggle']);
                    }, $datatable_headers);
                }
            }

            return response()->json([
                'status' => true,
                'message' => 'Lanes master data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve lanes master data: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve lanes master data: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function add(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Comment out authentication check for direct API access
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }
            
            // Use default values if user is not authenticated
            $user_id = $user ? $user->id : 1; // Default user ID
            $org_id = $user ? ($user->org_id ?? null) : 1; // Default org ID

            // FIX: Use correct method to get geo_master and country_master lists
            $geo_master = GeoMaster::select(['id', 'name'])->where('status', 1)->orderBy('name', 'ASC')->get()->toArray();
            $country_master = CountryMaster::select('country_name')->where('status', 1)->orderBy('country_name', 'ASC')->get()->pluck('country_name')->toArray();

            $data = [
                'page_title' => Lang::get('bpartner_lanes'),
                'sub_title' => Lang::get('general_add'),
                'org_id' => $org_id,
                'user_id' => $user_id,
                'geo_master' => $geo_master,
                'country_master' => $country_master,
                // Include all LanesMaster fields for the add form
                'fields' => [
                    'lane_id' => '',
                    'lane_name' => '',
                    'source_geo' => '',
                    'source' => '',
                    'destination_geo' => '',
                    'destination' => '',
                    'org_id' => $org_id,
                    'user_id' => $user_id,
                    'status' => true,
                    'source_country' => '',
                    'destination_country' => '',
                    'service_id' => '',
                    'be_value' => '',
                    'createdon' => now(),
                    'updatedon' => now(),
                ],
            ];

            return response()->json([
                'status' => true,
                'message' => 'Add lanes master data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve add lanes master data: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve add lanes master data: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function update(Request $request, int $id): JsonResponse
    {
        try {
            if (!$id) {
                return response()->json([
                    'status' => false,
                    'message' => 'ID is required',
                    'data' => []
                ], 400);
            }

            $user = Auth::user();
            
            // Comment out authentication check for direct API access
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }
            
            // Use default values if user is not authenticated
            $user_id = $user ? $user->id : 1; // Default user ID
            $user_org_id = $user ? ($user->org_id ?? null) : 1; // Default org ID

            // Get all input fields with proper type casting and defaults
            $lane_name = $request->input('lane_name', '');
            $source_geo = (int) $request->input('source_geo', 0);
            $source_country = $request->input('source_country', '');
            $source = $request->input('source', '');
            $request_org_id = (int) $request->input('org_id', $user_org_id); // Use request org_id or fallback to user org_id
            $destination_geo = (int) $request->input('destination_geo', 0);
            $destination_country = $request->input('destination_country', '');
            $destination = $request->input('destination', '');
            $status = (int) $request->input('status', 1);
            $lane_id = $request->input('lane_id', '');
            $service_id = $request->input('service_id', '') ? (int) $request->input('service_id') : null;
            $be_value = $request->input('be_value', '');

            // Validate required fields
            if (empty($lane_name)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Lane name is required',
                    'data' => []
                ], 400);
            }

            // Handle source and destination logic
            if ($source_geo == 2) {
                $source = $source_country;
            }
            if ($destination_geo == 2) {
                $destination = $destination_country;
            }

            // Check if lane exists
            $lane = LanesMaster::find($id);
            if (!$lane) {
                return response()->json([
                    'status' => false,
                    'message' => 'Lane not found',
                    'data' => []
                ], 404);
            }

            // Check for duplicate lanes (excluding current lane)
            $where = [
                'source_geo' => $source_geo,
                'source_country' => $source_country,
                'source' => $source,
                'destination_geo' => $destination_geo,
                'destination_country' => $destination_country,
                'destination' => $destination,
                'user_id' => $user_id,
                'status' => 1
            ];

            $check_lane = LanesMaster::where($where)
                ->where('id', '!=', $id)
                ->first();

            if ($check_lane) {
                return response()->json([
                    'status' => false,
                    'message' => 'Lane already exists',
                    'data' => ['result' => 2]
                ], 400);
            }

            // Prepare update data with proper type casting
            $update_data = [
                'lane_name' => $lane_name,
                'source_geo' => $source_geo,
                'source_country' => strtoupper($source_country),
                'source' => $source,
                'org_id' => $request_org_id,
                'destination_geo' => $destination_geo,
                'destination_country' => $destination_country,
                'destination' => $destination,
                'user_id' => $user_id,
                'status' => $status,
                'service_id' => $service_id,
                'be_value' => $be_value,
                'updatedon' => now(),
            ];

            // Remove null values to avoid database issues
            $update_data = array_filter($update_data, function($value) {
                return $value !== null && $value !== '';
            });

            $lane->update($update_data);

            return response()->json([
                'status' => true,
                'message' => 'Lane updated successfully',
                'data' => $lane->fresh()
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to update lane: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to update lane: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function edit(int $id): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Comment out authentication check for direct API access
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }
            
            // Use default values if user is not authenticated
            $user_id = $user ? $user->id : 1; // Default user ID
            $org_id = $user ? ($user->org_id ?? null) : 1; // Default org ID

            // FIX: Use correct method to get geo_master and country_master lists
            $geo_master = GeoMaster::select(['id', 'name'])->where('status', 1)->orderBy('name', 'ASC')->get()->toArray();
            $country_master = CountryMaster::select('country_name')->where('status', 1)->orderBy('country_name', 'ASC')->get()->pluck('country_name')->toArray();
            
            // Get lane data directly
            $lane = LanesMaster::find($id);
            
            if (!$lane) {
                return response()->json([
                    'status' => false,
                    'message' => 'Lane not found',
                    'data' => []
                ], 404);
            }

            $lane_data = $lane->toArray();

            $data = [
                'page_title' => Lang::get('bpartner_lanes'),
                'sub_title' => Lang::get('general_edit'),
                'org_id' => $org_id,
                'user_id' => $user_id,
                'geo_master' => $geo_master,
                'country_master' => $country_master,
                // Include all LanesMaster fields for the edit form
                'fields' => [
                    'lane_id' => $lane_data['lane_id'] ?? '',
                    'lane_name' => $lane_data['lane_name'] ?? '',
                    'source_geo' => $lane_data['source_geo'] ?? '',
                    'source' => $lane_data['source'] ?? '',
                    'destination_geo' => $lane_data['destination_geo'] ?? '',
                    'destination' => $lane_data['destination'] ?? '',
                    'org_id' => $lane_data['org_id'] ?? $org_id,
                    'user_id' => $lane_data['user_id'] ?? $user_id,
                    'status' => $lane_data['status'] ?? 1,
                    'source_country' => $lane_data['source_country'] ?? '',
                    'destination_country' => $lane_data['destination_country'] ?? '',
                    'service_id' => $lane_data['service_id'] ?? '',
                    'be_value' => $lane_data['be_value'] ?? '',
                    'createdon' => $lane_data['createdon'] ?? now(),
                    'updatedon' => $lane_data['updatedon'] ?? now(),
                ],
            ];

            return response()->json([
                'status' => true,
                'message' => 'Lane edit data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve lane edit data: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve lane edit data: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function view(int $id): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Comment out authentication check for direct API access
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }
            
            // Use default values if user is not authenticated
            $user_id = $user ? $user->id : 1; // Default user ID
            $org_id = $user ? ($user->org_id ?? null) : 1; // Default org ID

            // Get lane data directly
            $lane = LanesMaster::find($id);
            
            if (!$lane) {
                return response()->json([
                    'status' => false,
                    'message' => 'Lane not found',
                    'data' => []
                ], 404);
            }

            $lane_data = $lane->toArray();

            $data = [
                'page_title' => Lang::get('bpartner_lanes'),
                'sub_title' => Lang::get('general_view'),
                'org_id' => $org_id,
                'user_id' => $user_id,
                'lane_data' => $lane_data,
                // Include all LanesMaster fields for the view
                'fields' => [
                    'lane_id' => $lane_data['lane_id'] ?? '',
                    'lane_name' => $lane_data['lane_name'] ?? '',
                    'source_geo' => $lane_data['source_geo'] ?? '',
                    'source' => $lane_data['source'] ?? '',
                    'destination_geo' => $lane_data['destination_geo'] ?? '',
                    'destination' => $lane_data['destination'] ?? '',
                    'org_id' => $lane_data['org_id'] ?? $org_id,
                    'user_id' => $lane_data['user_id'] ?? $user_id,
                    'status' => $lane_data['status'] ?? 1,
                    'source_country' => $lane_data['source_country'] ?? '',
                    'destination_country' => $lane_data['destination_country'] ?? '',
                    'service_id' => $lane_data['service_id'] ?? '',
                    'be_value' => $lane_data['be_value'] ?? '',
                    'createdon' => $lane_data['createdon'] ?? now(),
                    'updatedon' => $lane_data['updatedon'] ?? now(),
                ],
            ];

            return response()->json([
                'status' => true,
                'message' => 'Lane view data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve lane view data: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve lane view data: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function delete(int $id): JsonResponse
    {
        try {
            if (!$id) {
                return response()->json([
                    'status' => false,
                    'message' => 'ID is required',
                    'data' => []
                ], 400);
            }

            $lane = LanesMaster::find($id);
            if (!$lane) {
                return response()->json([
                    'status' => false,
                    'message' => 'Lane not found',
                    'data' => []
                ], 404);
            }

            $lane->update(['status' => false]);

            return response()->json([
                'status' => true,
                'message' => 'Lane deleted successfully',
                'data' => ['id' => $id]
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to delete lane: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to delete lane: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function checkLane(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Comment out authentication check for direct API access
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }
            
            // Use default values if user is not authenticated
            $user_id = $user ? $user->id : 1; // Default user ID

            $lane_name = $request->input('lane_name', '');
            $id = $request->input('id', '');

            $where = [
                'lane_name' => $lane_name,
                'user_id' => $user_id,
                'status' => true
            ];

            if ($id) {
                $where['id !='] = $id;
            }

            $check_lane = LanesMaster::where($where)->first();

            return response()->json([
                'status' => true,
                'message' => $check_lane ? 'Lane name already exists' : 'Lane name is available',
                'data' => ['exists' => $check_lane ? 1 : 2]
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to check lane name: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to check lane name: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    public function store(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Comment out authentication check for direct API access
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }
            
            // Use default values if user is not authenticated
            $user_id = $user ? $user->id : 1; // Default user ID
            $org_id = $user ? ($user->org_id ?? null) : 1; // Default org ID

            // Get all input fields with proper defaults and type casting
            $lane_name = $request->input('lane_name', '');
            $source_geo = (int) $request->input('source_geo', 0);
            $source_country = $request->input('source_country', '');
            $source = $request->input('source', '');
            $request_org_id = (int) $request->input('org_id', $org_id); // Use request org_id or fallback to user org_id
            $destination_geo = (int) $request->input('destination_geo', 0);
            $destination_country = $request->input('destination_country', '');
            $destination = $request->input('destination', '');
            $service_id = $request->input('service_id', '') ? (int) $request->input('service_id') : null;
            $be_value = $request->input('be_value', '');

            // Handle source and destination logic
            if ($source_geo == 2) {
                $source = $source_country;
            }
            if ($destination_geo == 2) {
                $destination = $destination_country;
            }

            // Validate required fields
            if (empty($lane_name)) {
                return response()->json([
                    'status' => false,
                    'message' => 'Lane name is required',
                    'data' => []
                ], 400);
            }

            $where = [
                'source_geo' => $source_geo,
                'source_country' => $source_country,
                'source' => $source,
                'destination_geo' => $destination_geo,
                'destination_country' => $destination_country,
                'destination' => $destination,
                'user_id' => $user_id,
                'status' => 1
            ];

            $check_lane = LanesMaster::where($where)->first();

            if (!$check_lane) {
                $lane_id = $this->generateLaneId();

                // Prepare data with proper type casting
                $lane_data = [
                    'lane_id' => $lane_id,
                    'lane_name' => $lane_name,
                    'source_geo' => $source_geo,
                    'source_country' => strtoupper($source_country),
                    'source' => $source,
                    'org_id' => $request_org_id,
                    'destination_geo' => $destination_geo,
                    'destination_country' => $destination_country,
                    'destination' => $destination,
                    'user_id' => $user_id,
                    'status' => 1,
                    'service_id' => $service_id,
                    'be_value' => $be_value,
                    'createdon' => now(),
                    'updatedon' => now(),
                ];

                // Remove null values to avoid database issues
                $lane_data = array_filter($lane_data, function($value) {
                    return $value !== null && $value !== '';
                });

                $lane = LanesMaster::create($lane_data);

                return response()->json([
                    'status' => true,
                    'message' => 'Lane created successfully',
                    'data' => $lane
                ], 201);
            }

            return response()->json([
                'status' => false,
                'message' => 'Lane already exists',
                'data' => ['result' => 2]
            ], 400);
        } catch (\Exception $e) {
            Log::error('Failed to create lane: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to create lane: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }

    protected function generateLaneId(): string
    {
        $user = Auth::user();
        
        // Comment out authentication check for direct API access
        // if (!$user) {
        //     throw new \Exception('User not authenticated');
        // }
        
        // Use default values if user is not authenticated
        $org_id = $user ? ($user->org_id ?? '') : '01'; // Default org ID
        $year = date('y');
        $week = date('W');
        $country_code = substr($org_id, 0, 2);

        $latest_lane = LanesMaster::select('lane_id')
            ->orderBy('id', 'desc')
            ->first();

        if ($latest_lane) {
            $get_lane_id = $latest_lane->lane_id;
            $previous_weeknumber = substr($get_lane_id, 6, 2);
            $get_lane_id1 = substr($get_lane_id, 8);
            $get_lane_id2 = ltrim($get_lane_id1, '0');

            if ($previous_weeknumber < $week) {
                $id1 = '0001';
            } else {
                $i_id = (int)$get_lane_id2 + 1;
                $id1 = str_pad($i_id, 4, '0', STR_PAD_LEFT);
            }

            $lane_id = "LN{$country_code}{$year}{$week}{$id1}";

            $exists = LanesMaster::where('lane_id', $lane_id)->exists();
            if ($exists) {
                $i_id = (int)$id1 + 1;
                $id1 = str_pad($i_id, 4, '0', STR_PAD_LEFT);
                $lane_id = "LN{$country_code}{$year}{$week}{$id1}";
            }
        } else {
            $lane_id = "LN{$country_code}{$year}{$week}0001";
        }

        return $lane_id;
    }

    public function getLaneId(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            
            // Comment out authentication check for direct API access
            // if (!$user) {
            //     return response()->json([
            //         'status' => false,
            //         'message' => 'User not authenticated',
            //         'data' => []
            //     ], 401);
            // }
            
            // Use default values if user is not authenticated
            $user_id = $user ? $user->id : 1; // Default user ID
            $lane_id = $request->input('lane_id', '');

            $lane_ids = LanesMaster::where('user_id', $user_id)
                ->where('lane_id', 'ILIKE', "%{$lane_id}%")
                ->pluck('lane_id')
                ->toArray();

            return response()->json([
                'status' => true,
                'message' => 'Lane IDs retrieved successfully',
                'data' => $lane_ids
            ], 200);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve lane IDs: ' . $e->getMessage());
            return response()->json([
                'status' => false,
                'message' => 'Failed to retrieve lane IDs: ' . $e->getMessage(),
                'data' => []
            ], 500);
        }
    }
}