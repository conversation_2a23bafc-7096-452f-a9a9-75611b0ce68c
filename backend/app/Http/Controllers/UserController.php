<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Carbon\Carbon;
use App\Models\User;
use App\Models\SxUserOrganization;
use App\Models\PrivilegeModule;
use App\Models\OrgModuleFeature;
use App\Models\PasswordResetTokens;
use App\Jobs\SendPasswordResetEmailJob;

class UserController extends Controller
{
    public function index()
    {
        return response()->json(User::all());
    }

    public function menu()
    {
        $user = Auth::guard('api')->user();
        $orgId = $user->default_org_id;
        $userOrganization = SxUserOrganization::where('user_id', $user->id)->where('org_id', $user->default_org_id)->first();
        $privilegeIds = json_decode($userOrganization->roles, true) ?? [];
        if(!empty($privilegeIds)){
            $prvmodules = PrivilegeModule::query()
            ->select([
                'sx_modules.id',
                'sx_modules.module_name',
                'sx_modules.menu_name',
                'sx_organization_modules.id as module_id',
                'sx_modules.status as jon2status',
                'sx_modules.menu_sequence',
            ])
            ->join('sx_organization_modules', 'sx_previllege_modules.module_id', '=', 'sx_organization_modules.id')
            ->join('sx_modules', 'sx_organization_modules.module_id', '=', 'sx_modules.id')
            ->whereIn('sx_previllege_modules.previllege_id', $privilegeIds)
            ->where('sx_previllege_modules.status', 1)
            ->where('sx_organization_modules.status', 1)
            ->where('sx_modules.status', 1)
            ->whereNull('sx_previllege_modules.deleted_at')
            ->orderBy('sx_modules.menu_sequence', 'asc')
            ->get();
        }
        $modules = [];
        foreach ($prvmodules as $module) {
            if (!$this->checkModuleExist($module['module_id'], $modules)) {
                $modules[] = $module;
            }
        }
        $moduleIds = array_column($modules, 'id');
        if (empty($privilegeIds) || empty($moduleIds)) {
            return response()->json(['success' => 'fail', 'error' => 'Missing required parameters'], 400);
        }
        $features = OrgModuleFeature::query()
            ->select([
                'sx_org_module_features.*',
                'sx_org_module_features.module_id',
                'sx_org_module_features.feature_id',
                'sx_module_features.menu_name AS feature_menu_name',
                'sx_module_features.method_name',
                'sx_modules.controller_name',
                'sx_module_features.feature_name',
                'sx_modules.menu_sequence',
                'sx_module_features.menu_sequence AS feature_menu_sequence',
            ])
            ->distinct()
            ->join('sx_module_features', 'sx_org_module_features.feature_id', '=', 'sx_module_features.id')
            ->join('sx_modules', 'sx_org_module_features.module_id', '=', 'sx_modules.id')
            ->leftJoin('sx_previlege_access_rules', function ($join) use ($privilegeIds) {
                $join->on('sx_org_module_features.feature_id', '=', 'sx_previlege_access_rules.feature_id')
                    ->whereIn('sx_previlege_access_rules.previllege_id', $privilegeIds);
            })
            ->where('sx_org_module_features.org_id', $orgId)
            ->whereIn('sx_org_module_features.module_id', $moduleIds)
            ->where('sx_previlege_access_rules.view_access', 1)
            ->where('sx_org_module_features.status', 1)
            ->whereNull('sx_org_module_features.deleted_at')
            ->orderBy('sx_modules.menu_sequence', 'asc')
            ->orderBy('sx_module_features.menu_sequence', 'asc')
            ->get();
        /* // Get the query log
        $queries = \DB::getQueryLog();
        // Get the last query
        $lastQuery = end($queries);
        // Log or output the last query
        if ($lastQuery) {
            $queryString = $lastQuery['query'];
            $bindings = $lastQuery['bindings'];
            // Simple replacement of ? with bindings (not perfect for all cases)
            $formattedQuery = vsprintf(str_replace('?', '%s', $queryString), array_map(function ($binding) {
                return is_string($binding) ? "'$binding'" : $binding;
            }, $bindings));
            \Log::info('Last executed query with bindings:', ['query' => $formattedQuery]);
        } */
        $submenu = $selectedModulesIds = [];
        foreach ($features as $feature) {
            $selectedModulesIds[] =  $feature['module_id'];
            $submenuCheck = [];
            $submenuCheck['id'] = $feature['feature_menu_name'] ?: $feature['feature_name'];
            if($submenuCheck['id'] == 'Fleet'){
                $submenuCheck['children'] = json_decode($feature['method_name'], true);
            }
            if($submenuCheck['id'] == 'Profiles'){
                $submenuCheck['children'] = json_decode($feature['method_name'], true);
            }
            if($submenuCheck['id'] == 'Pincodes'){
                $submenuCheck['children'] = json_decode($feature['method_name'], true);
            }
            $submenu[$feature['module_id']][] = $submenuCheck;
        }
        $moduleFeatures = [];
        if (!empty($features) && !empty($submenu)) {
            foreach ($modules as $module) {
                if (in_array($module['id'], $selectedModulesIds)) {
                    $moduleFeatures[] = [
                        "id" => $module['menu_name'] ?: $module['module_name'],
                        "children" => $submenu[$module['id']] ?? []
                    ];
                }
            }
        }
        return response()->json([
            'success' => 'success',
            'menus' => $moduleFeatures
        ]);
    }

    private function checkModuleExist($module_id, $modules)
    {
        foreach ($modules as $module) {
            if ($module['module_id'] == $module_id) {
                return true;
            }
        }
        return false;
    }

    public function forgotPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|exists:sx_users,emailid'
        ]);
        if ($validator->fails()) {
            return response()->json([
                'status' => 'fail',
                'errors' => $validator->errors()
            ], 404);
        }
        $token = Str::random(64);
        // Insert or update the password reset token
        $insertToken = PasswordResetTokens::updateOrInsert(
            ['email' => $request->email],
            [
                'email' => $request->email,
                'token' => $token,
                'created_at' => Carbon::now()
            ]
        );
        try {
            // Generate reset URL considering the frontend URL considering sample frontend URL
            // You can change this URL to your actual frontend URL where the reset password page is hosted
            $resetUrl = config('app.frontend_url', 'http://localhost:3000') . '/reset-password?token=' . $token . '&email=' . urlencode($request->email);

            // Dispatch email job to RabbitMQ
            SendPasswordResetEmailJob::dispatch($request->email, $token, $resetUrl);
             
            return response()->json([
                'status' => 'success',
                'message' => 'Password reset link sent to your email successfully.',
            ], 200);
        } catch (\Exception $e) {
            // If job dispatch fails, remove the token from database
            PasswordResetTokens::where(['email' => $request->email])->delete();
            
            return response()->json([
                'status' => 'fail',
                'message' => 'Failed to queue password reset email. Please try again.',
                'error' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }
    
    public function resetPassword(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string|min:6|confirmed',
            'token' => 'required|string',
        ]);

        // Verify token and check expiration (60 minutes)
        $resetToken = PasswordResetTokens::where([
            'email' => $request->email,
            'token' => $request->token,
        ])->where('created_at', '>', Carbon::now()->subMinutes(60))->first();

        if (!$resetToken) {
            // Clean up expired tokens
            PasswordResetTokens::where('email', $request->email)
                ->where('created_at', '<=', Carbon::now()->subMinutes(60))
                ->delete();
                
            return response()->json([
                'status' => 'fail',
                'error' => 'Invalid or expired token.'
            ], 400);
        }
        $user = User::where('emailid', $request->email)->update(['password' => Hash::make($request->password)]);

        $removeToken = PasswordResetTokens::where(['email' => $request->email])->delete();

        return response()->json([
            'status' => 'success',
            'message' => 'Password updated successfully.'
        ], 200);
    }

    function getStructureSequences()
	{
		global $CI;
		// $structures = $CI->session->userdata('default_org_structures');
        $user = Auth::guard('api')->user();
        $orgId = $user->default_org_id;
        $userOrganization = SxUserOrganization::where('user_id', $user->id)->where('org_id', $user->default_org_id)->first();
        $privilegeIds = json_decode($userOrganization->roles, true) ?? [];
        $structure_id = SxStructureMaster::where('org_id', $orgId)->where('id', $userOrganization->structure_id)->first();
		$default_org_privilege_ids = $CI->session->userdata('default_org_privilege_ids');
		$joined_table = $CI->SxBusinessEntity->table;
		$current_table = $CI->SxStructureSequence->table;
		$current_id = $current_table . '.id';
        $get_current_org_id = $CI->session->userdata('org_id');
		$sequences = $CI->SxStructureSequence->join($joined_table . ' AS sbe', 'sbe.id = ' . $current_table . '.parent_business_entity')
                        ->join($joined_table . ' AS sbee', 'sbee.id = ' . $current_table . '.child_business_entity')
                        ->where([$current_table . '.structure_id' => $structure_id->id, $current_table . '.org_id' => $get_current_org_id])
                        ->select("*, $current_id AS id, sbe.id AS parent_entity_id, sbe.entity_name AS parent_entity_name, sbee.entity_name AS child_entity_name, sbee.id AS child_entity_id")
                        ->getActiveRecords();
		$privileges = $CI->SxPrevilleges->whereIn("id", $default_org_privilege_ids)->getActiveRecords();
		$business_entities = [];
		foreach ($privileges as $privilege) {
			$business_entities[] = $privilege['business_entity'];
		}
		$sequenceArr = [];
		foreach ($sequences as $sequence) {
			$sequenceArr[] = $sequence['parent_business_entity'];
			$sequenceArr[] = $sequence['child_business_entity'];
		}
		$entity_ids = [];
		foreach ($business_entities as $business_entity) {
			$index = array_search($business_entity, $sequenceArr);
			if ($index >= 0) {
				for ($i = $index; $i < count($sequenceArr); $i++) {
					$entity_ids[] = $sequenceArr[$i];
				}
			}
		}
		$returnArr = [];
		$current_table = $CI->SxBusinessEntityValue->table;
		$joined_table = $CI->SxBusinessEntity->table;
		$entityvalue = $CI->SxBusinessEntityValue->join($joined_table, $joined_table . '.id = ' . $current_table . '.entity_id')->whereIn('entity_id ', $entity_ids)->select("*, $joined_table.entity_id as bentity_id, $current_table.id as id")->findAll();

        if ($orgid = $CI->session->userdata('current_org')) {
			$get_current_org_orgid = $orgid;
		}
		$org = $CI->SxOrganization->where(['id' => $get_current_org_id])->row_array();
		$get_current_org_orgid = $org['org_id'];

		if (!empty($entityvalue)) {
			foreach ($entityvalue as $value) {
				$returnArr[$value['id']] = [
					"id" => $value['id'],
					"value" => $get_current_org_orgid . " &#10148; " . $value['bentity_id'] . " &#10148; " . $value['entity_value']
				];
			}
		}
		return $returnArr;
	}

}