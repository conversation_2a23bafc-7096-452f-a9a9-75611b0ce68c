<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\VatMaster;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\ChargeCode;
use App\Models\VatCategory;
use App\Models\SxPartyMembers;
use App\Models\Lane;
use App\Models\LaneVat;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class VatMasterController extends Controller
{
    private function getAuthenticatedUser()
    {
        return Auth::user();
    }

    public function index(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json(['message' => 'Invalid or missing token', 'status' => 0], 401);
        }

        $post = $request->all();
        $data = [
            'org_id' => $user->org_id ?? '',
            'vatdata' => [],
            'postData' => $post,
        ];

        $whr = !empty($post) ? $this->searchVats($post) : [];
        $data['vatdata'] = VatMaster::getVatData($user->id, $whr);

        return response()->json($data);
    }

    private function searchVats(array $post): array
    {
        $whr = [];
        if (!empty($post['search_vatid'])) {
            $whr['v.vat_id'] = $post['search_vatid'];
        }
        if (!empty($post['search_vatname'])) {
            $whr['v.name'] = $post['search_vatname'];
        }
        if (!empty($post['search_custname'])) {
            $whr['cp.name'] = $post['search_custname'];
        }
        if (!empty($post['adv_vatid'])) {
            $whr['v.vat_id'] = $post['adv_vatid'];
        }
        if (!empty($post['adv_vatname'])) {
            $whr['v.name'] = $post['adv_vatname'];
        }
        if (!empty($post['adv_custname'])) {
            $whr['cp.name'] = $post['adv_custname'];
        }
        if (!empty($post['adv_vendorname'])) {
            $whr['vp.name'] = $post['adv_vendorname'];
        }
        return $whr;
    }

    public function add(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            $data = [
                'org_id' => $orgId,
                'be_value' => $user->be_value ?? $post['be_value'] ?? 0,
                'bstatus' => [],
                'bgroups' => [],
                'chargecodes' => ChargeCode::getChargeCodes(),
                'vatcategory' => VatCategory::getVatCategories($orgId),
            ];

            Log::info("vatmaster/add data for user ID {$userId}: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'VAT master data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in vatmaster/add for user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving VAT master data',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function viewRoleTypeList(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $type = $request->input('type', '');
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            if (empty($type)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Type parameter is required',
                    'data' => ['status' => 0]
                ], 400);
            }
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if (empty($userId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }

            $getRoleTypeList = SxPartyMembers::getRoleTypeList($userId, $orgId, $type);
            if (empty($getRoleTypeList)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No parties found for the specified type',
                    'data' => ['status' => 0, 'parties' => []]
                ], 404);
            }

            $data = array_map(function ($list) {
                return [
                    'check' => "<input class='twopartieslist' type='radio' name='selectparties' id='twopartieslist_{$list['id']}' value='{$list['customeridentifier']}' onchange='selectpartiesbyid({$list['id']})'>",
                    'id' => $list['id'],
                    'code' => $list['customeridentifier'],
                    'name' => $list['name'],
                    'email_id' => $list['email'],
                    'org_id' => $list['org_id'],
                    'be_value' => $list['be_value'],
                ];
            }, $getRoleTypeList);

            Log::info("viewRoleTypeList data for user ID {$userId}, type {$type}: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'Role type list retrieved successfully',
                'data' => ['status' => 1, 'parties' => $data]
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in viewRoleTypeList for user ID {$userId}, type {$type}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving role type list',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function getPartnerDetailsById(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $code = $request->input('id');
            $type = $request->input('type', '');
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if (empty($code)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing customer identifier',
                    'data' => ['status' => 0]
                ], 400);
            }
            if (empty($userId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if (empty($orgId)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }

            $partner = SxPartyMembers::getPartnerDetailsById($code);
            if ($partner) {
                $data = [
                    'status' => 1,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value,
                    'partner' => [
                        'customeridentifier' => $partner['customeridentifier'],
                        'type' => $type,
                    ]
                ];

                Log::info("getPartnerDetailsById data for user ID {$userId}, code {$code}: " . json_encode($data));

                return response()->json([
                    'status' => 'success',
                    'message' => 'Partner details retrieved successfully',
                    'data' => $data
                ], 200);
            }

            Log::warning("getPartnerDetailsById: Partner not found for code {$code}");
            return response()->json([
                'status' => 'error',
                'message' => 'Partner not found',
                'data' => ['status' => 0, 'partner' => []]
            ], 404);
        } catch (\Exception $e) {
            Log::error("Error in getPartnerDetailsById for user ID {$userId}, code {$code}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving partner details',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function saveLane(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            if (empty($post)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No data provided',
                    'data' => ['status' => 0]
                ], 400);
            }

            $vatId = $request->input('vat_id', 0);
            if ($vatId === 'undefined' || $vatId === '') {
                $vatId = 0;
            }

            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;
            $source_geo = $request->input('source_geo', 1);
            $source_country = $request->input('source_country', '');
            $destination_geo = $request->input('destination_geo', 1);
            $destination_country = $request->input('destination_country', '');

            if ($orgId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if ($userId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if (empty($source_country)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Source country is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if (empty($destination_country)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Destination country is required',
                    'data' => ['status' => 0]
                ], 422);
            }

            $insData = [
                'vatid' => $vatId,
                'source_geo' => $source_geo,
                'source_country' => $source_country,
                'destination_geo' => $destination_geo,
                'destination_country' => $destination_country,
                'org_id' => $orgId,
                'user_id' => $userId,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];

            $laneId = Lane::createLane($insData);
            if ($laneId) {
                $data = [
                    'status' => 1,
                    'laneid' => $laneId,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value
                ];

                Log::info("saveLane successful for user ID {$userId}, lane ID {$laneId}: " . json_encode($insData));

                return response()->json([
                    'status' => 'success',
                    'message' => 'Lane created successfully',
                    'data' => $data
                ], 200);
            } else {
                Log::error("saveLane failed to create lane for user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to create lane',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("Error in saveLane for user ID {$userId}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while creating lane',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function getRecentLanes(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if ($orgId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if ($userId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }

            $lanesData = Lane::getRecentLanes($userId, $orgId);
            if (empty($lanesData)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No recent lanes found',
                    'data' => ['status' => 0, 'lanes' => []]
                ], 404);
            }

            $lanes = array_map(function ($res) {
                $action = "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'> <a class='dropdown-toggle' data-toggle='dropdown' href='#'><span class='icon tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" .
                    "<li><a id='bEdit' type='button' class='btn btn-sm btn-default' onclick='rowEditlane(this,{$res['id']},0);'><span class='glyphicon glyphicon-pencil'></span>Edit</a></li>" .
                    "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deletelane({$res['id']},0);'><span class='glyphicon glyphicon-trash'></span>Remove</a></li>" .
                    "<li><a id='bAcep' type='button' class='btn btn-sm btn-default' style='display:none;' onclick='rowAcep(this);'><span class='glyphicon glyphicon-ok'></span>Update</a></li>" .
                    "<li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='lanerowAdd(this,0);'><span class='glyphicon glyphicon-plus'></span>Add Lane</a></li>" .
                    "<li><a id='innerpacking' type='button' class='btn btn-sm btn-default' onclick='addcharges(this,{$res['id']});'><span class='fa fa-archive'></span>Add charges</a></li>" .
                    "<li><a id='innerpacking' type='button' class='btn btn-sm btn-default' onclick='getIndchargesbylane(this,{$res['id']},1);'><span class='fa fa-archive'></span>Get charges</a></li>" .
                    "<input type='hidden' form='vat_data' class='laneids' name='laneids[]' value='{$res['id']}'>";

                $sourceGeo = match ($res['source_geo']) {
                    1 => 'Country',
                    2 => 'Province',
                    3 => 'City',
                    default => '',
                };

                $destinationGeo = match ($res['destination_geo']) {
                    1 => 'Country',
                    2 => 'Province',
                    3 => 'City',
                    default => '',
                };

                $radio = "<input type='radio' name='getcharges' id='getcharges' class='getcharges' value='' onclick='clearChargeCodes()'>";

                return [
                    'id' => $res['id'],
                    'radio' => $radio,
                    'source_geo' => $sourceGeo,
                    'source_country' => $res['source_country'] ?? '',
                    'destination_geo' => $destinationGeo,
                    'destination_country' => $res['destination_country'] ?? '',
                    'action' => $action,
                ];
            }, $lanesData);

            $data = [
                'status' => 1,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'lanes' => $lanes
            ];

            Log::info("getRecentLanes data for user ID {$userId}: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'Recent lanes retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getRecentLanes for user ID {$userId}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving recent lanes',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function getLaneById(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $id = $request->input('id', 0);
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if (empty($id) || !is_numeric($id) || $id <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing lane ID',
                    'data' => ['status' => 0]
                ], 400);
            }
            if ($orgId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if ($userId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }

            $laneData = Lane::getLaneById($id);
            if (empty($laneData)) {
                Log::warning("getLaneById: Lane not found for ID {$id}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lane not found',
                    'data' => ['status' => 0, 'lane' => []]
                ], 404);
            }

            $data = [
                'status' => 1,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'lane' => $laneData
            ];

            Log::info("getLaneById data for lane ID {$id}, user ID {$userId}: " . json_encode($data));
            return response()->json([
                'status' => 'success',
                'message' => 'Lane details retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getLaneById for lane ID {$id}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving lane details',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function updateLane(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $laneId = $request->input('lane_id', 0);
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;
            $source_geo = $request->input('source_geo', 1);
            $source_country = $request->input('source_country', '');
            $destination_geo = $request->input('destination_geo', 1);
            $destination_country = $request->input('destination_country', '');

            if (empty($laneId) || !is_numeric($laneId) || $laneId <= 0 || $laneId === 'undefined') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing lane ID',
                    'data' => ['status' => 0]
                ], 400);
            }
            if ($orgId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if ($userId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if (empty($source_country)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Source country is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if (empty($destination_country)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Destination country is required',
                    'data' => ['status' => 0]
                ], 422);
            }

            $laneExists = Lane::getLaneById($laneId);
            if (!$laneExists) {
                Log::warning("updateLane: Lane not found for ID {$laneId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lane not found',
                    'data' => ['status' => 0]
                ], 404);
            }

            $updateData = [
                'source_geo' => $source_geo,
                'source_country' => $source_country,
                'destination_geo' => $destination_geo,
                'destination_country' => $destination_country,
                'org_id' => $orgId,
                'user_id' => $userId,
                'status' => 1,
                'updated_at' => Carbon::now(),
            ];

            $updated = Lane::updateLane($laneId, $updateData);
            if ($updated) {
                $data = [
                    'status' => 1,
                    'laneid' => $laneId,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value
                ];

                Log::info("updateLane successful for lane ID {$laneId}, user ID {$userId}: " . json_encode($updateData));

                return response()->json([
                    'status' => 'success',
                    'message' => 'Lane updated successfully',
                    'data' => $data
                ], 200);
            } else {
                Log::error("updateLane failed for lane ID {$laneId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to update lane',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("Error in updateLane for lane ID {$laneId}, user ID {$userId}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while updating lane',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function getChargeDescription(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $id = $request->input('id', '');
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if (empty($id)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing charge code ID',
                    'data' => ['status' => 0]
                ], 400);
            }

            $description = ChargeCode::getDescriptionById($id);
            if ($description === null || $description === '') {
                Log::warning("getChargeDescription: Charge code not found for ID {$id}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Charge code not found',
                    'data' => ['status' => 0, 'description' => '']
                ], 404);
            }

            $data = [
                'status' => 1,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'description' => $description
            ];

            Log::info("getChargeDescription data for charge code ID {$id}, user ID {$userId}: " . json_encode($data));

            return response()->json([
                'status' => 'success',
                'message' => 'Charge description retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getChargeDescription for charge code ID {$id}, user ID {$userId}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving charge description',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function saveLaneVat(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            if (empty($post)) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'No data provided',
                    'data' => ['status' => 0]
                ], 400);
            }

            $laneId = $request->input('lane_id', 0);
            $chargeId = $request->input('charge_id', 1);
            $vat = $request->input('vat', 0);
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if (empty($laneId) || !is_numeric($laneId) || $laneId <= 0 || $laneId === 'undefined') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing lane ID',
                    'data' => ['status' => 0]
                ], 400);
            }
            if (empty($chargeId) || !is_numeric($chargeId) || $chargeId <= 0 || $chargeId === 'undefined') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing charge ID',
                    'data' => ['status' => 0]
                ], 400);
            }
            if ($vat === 'undefined' || $vat === '') {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing VAT value',
                    'data' => ['status' => 0]
                ], 400);
            }

            $laneExists = Lane::getLaneById($laneId);
            if (!$laneExists) {
                Log::warning("saveLaneVat: Lane not found for ID {$laneId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lane not found',
                    'data' => ['status' => 0]
                ], 404);
            }

            $chargeExists = ChargeCode::getDescriptionById($chargeId);
            if ($chargeExists === null || $chargeExists === '') {
                Log::warning("saveLaneVat: Charge code not found for ID {$chargeId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Charge code not found',
                    'data' => ['status' => 0]
                ], 404);
            }

            $insData = [
                'lane_id' => $laneId,
                'charge_id' => $chargeId,
                'vat' => $vat,
                'org_id' => $orgId,
                'user_id' => $userId,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ];

            $laneVatId = LaneVat::createLaneVat($insData);
            if ($laneVatId) {
                $data = [
                    'status' => 1,
                    'lanevatid' => $laneVatId,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value
                ];

                Log::info("saveLaneVat successful for lane VAT ID {$laneVatId}, user ID {$userId}: " . json_encode($insData));
                return response()->json([
                    'status' => 'success',
                    'message' => 'Lane VAT created successfully',
                    'data' => $data
                ], 200);
            } else {
                Log::error("saveLaneVat failed for lane ID {$laneId}, charge ID {$chargeId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to create lane VAT',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("Error in saveLaneVat for lane ID {$laneId}, charge ID {$chargeId}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while creating lane VAT',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function getLaneVats(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $laneId = $request->input('id', 0);
            $type = $request->input('type', 1);
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if (empty($laneId) || !is_numeric($laneId) || $laneId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing lane ID',
                    'data' => ['status' => 0]
                ], 400);
            }

            /* if (!in_array($type, [1, 2])) { // Assuming type can be 1 or 2 based on conditional HTML
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid type value',
                    'data' => ['status' => 0]
                ], 400);
            } */

            // Check if lane exists
            $laneExists = Lane::getLaneById($laneId);
            if (!$laneExists) {
                Log::warning("getLaneVats: Lane not found for ID {$laneId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lane not found',
                    'data' => ['status' => 0, 'lane_vats' => []]
                ], 404);
            }

            $laneVatsData = LaneVat::getLaneVats($laneId);
            if (empty($laneVatsData)) {
                Log::warning("getLaneVats: No lane VATs found for lane ID {$laneId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'No lane VATs found',
                    'data' => ['status' => 0, 'lane_vats' => []]
                ], 404);
            }

            $laneVats = array_map(function ($res) use ($laneId, $type) {
                $action = $type == 1
                    ? "<ul class='nav nav-tabs'><li class='dropdown tablebtnrleft'> <a class='dropdown-toggle' data-toggle='dropdown' href='#'><span class='icon tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" .
                    "<li><a id='bEdit' type='button' class='btn btn-sm btn-default' onclick='rowEditcharge(this,{$res['id']},{$laneId});'><span class='glyphicon glyphicon-pencil'></span>Edit</a></li>" .
                    "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='deletecharge({$res['id']},{$laneId});'><span class='glyphicon glyphicon-trash'></span>Remove</a></li>" .
                    "<li><a id='bAcep' type='button' class='btn btn-sm btn-default' style='display:none;' onclick='rowAcep(this);'><span class='glyphicon glyphicon-ok'></span>Update</a></li>" .
                    "<li><a id='innerpacking' type='button' class='btn btn-sm btn-default' onclick='addindvidualcharges(this,{$laneId});'><span class='fa fa-archive'></span>Add charges</a></li>"
                    : '';

                return [
                    'charge_code' => $res['charge_code'] ?? '',
                    'description' => $res['description'] ?? '',
                    'vat' => $res['vat'] ?? 0,
                    'action' => $action,
                ];
            }, $laneVatsData);

            $data = [
                'status' => 1,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'lane_vats' => $laneVats
            ];

            Log::info("getLaneVats data for lane ID {$laneId}, user ID {$userId}: " . json_encode($data));
            return response()->json([
                'status' => 'success',
                'message' => 'Lane VATs retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getLaneVats for lane ID {$laneId}, user ID {$userId}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving lane VATs',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function getChargeById(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $id = $request->input('id', 0);
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if (empty($id) || !is_numeric($id) || $id <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid or missing charge VAT ID',
                    'data' => ['status' => 0]
                ], 400);
            }

            $chargeVatData = LaneVat::getChargeVatById($id);
            if (empty($chargeVatData)) {
                Log::warning("getChargeById: Charge VAT not found for ID {$id}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Charge VAT not found',
                    'data' => ['status' => 0, 'charge_vat' => []]
                ], 404);
            }

            $data = [
                'status' => 1,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'charge_vat' => $chargeVatData
            ];

            Log::info("getChargeById data for charge VAT ID {$id}, user ID {$userId}: " . json_encode($data));
            return response()->json([
                'status' => 'success',
                'message' => 'Charge VAT details retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getChargeById for charge VAT ID {$id}, user ID {$userId}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving charge VAT details',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function updateVat(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }
        $post = $request->all() ?? [];
        $orgId = $user->org_id ?? $post['org_id'] ?? 0;
        $userId = $user->user_id ?? $post['user_id'] ?? 0;
        $be_value = $user->be_value ?? $post['be_value'] ?? 0;
        $vatId = $post['vat_id'];
        $chargeId = $post['charge_id'];
        $vat = $post['vat'];
        try {

            $validator = Validator::make($post, [
                'vat_id' => 'required|integer|exists:lane_vat,id',
                'charge_id' => 'required|integer|exists:charge_codes,id',
                'vat' => 'required|numeric|min:0',
                'destination_geo' => 'nullable|integer',
                'destination_country' => 'nullable|string|max:255',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'data' => ['status' => 0, 'errors' => $validator->errors()]
                ], 422);
            }


            $destinationGeo = $post['destination_geo'] ?? null;
            $destinationCountry = $post['destination_country'] ?? null;

            $updateData = [
                'charge_id' => $chargeId,
                'vat' => $vat,
                'destination_geo' => $destinationGeo,
                'destination_country' => $destinationCountry,
                'org_id' => $orgId,
                'user_id' => $userId,
                'updated_at' => Carbon::now(),
            ];

            $updated = LaneVat::where('id', $vatId)->update($updateData);
            if ($updated) {
                $data = [
                    'status' => 1,
                    'vat_id' => $vatId,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value
                ];

                Log::info("updateVat successful for VAT ID {$vatId}, user ID {$userId}: " . json_encode($updateData));
                return response()->json([
                    'status' => 'success',
                    'message' => 'VAT updated successfully',
                    'data' => $data
                ], 200);
            } else {
                Log::error("updateVat failed for VAT ID {$vatId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to update VAT',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("Error in updateVat for VAT ID {$vatId}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while updating VAT',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function deleteCharge(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            // Validate input
            $validator = Validator::make($post, [
                'lane_vat' => ['required', 'integer', 'exists:lane_vat,id']
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'data' => ['status' => 0, 'errors' => $validator->errors()]
                ], 422);
            }

            // Extract validated data
            $laneVatId = $post['lane_vat'];

            if ($orgId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Organization ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }
            if ($userId <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'User ID is required',
                    'data' => ['status' => 0]
                ], 422);
            }

            $laneVat = LaneVat::where('id', $laneVatId)->where('org_id', $orgId)->first();
            if (!$laneVat) {
                Log::warning("deleteCharge: Lane VAT not found or does not belong to org_id {$orgId} for ID {$laneVatId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lane VAT not found or unauthorized',
                    'data' => ['status' => 0]
                ], 404);
            }

            $updated = LaneVat::where('id', $laneVatId)->update([
                'status' => 0,
                'updated_at' => Carbon::now(),
            ]);

            if ($updated) {
                $data = [
                    'status' => 1,
                    'lane_vat_id' => $laneVatId,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value
                ];

                Log::info("deleteCharge successful for lane VAT ID {$laneVatId}, user ID {$userId}");
                return response()->json([
                    'status' => 'success',
                    'message' => 'Lane VAT deleted successfully',
                    'data' => $data
                ], 200);
            } else {
                Log::error("deleteCharge failed for lane VAT ID {$laneVatId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to delete lane VAT',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("Error in deleteCharge for lane VAT ID {$laneVatId}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while deleting lane VAT',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function deleteLane(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            $validator = Validator::make($post, [
                'laneid' => ['required', 'integer', 'exists:lanes,id'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'data' => ['status' => 0, 'errors' => $validator->errors()]
                ], 422);
            }

            $laneId = $post['laneid'];


            $lane = Lane::where('id', $laneId)->where('org_id', $orgId)->first();
            if (!$lane) {
                Log::warning("deleteLane: Lane not found or does not belong to org_id {$orgId} for ID {$laneId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Lane not found or unauthorized',
                    'data' => ['status' => 0]
                ], 404);
            }

            $activeLaneVats = LaneVat::where('lane_id', $laneId)->where('status', 1)->exists();
            if ($activeLaneVats) {
                Log::warning("deleteLane: Cannot delete lane ID {$laneId} due to active lane VATs, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot delete lane with active VAT records',
                    'data' => ['status' => 0]
                ], 409);
            }

            $updated = Lane::where('id', $laneId)->update([
                'status' => 0,
                'updated_at' => Carbon::now(),
            ]);

            if ($updated) {
                $data = [
                    'status' => 1,
                    'lane_id' => $laneId,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value
                ];

                Log::info("deleteLane successful for lane ID {$laneId}, user ID {$userId}");

                return response()->json([
                    'status' => 'success',
                    'message' => 'Lane deleted successfully',
                    'data' => $data
                ], 200);
            } else {
                Log::error("deleteLane failed for lane ID {$laneId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to delete lane',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("Error in deleteLane for lane ID {$laneId}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while deleting lane',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function insertVatData(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            $validator = Validator::make($post, [
                'vat_name' => ['required', 'string', 'max:255'],
                'vat_description' => ['nullable', 'string', 'max:255'],
                'customeridentifier' => ['nullable', 'string', 'max:255'],
                'vendoridentifier' => ['nullable', 'string', 'max:255'],
                'laneids' => ['nullable', 'array'],
                'laneids.*' => ['integer', 'exists:lanes,id'],
                'vat_category' =>  ['nullable', 'string', 'max:255'],
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed',
                    'data' => ['status' => 0, 'errors' => $validator->errors()]
                ], 422);
            }

            $vatName = $post['vat_name'];
            $vatDescription = $post['vat_description'] ?? '';
            $customerIdentifier = $post['customeridentifier'] ?? '';
            $vendorIdentifier = $post['vendoridentifier'] ?? '';
            $laneIds = $post['laneids'] ?? [];
            $vatCategory = $post['vat_category'] ?? '';

            $catId = 0;
            $catVal = '';
            if ($vatCategory) {
                [$catId, $catVal] = explode('_', $vatCategory) + [0, ''];
                $catId = (int)$catId;
            }

            $generic = ($customerIdentifier === '' && $vendorIdentifier === '') ? 1 : 0;

            if (!empty($laneIds)) {
                $validLanes = Lane::whereIn('id', $laneIds)->where('org_id', $orgId)->count();
                if ($validLanes !== count($laneIds)) {
                    Log::warning("insertVatData: Some lane IDs do not belong to org_id {$orgId} for user ID {$userId}");
                    return response()->json([
                        'status' => 'error',
                        'message' => 'Some lane IDs are invalid or unauthorized',
                        'data' => ['status' => 0]
                    ], 422);
                }
            }

            $vat = VatMaster::create([
                'vat_id' => 0,
                'name' => $vatName,
                'description' => $vatDescription,
                'org_id' => $orgId,
                'customeridentifier' => $customerIdentifier,
                'vendoridentifier' => $vendorIdentifier,
                'generic' => $generic,
                'cat_id' => $catId,
                'cat_val' => $catVal,
                'user_id' => $userId,
                'status' => 1,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            $orgName = 'shipmentx';
            $ccode = strtoupper(substr((string)$orgName, 0, 2));
            if (strlen($ccode) < 2) {
                Log::warning("insertVatData: Invalid org_id {$orgName} for generating vat_id, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid organization ID for VAT ID generation',
                    'data' => ['status' => 0]
                ], 422);
            }

            $vatId = str_pad($vat->id, 6, '0', STR_PAD_LEFT);
            $vatCode = $ccode . $vatId;

            $vat->update(['vat_id' => $vatCode]);
            $lanesUpdated = 0;
            if (!empty($laneIds)) {
                $lanesUpdated = Lane::whereIn('id', $laneIds)->update(['vatid' => $vat->id]);
            }
            $data = [
                'status' => 1,
                'vat_id' => $vat->id,
                'vat_code' => $vatCode,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'lanes_updated' => $lanesUpdated
            ];

            Log::info("insertVatData successful for VAT ID {$vat->id}, vat_code {$vatCode}, user ID {$userId}: " . json_encode($data));
            return response()->json([
                'status' => 'success',
                'message' => 'VAT data inserted successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in insertVatData for user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while inserting VAT data',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function getLanes(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            // Validate input
            // $validator = Validator::make($post, [
            //     'vatid' => ['required', 'integer', 'exists:vat_master,id'], // Check vatid in vat_master
            //     'org_id' => ['nullable', 'integer'], // Align with other endpoints
            // ]);

            // if ($validator->fails()) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Validation failed',
            //         'data' => ['status' => 0, 'errors' => $validator->errors()]
            //     ], 422);
            // }

            $vatId = $post['vatid'];

            $vatExists = VatMaster::where('id', $vatId)->where('org_id', $orgId)->exists();
            if (!$vatExists) {
                Log::warning("getLanes: VAT ID {$vatId} not found or does not belong to org_id {$orgId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'VAT ID not found or unauthorized',
                    'data' => ['status' => 0, 'lanes' => []]
                ], 404);
            }

            $lanes = Lane::where('vatid', $vatId)
                ->where('org_id', $orgId)
                ->where('status', 1)
                ->orderBy('id', 'DESC')
                ->get()
                ->map(function ($lane) {
                    return [
                        'id' => $lane->id,
                        'source_geo' => $lane->source_geo,
                        'source_country' => $lane->source_country,
                        'destination_geo' => $lane->destination_geo,
                        'destination_country' => $lane->destination_country,
                        'vatid' => $lane->vatid,
                        'org_id' => $lane->org_id,
                        'user_id' => $lane->user_id,
                        'status' => $lane->status,
                        'created_at' => $lane->created_at,
                        'updated_at' => $lane->updated_at,
                    ];
                })
                ->toArray();

            $data = [
                'status' => 1,
                'vat_id' => $vatId,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'lanes' => $lanes
            ];

            Log::info("getLanes successful for VAT ID {$vatId}, org_id {$orgId}, user ID {$userId}: " . json_encode($data));
            if (empty($lanes)) {
                Log::warning("getLanes: No active lanes found for VAT ID {$vatId}, org_id {$orgId}, user ID {$userId}");
                return response()->json([
                    'status' => 'success',
                    'message' => 'No active lanes found for the specified VAT ID',
                    'data' => $data
                ], 200);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Lanes retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in getLanes for VAT ID {$vatId}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving lanes',
                'data' => ['status' => 0, 'lanes' => []],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function editVat(Request $request, $id)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if (!is_numeric($id) || $id <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid VAT ID',
                    'data' => ['status' => 0]
                ], 422);
            }


            $data = [
                'status' => 1,
                'vat_id' => (int)$id,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'lanes' => [],
                'vatdata' => [],
                'chargecodes' => [],
                'vatcategory' => []
            ];

            $vatData = VatMaster::where('id', $id)->where('org_id', $orgId)->first();
            if (!$vatData) {
                Log::warning("editVat: VAT ID {$id} not found or does not belong to org_id {$orgId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'VAT record not found or unauthorized',
                    'data' => ['status' => 0, 'lanes' => [], 'vatdata' => [], 'chargecodes' => [], 'vatcategory' => []]
                ], 404);
            }
            $data['vatdata'] = $vatData->toArray();

            $lanes = Lane::where('vatid', $id)
                ->where('org_id', $orgId)
                ->where('status', 1)
                ->orderBy('id', 'DESC')
                ->get()
                ->map(function ($lane) {
                    return [
                        'id' => $lane->id,
                        'source_geo' => $lane->source_geo,
                        'source_country' => $lane->source_country,
                        'destination_geo' => $lane->destination_geo,
                        'destination_country' => $lane->destination_country,
                        'vatid' => $lane->vatid,
                        'org_id' => $lane->org_id,
                        'user_id' => $lane->user_id,
                        'status' => $lane->status,
                        'created_at' => $lane->created_at,
                        'updated_at' => $lane->updated_at,
                    ];
                })->toArray();
            $data['lanes'] = $lanes;

            $chargeCodes = ChargeCode::select('id', 'charge_code')
                ->where('status', 1)
                ->get()
                ->map(function ($res) {
                    return [
                        'charge_id' => $res->id,
                        'charge_code' => $res->charge_code,
                    ];
                })->toArray();
            $data['chargecodes'] = $chargeCodes;

            $vatCategories = VatCategory::select('id', 'description', 'vat_category', 'vat_percentage')
                ->where('org_id', $orgId)
                ->where('status', 1)
                ->get()
                ->map(function ($res) {
                    $val = $res->id . '_' . $res->vat_category;
                    $desc = $res->description . ' (' . $res->vat_category . '-' . $res->vat_percentage . '%)';
                    return [
                        'id' => $res->id,
                        'val' => $val,
                        'desc' => $desc,
                    ];
                })->toArray();
            $data['vatcategory'] = $vatCategories;

            Log::info("editVat successful for VAT ID {$id}, org_id {$orgId}, user ID {$userId}: " . json_encode($data));
            return response()->json([
                'status' => 'success',
                'message' => 'VAT data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in editVat for VAT ID {$id}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving VAT data',
                'data' => ['status' => 0, 'lanes' => [], 'vatdata' => [], 'chargecodes' => [], 'vatcategory' => []],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function updateVatData(Request $request)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            // $validator = Validator::make($post, [
            //     'vat_masterid' => ['required', 'integer', 'exists:vat_master,id'], // Corrected table name
            //     'vat_name' => ['required', 'string', 'max:255'],
            //     'vat_description' => ['nullable', 'string', 'max:255'],
            //     'org_id' => ['nullable', 'integer'], // Align with other endpoints
            //     'customeridentifier' => ['nullable', 'string', 'max:255'],
            //     'vendoridentifier' => ['nullable', 'string', 'max:255'],
            //     'vat_category' => ['nullable', 'regex:/^\d+_[a-zA-Z0-9]+$/'], // e.g., "1_category"
            // ]);

            // if ($validator->fails()) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Validation failed',
            //         'data' => ['status' => 0, 'errors' => $validator->errors()]
            //     ], 422);
            // }

            $vatMasterId = $post['vat_masterid'];
            $vatName = $post['vat_name'];
            $vatDescription = $post['vat_description'] ?? '';
            $customerIdentifier = $post['customeridentifier'] ?? '';
            $vendorIdentifier = $post['vendoridentifier'] ?? '';
            $vatCategory = $post['vat_category'] ?? '';



            // Verify vat_masterid belongs to org_id
            $vatExists = VatMaster::where('id', $vatMasterId)->where('org_id', $orgId)->exists();
            if (!$vatExists) {
                Log::warning("updateVatData: VAT ID {$vatMasterId} not found or does not belong to org_id {$orgId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'VAT record not found or unauthorized',
                    'data' => ['status' => 0]
                ], 404);
            }

            $catId = 0;
            $catVal = '';
            if ($vatCategory) {
                [$catId, $catVal] = explode('_', $vatCategory) + [0, ''];
                $catId = (int)$catId;
            }

            $generic = ($customerIdentifier === '' && $vendorIdentifier === '') ? 1 : 0;

            $updateData = [
                'name' => $vatName,
                'description' => $vatDescription,
                'org_id' => $orgId,
                'customeridentifier' => $customerIdentifier,
                'vendoridentifier' => $vendorIdentifier,
                'generic' => $generic,
                'cat_id' => $catId,
                'cat_val' => $catVal,
                'user_id' => $userId,
                'status' => 1,
                'updated_at' => Carbon::now(),
            ];

            $updated = VatMaster::where('id', $vatMasterId)->update($updateData);

            if ($updated) {
                $data = [
                    'status' => 1,
                    'vat_masterid' => $vatMasterId,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value
                ];

                Log::info("updateVatData successful for VAT ID {$vatMasterId}, user ID {$userId}: " . json_encode($updateData));

                return response()->json([
                    'status' => 'success',
                    'message' => 'VAT data updated successfully',
                    'data' => $data
                ], 200);
            } else {
                Log::error("updateVatData failed for VAT ID {$vatMasterId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to update VAT data',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            Log::error("Error in updateVatData for VAT ID {$vatMasterId}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while updating VAT data',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function viewVat(Request $request, $id)
    {
        $user = $this->getAuthenticatedUser();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token',
                'data' => ['status' => 0]
            ], 401);
        }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            if (!is_numeric($id) || $id <= 0) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Invalid VAT ID',
                    'data' => ['status' => 0]
                ], 422);
            }

            $data = [
                'status' => 1,
                'vat_id' => (int)$id,
                'org_id' => $orgId,
                'user_id' => $userId,
                'be_value' => $be_value,
                'lanes' => [],
                'vatdata' => [],
                'cat_desc' => ''
            ];

            $vatData = VatMaster::where('id', $id)->where('org_id', $orgId)->first();
            if (!$vatData) {
                Log::warning("viewVat: VAT ID {$id} not found or does not belong to org_id {$orgId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'VAT record not found or unauthorized',
                    'data' => ['status' => 0, 'lanes' => [], 'vatdata' => [], 'cat_desc' => '']
                ], 404);
            }
            $data['vatdata'] = $vatData->toArray();

            $catId = $vatData->cat_id ?? 0;
            if ($catId > 0) {
                $catInfo = VatCategory::where('id', $catId)->where('org_id', $orgId)->first();
                if ($catInfo) {
                    $data['cat_desc'] = $catInfo->description . ' (' . $catInfo->vat_category . '-' . $catInfo->vat_percentage . '%)';
                }
            }

            $lanes = Lane::where('vatid', $id)
                ->where('org_id', $orgId)
                ->where('status', 1)
                ->orderBy('id', 'DESC')
                ->get()
                ->map(function ($lane) {
                    return [
                        'id' => $lane->id,
                        'source_geo' => $lane->source_geo,
                        'source_country' => $lane->source_country,
                        'destination_geo' => $lane->destination_geo,
                        'destination_country' => $lane->destination_country,
                        'vatid' => $lane->vatid,
                        'org_id' => $lane->org_id,
                        'user_id' => $lane->user_id,
                        'status' => $lane->status,
                        'created_at' => $lane->created_at,
                        'updated_at' => $lane->updated_at,
                    ];
                })->toArray();
            $data['lanes'] = $lanes;

            Log::info("viewVat successful for VAT ID {$id}, org_id {$orgId}, user ID {$userId}: " . json_encode($data));
            return response()->json([
                'status' => 'success',
                'message' => 'VAT data retrieved successfully',
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            Log::error("Error in viewVat for VAT ID {$id}, user ID {$userId}: " . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while retrieving VAT data',
                'data' => ['status' => 0, 'lanes' => [], 'vatdata' => [], 'cat_desc' => ''],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }

    public function deleteVatMaster(Request $request)
    {
        // $user = $this->getAuthenticatedUser();
        // if (!$user) {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Invalid or missing token',
        //         'data' => ['status' => 0]
        //     ], 401);
        // }

        try {
            $post = $request->all() ?? [];
            $orgId = $user->org_id ?? $post['org_id'] ?? 0;
            $userId = $user->user_id ?? $post['user_id'] ?? 0;
            $be_value = $user->be_value ?? $post['be_value'] ?? 0;

            // $validator = Validator::make($post, [
            //     'id' => ['required', 'integer', 'exists:vat_master,id'], // Corrected table name
            // ]);

            // if ($validator->fails()) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Validation failed',
            //         'data' => ['status' => 0, 'errors' => $validator->errors()]
            //     ], 422);
            // }

            $id = $post['id'];

            $vatExists = VatMaster::where('id', $id)->where('org_id', $orgId)->first();
            if (!$vatExists) {
                Log::warning("deleteVatMaster: VAT ID {$id} not found or does not belong to org_id {$orgId}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'VAT record not found or unauthorized',
                    'data' => ['status' => 0]
                ], 404);
            }

            $activeLanes = Lane::where('vatid', $id)->where('status', 1)->exists();
            if ($activeLanes) {
                Log::warning("deleteVatMaster: Cannot delete VAT ID {$id} due to active lanes, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Cannot delete VAT with active lanes',
                    'data' => ['status' => 0]
                ], 409);
            }

            $updated = VatMaster::where('id', $id)->update([
                'status' => 0,
                'updated_at' => Carbon::now(),
            ]);

            if ($updated) {
                $data = [
                    'status' => 1,
                    'vat_masterid' => $id,
                    'org_id' => $orgId,
                    'user_id' => $userId,
                    'be_value' => $be_value
                ];

                // Log success for debugging
                Log::info("deleteVatMaster successful for VAT ID {$id}, user ID {$userId}");

                return response()->json([
                    'status' => 'success',
                    'message' => 'VAT record deleted successfully',
                    'data' => $data
                ], 200);
            } else {
                Log::error("deleteVatMaster failed for VAT ID {$id}, user ID {$userId}");
                return response()->json([
                    'status' => 'error',
                    'message' => 'Failed to delete VAT record',
                    'data' => ['status' => 0]
                ], 500);
            }
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error("Error in deleteVatMaster for VAT ID {$id}, user ID {$userId}: " . $e->getMessage());

            return response()->json([
                'status' => 'error',
                'message' => 'An error occurred while deleting VAT record',
                'data' => ['status' => 0],
                'error_details' => env('APP_DEBUG', false) ? $e->getMessage() : null
            ], 500);
        }
    }
}
