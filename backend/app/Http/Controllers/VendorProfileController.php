<?php

namespace App\Http\Controllers;

use App\Models\VendorProfile;
use App\Models\VendorProfileList;
use App\Models\SxPartyMembers;
use App\Models\DepartmentMaster;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class VendorProfileController extends Controller
{
    public function index(Request $request)
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            // $org_id = $user->org_id;
            // $be_value = $user->be_value;
            // $user_id = $user->id;
            
            // Temporary hardcoded values for testing
            $org_id = 44;
            $be_value = 1;
            $user_id = 1;

            $query = VendorProfile::query()
                ->where('user_id', $user_id)
                ->where('org_id', $org_id);

            // Apply filters based on request
            if ($request->input('searchsubmit') === 'Search') {
                $post = $request->all();
                
                if (!empty($post['name1'])) {
                    $query->where('name', 'like', '%' . $post['name1'] . '%');
                }

                if (!empty($post['customer_name'])) {
                    $query->where('name', 'like', '%' . $post['customer_name'] . '%');
                }

                if (!empty($post['fromdate'])) {
                    $fromdate = Carbon::parse($post['fromdate'])->format('Y-m-d');
                    $query->whereDate('created_at', '>=', $fromdate);
                }

                if (!empty($post['todate'])) {
                    $todate = Carbon::parse($post['todate'])->format('Y-m-d');
                    $query->whereDate('created_at', '<=', $todate);
                }

                if (!empty($post['profile_id1'])) {
                    $query->where('vend_profile_id', $post['profile_id1']);
                }

                if (!empty($post['profile_id'])) {
                    $query->where('vend_profile_id', $post['profile_id']);
                }

                if (isset($post['status']) && $post['status'] !== '') {
                    $query->where('status', $post['status']);
                } else {
                    $query->where('status', 1);
                }
            }

            // Apply org_id filter
            if ($org_id !== '44') {
                $query->where('be_value', $be_value);
            }

            $vendorProfiles = $query->get();

            $mappedProfiles = $vendorProfiles->map(fn($row) => [
                'id' => $row->id,
                'vend_profile_id' => $row->vend_profile_id,
                'name' => $row->name,
                'description' => $row->description,
                'status' => $row->status,
                'org_id' => $row->org_id,
                'be_value' => $row->be_value,
                'user_id' => $row->user_id,
                'created_at' => $row->created_at,
                'updated_at' => $row->updated_at,
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor profiles retrieved successfully.',
                'data' => $mappedProfiles,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching vendor profiles', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch vendor profiles.',
                'debug' => config('app.debug') ? $e->getMessage() : null,
            ], 500);
        }
    }

    public function create(Request $request)
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            // $org_id = $user->org_id;
            // $be_value = $user->be_value;
            // $user_id = $user->id;
            
            // Temporary hardcoded values for testing
            $org_id = 44;
            $be_value = 1;
            $user_id = 1;

            // Get customers for dropdown
            $customers = SxPartyMembers::select('id', 'name')
                ->where('user_id', $user_id)
                ->where('status', true)
                ->orderBy('name')
                ->get();

            // Get departments for dropdown
            $departments = DepartmentMaster::select('id', 'department_name')
                ->where('org_id', $org_id)
                ->where('be_value', $be_value)
                ->where('status', true)
                ->orderBy('department_name')
                ->get();

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor profile creation form data retrieved successfully.',
                'data' => [
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'user_id' => $user_id,
                    'customers' => $customers,
                    'departments' => $departments,
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error getting vendor profile creation form data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get vendor profile creation form data.',
            ], 500);
        }
    }
    
    public function store(Request $request)
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            $validator = Validator::make($request->all(), [
                'profile_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                // 'org_id' => 'required|string',
                // 'be_value' => 'required|string',
                // 'vendor_ids' => 'required|string',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Temporary hardcoded values for testing
            $user_id = 1;
            $org_id = 44;
            $be_value = 1;

            $vend_profile_id = $this->generateVendorProfileId();

            $vendorProfile = VendorProfile::create([
                'vend_profile_id' => $vend_profile_id,
                'name' => $request->input('profile_name'),
                'description' => $request->input('description'),
                'org_id' => $org_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'status' => 1,
            ]);

            // Update vendor profile list if vendor_ids provided
            if ($request->has('vendor_ids') && !empty($request->input('vendor_ids'))) {
                $vendor_ids = explode(',', $request->input('vendor_ids'));
                VendorProfileList::whereIn('id', $vendor_ids)
                    ->update(['vp_id' => $vendorProfile->id]);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor profile created successfully.',
                'data' => $vendorProfile,
            ], 201);
        } catch (\Exception $e) {
            Log::error('Error creating vendor profile', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to create vendor profile.',
            ], 500);
        }
    }

    public function show($id)
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            // $org_id = $user->org_id;
            // $be_value = $user->be_value;
            
            // Temporary hardcoded values for testing
            $org_id = 44;
            $be_value = 1;

            $vendorProfile = VendorProfile::byOrgAndBe($id, $org_id, $be_value);

            if (!$vendorProfile) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vendor profile not found.',
                ], 404);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor profile retrieved successfully.',
                'data' => $vendorProfile,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error fetching vendor profile', [
                'error' => $e->getMessage(),
                'id' => $id,
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to fetch vendor profile.',
            ], 500);
        }
    }

    public function edit($id)
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            // $org_id = $user->org_id;
            // $be_value = $user->be_value;
            // $user_id = $user->id;
            
            // Temporary hardcoded values for testing
            $org_id = 44;
            $be_value = 1;
            $user_id = 1;

            $vendorProfile = VendorProfile::byOrgAndBe($id, $org_id, $be_value);

            if (!$vendorProfile) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vendor profile not found.',
                ], 404);
            }

            // Get customers for dropdown
            $customers = SxPartyMembers::select('id', 'name')
                ->where('user_id', $user_id)
                ->where('status', true)
                ->orderBy('name')
                ->get();

            // Get departments for dropdown
            $departments = DepartmentMaster::select('id', 'department_name')
                ->where('org_id', $org_id)
                ->where('be_value', $be_value)
                ->where('status', true)
                ->orderBy('department_name')
                ->get();

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor profile edit form data retrieved successfully.',
                'data' => [
                    'vendor_profile' => $vendorProfile,
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'user_id' => $user_id,
                    'customers' => $customers,
                    'departments' => $departments,
                ],
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error getting vendor profile edit form data', [
                'error' => $e->getMessage(),
                'id' => $id,
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to get vendor profile edit form data.',
            ], 500);
        }
    }

    public function update(Request $request, $id)
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            $validator = Validator::make($request->all(), [
                'profile_name' => 'required|string|max:255',
                'description' => 'nullable|string',
                // 'org_id' => 'required|string',
                // 'be_value' => 'required|string',
                'status' => 'nullable|in:0,1',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Temporary hardcoded values for testing
            $user_id = 1;
            $org_id = 44;
            $be_value = 1;

            $vendorProfile = VendorProfile::where('id', $id)
                ->where('user_id', $user_id)
                ->first();

            if (!$vendorProfile) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vendor profile not found.',
                ], 404);
            }

            $vendorProfile->update([
                'name' => $request->input('profile_name'),
                'description' => $request->input('description'),
                'org_id' => $org_id,
                'be_value' => $be_value,
                'status' => $request->input('status', 1),
            ]);

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor profile updated successfully.',
                'data' => $vendorProfile,
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error updating vendor profile', [
                'error' => $e->getMessage(),
                'id' => $id,
                'request' => $request->all(),
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update vendor profile.',
            ], 500);
        }
    }

    public function destroy($id)
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            // Temporary hardcoded values for testing
            $org_id = 44;
            $be_value = 1;

            $vendorProfile = VendorProfile::where('id', $id)
                ->where('org_id', $org_id)
                ->where('be_value', $be_value)
                ->first();

            if (!$vendorProfile) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Vendor profile not found.',
                ], 404);
            }

            $vendorProfile->update(['status' => 0]);

            return response()->json([
                'status' => 'success',
                'message' => 'Vendor profile deleted successfully.',
            ], 200);
        } catch (\Exception $e) {
            Log::error('Error deleting vendor profile', [
                'error' => $e->getMessage(),
                'id' => $id,
            ]);
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to delete vendor profile.',
            ], 500);
        }
    }

    public function getVendorProfileId(Request $request)
    {
        try {
            // Temporary hardcoded values for testing
            $user_id = 1;

            $profile_id1 = $request->input('profile_id1', '');
            $profile_id1 = trim($profile_id1);

            $data = VendorProfile::select('vend_profile_id')
                ->where('vend_profile_id', 'like', '%' . $profile_id1 . '%')
                ->where('user_id', $user_id)
                ->where('status', 1)
                ->pluck('vend_profile_id')
                ->toArray();

            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('Error getting vendor profile IDs', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    public function getPartyMaster(Request $request)
    {
        try {
            // Commented out auth for testing
            // $user = Auth::guard('api')->user();
            // if (!$user) {
            //     return response()->json([
            //         'status' => 'error',
            //         'message' => 'Unauthorized access.',
            //     ], 401);
            // }

            // $org_id = $user->org_id;
            // $user_id = $user->id;
            
            // Temporary hardcoded values for testing
            $org_id = 44;
            $user_id = 1;

            // Handle KNAU to AUKN conversion
            if ($org_id == "KNAU") {
                $org_id = "AUKN";
            }

            $query = SxPartyMembers::select('id', 'name', 'phone', 'code', 'street', 'pincode')
                ->whereNotNull('code')
                ->where('code', '!=', '')
                ->where('code', '!=', '0')
                ->where('status', true);

            if ($org_id == 'RUKN' || $org_id == "AUKN") {
                $query->where('org_id', $org_id);
            } else {
                $query->where('user_id', $user_id);
            }

            $vendors = $query->groupBy('code')
                ->orderBy('id', 'DESC')
                ->get();

            $result = [];
            foreach ($vendors as $vendor) {
                $result[] = [
                    'check' => "<input type='radio' name='listvendor' id='listvendor_" . $vendor->code . "' class='listvendor' onchange='selectPartyMaster(" . $vendor->id . ")' value='" . $vendor->code . "'>",
                    'name' => $vendor->name,
                    'mobile' => $vendor->phone,
                    'address' => $vendor->street,
                    'pincode' => $vendor->pincode,
                ];
            }

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting party master data', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    public function saveVendor(Request $request, $id = null)
    {
        try {
            $validator = Validator::make($request->all(), [
                // 'code' => 'required|string',
                // 'party_id' => 'required|integer',
                'venprid' => 'nullable|integer',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Validation failed.',
                    'errors' => $validator->errors(),
                ], 422);
            }

            $vendor_p = [
                'profile_id' => $request->input('code', ''),
                'party_id' => $request->input('party_id', 0),
            ];

            if ($id === null) {
                $vendor_p['vp_id'] = $request->input('venprid', 0);
                $vendor_p['status'] = 1;
                $vendor_p['created_at'] = now();
                $vendor_p['updated_at'] = now();
                
                $vendorProfileList = VendorProfileList::create($vendor_p);
                $data['id'] = $vendorProfileList->id;
            } else {
                $vendor_p['updated_at'] = now();
                VendorProfileList::where('id', $id)->update($vendor_p);
                $data['id'] = $id;
            }

            return response()->json($data);
        } catch (\Exception $e) {
            Log::error('Error saving vendor profile list', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
                'id' => $id,
            ]);
            return response()->json(['error' => 'Failed to save vendor profile list'], 500);
        }
    }

    public function showProfileList(Request $request)
    {
        try {
            $ids = $request->input('vendor_ids_list', '');
            
            if (empty($ids)) {
                return response()->json([]);
            }

            $lists = VendorProfileList::getProfileListData($ids);
            $profilelist = [];

            foreach ($lists as $list) {
                $party_id = '"' . $list['party_id'] . '"';
                $name = '"' . $list['name'] . '"';
                $mobile = '"' . $list['phone'] . '"';
                $address = '"' . $list['street'] . '"';
                $pincode = '"' . $list['pincode'] . '"';
                $code = '"' . $list['code'] . '"';
                
                $list['action'] = "<ul class='nav nav-tabs'><li class='dropdown'> <a class='dropdown-toggle' data-toggle='dropdown' href='#''><span class='icon  tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" . 
                    "<li><a id='bEdit' type='button' class='btn btn-sm btn-default'  onclick='rowvendorEdit(this," . $list['vendor_profile_list_id'] . "," . $list['party_id'] . ");'><span class='glyphicon glyphicon-pencil' ></span>Edit</a></li>" .
                    "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='rowvendorElim(this," . $list['vendor_profile_list_id'] . ");'><span class='glyphicon glyphicon-trash' > </span>Remove</a></li></li>" .
                    "<li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='rowVenProfile(this);'><span class='glyphicon glyphicon-plus' > </span>Add Profile</a></li>";
                
                $profilelist[] = $list;
            }

            return response()->json($profilelist);
        } catch (\Exception $e) {
            Log::error('Error showing profile list', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    public function getProfileList(Request $request)
    {
        try {
            $venprid = $request->input('venprid', '');
            
            if (empty($venprid)) {
                return response()->json([]);
            }

            // Temporary hardcoded values for testing
            $org_id = 44;
            $be_value = 1;

            $profilelist = VendorProfileList::getProfileListById($venprid, $org_id, $be_value);
            $profile_list = [];

            foreach ($profilelist as $profile) {
                $party_id = '"' . $profile['party_id'] . '"';
                $name = '"' . $profile['name'] . '"';
                $mobile = '"' . $profile['phone'] . '"';
                $address = '"' . $profile['street'] . '"';
                $pincode = '"' . $profile['pincode'] . '"';
                $code = '"' . $profile['code'] . '"';
                
                $profile['action'] = "<ul class='nav nav-tabs'><li class='dropdown'> <a class='dropdown-toggle' data-toggle='dropdown' href='#''><span class='icon  tru-icon-action-setting'></span></a><ul class='dropdown-menu' role='menu'>" . 
                    "<li><a id='bEdit' type='button' class='btn btn-sm btn-default'  onclick='rowvendorEdit(this," . $profile['vendor_profile_list_id'] . "," . $party_id . ");'><span class='glyphicon glyphicon-pencil' ></span>Edit</a></li>" .
                    "<li><a id='bElim' type='button' class='btn btn-sm btn-default' onclick='rowvendorElim(this," . $profile['vendor_profile_list_id'] . ");'><span class='glyphicon glyphicon-trash' > </span>Remove</a></li></li>" .
                    "<li><a id='bAdd' type='button' class='btn btn-sm btn-default' onclick='rowVenProfile(this);'><span class='glyphicon glyphicon-plus' > </span>Add Profile</a></li>";
                
                $profile_list[] = [
                    'vend_id' => $profile['vendor_profile_list_id'],
                    'name' => $profile['name'],
                    'mobile' => $profile['phone'],
                    'address' => $profile['street'],
                    'pincode' => $profile['pincode'],
                    'action' => $profile['action']
                ];
            }

            return response()->json($profile_list);
        } catch (\Exception $e) {
            Log::error('Error getting profile list', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json([], 500);
        }
    }

    public function deleteProfileDetails(Request $request)
    {
        try {
            $id = $request->input('id', '0');
            
            if ($id == '0') {
                return response()->json('0');
            }

            $vendorProfileList = VendorProfileList::select('id', 'profile_id')
                ->where('id', $id)
                ->first();

            if (!$vendorProfileList) {
                return response()->json('0');
            }

            $result = VendorProfileList::where('id', $id)
                ->update(['status' => 0]);

            return response()->json($result ? '1' : '0');
        } catch (\Exception $e) {
            Log::error('Error deleting profile details', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json('0');
        }
    }

    public function getVendorInfo($id)
    {
        try {
            if ($id <= 0) {
                return response()->json([]);
            }

            $vendor = SxPartyMembers::select('id', 'name', 'phone', 'code', 'street', 'pincode')
                ->where('id', $id)
                ->first();

            if (!$vendor) {
                return response()->json([]);
            }

            $result = [
                'id' => $vendor->id,
                'name' => $vendor->name,
                'mobile' => $vendor->phone,
                'address' => $vendor->street,
                'pincode' => $vendor->pincode,
                'code' => $vendor->code
            ];

            return response()->json($result);
        } catch (\Exception $e) {
            Log::error('Error getting vendor info', [
                'error' => $e->getMessage(),
                'id' => $id,
            ]);
            return response()->json([]);
        }
    }

    public function checkVendorName(Request $request)
    {
        try {
            $name = $request->input('name', '');
            $id = $request->input('id', '');
            $org_id = $request->input('org_id', '');
            $be_value = $request->input('be_value', '');

            // Temporary hardcoded values for testing
            $user_id = 1;

            $query = VendorProfile::where('name', $name)
                ->where('org_id', $org_id)
                ->where('be_value', $be_value)
                ->where('user_id', $user_id)
                ->where('status', 1);

            if (!empty($id)) {
                $query->where('id', '!=', $id);
            }

            $exists = $query->exists();

            return response()->json($exists ? '1' : '2');
        } catch (\Exception $e) {
            Log::error('Error checking vendor name', [
                'error' => $e->getMessage(),
                'request' => $request->all(),
            ]);
            return response()->json('1');
        }
    }

    private function generateVendorProfileId(): string
    {
        // Temporary hardcoded values for testing
        $org_id = 44;
        
        $year = date('y');
        $week = date('W');
        $country_code = substr($org_id, 0, 2);
        
        $lastVendor = VendorProfile::orderBy('id', 'DESC')->first();
        
        if ($lastVendor) {
            $get_vend_id = $lastVendor->vend_profile_id;
            $get_vend_id_c = strlen($get_vend_id);
            $previous_weeknumber = substr($get_vend_id, 6, 2);
            $get_vend_id1 = substr($get_vend_id, 8);
            $get_vend_id2 = ltrim($get_vend_id1, '0');
            
            if ($previous_weeknumber < $week) {
                $id1 = '0001';
            } else {
                $i_id = $get_vend_id2;
                $i_id++;
                $idlength = strlen($i_id);
                if ($idlength == '1') {
                    $id1 = "000" . $i_id;
                } else if ($idlength == '2') {
                    $id1 = "00" . $i_id;
                } else if ($idlength == '3') {
                    $id1 = "0" . $i_id;
                } else if ($idlength == '4') {
                    $id1 = $i_id;
                } else {
                    $id1 = $i_id;
                }
            }
            $vend_id1 = "VN" . $country_code . $year . $week . $id1;
            
            $chk = VendorProfile::where('vend_profile_id', $vend_id1)->exists();
            if ($chk) {
                $iid = $id1;
                $iid++;
                $ii_d = $iid;
                $id_length = strlen($iid);
                if ($id_length == '1') {
                    $ii_d = "000" . $iid;
                } else if ($id_length == '2') {
                    $ii_d = "00" . $iid;
                } else if ($id_length == '3') {
                    $ii_d = "0" . $iid;
                } else if ($id_length == '4') {
                    $ii_d = $iid;
                } else {
                    $ii_d = $iid;
                }
                $vend_id1 = "VN" . $country_code . $year . $week . $ii_d;
            }
        } else {
            $i_id = 1;
            $id1 = '000' . $i_id;
            $vend_id1 = "VN" . $country_code . $year . $week . $id1;
        }
        
        return $vend_id1;
    }
}
