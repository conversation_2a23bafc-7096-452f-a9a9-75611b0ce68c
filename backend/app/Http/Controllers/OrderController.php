<?php

namespace App\Http\Controllers;

use App\Models\CountryMaster;
use App\Models\OrderType;
use App\Models\SxPartyMembers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use App\Models\SxPartyTypes;
use App\Models\ChargeCode;
use App\Models\VatCategory;
use App\Models\CostCenter;
use App\Models\TransportMode;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\UniqueOrderId;
use App\Models\User;
use App\Models\LocationData;
use Illuminate\Support\Facades\Cache;
use DateTime;
use DateTimeZone;
use Exception;
use Illuminate\Support\Facades\Http;
use App\Models\OrderpartyAddress;
use App\Models\ShipUnitType;
use App\Models\OrderCargodetail;
use App\Models\CargoDetail;
use App\Models\OrderParty;
use App\Models\OrderReference;
use App\Models\OrderFileLineIdentifier;
use App\Models\Item;
use App\Models\Revenue;
use App\Models\SxGeocode;
use App\Models\RoutingAuto;
use App\Models\VroOrder;
use Carbon\Carbon;


use App\Models\ReferenceMaster;
use App\Models\StopStatus;
use App\Services\OrderList\AdvancedSearch;
use App\Services\OrderList\ExcelUpload;
use App\Services\OrderList\Properties;
use App\Services\OrderList\Search;
use App\Services\Pagination\GetArgumentsResolver;
use App\Services\Pagination\Pagination;
use App\Services\Pagination\PaginationResponse;
use App\Services\Pagination\QueryPaginator;

class OrderController extends Controller
{

    public function index(Request $request, $id = null)
    {
        if (Auth::user()->business_type === 'Carrier') {
            return redirect()->route('logout');
        }

        return $this->orderslist($request, $id);
    }

    public function orderslist(Request $request, $id = null)
    {
        $user = Auth::user();
        $sessionData = [
            'user_id' => $user->id ?? $request->input('user_id', 0),
            'cust_id' => $user->cust_id ?? $request->input('cust_id', 0),
            'country_user_ids' => $user->country_user_ids ?? $request->input('country_user_ids', []),
            'be_value' => $user->be_value ?? $request->input('be_value', 0),
            'org_id' => $user->org_id ?? $request->input('org_id', 0),
            'user_role_id' => $user->user_role_id ?? $request->input('user_role_id', 0),
            'sub_cust' => $user->sub_cust ?? $request->input('sub_cust', []),
            'curtz' => $user->usr_tzone['timezone'] ?? 'UTC',
        ];

        $data = [
            'postData' => $request->all(),
        ];

        $getArguments = [];
        $properties = new Properties();
        $order = [];
        $excel_uploaddata = $clexcel_uploaddata = $chexcel_uploaddata = $knlogin_uploaddata = [];
        $ats_parties = [];
        $whr = $conditions = [];

        if (in_array($id, ExcelUpload::IDS_FOR_UPLOAD)) {
            $excel = new ExcelUpload($id);
            $result = $excel->upload($sessionData['org_id']);

            if (!empty($result['booking_ids'])) {
                $properties->getOrderBookings($result['booking_ids'], $ats_parties);
                $data['ats_parties'] = !empty($ats_parties) ? json_encode($ats_parties, JSON_HEX_TAG) : [];
            }

            $list_type = $result['list_types']['list_type'] ?? 0;
            $cllist_type = $result['list_types']['cllist_type'] ?? 0;
            $charge_list_type = $result['list_types']['charge_list_type'] ?? 0;
            $knlogin_list_type = $result['list_types']['knlogin_list_type'] ?? 0;
        } elseif ($id) {
            $getArguments['bookingid'] = $properties->getBooking($id, $ats_parties);
            $data['getbookingid'] = $getArguments['bookingid'];
            $data['ats_parties'] = !empty($ats_parties) ? json_encode($ats_parties, JSON_HEX_TAG) : [];
        }

        $getArguments = (new GetArgumentsResolver())->resolve(
            $this->getDefaultFilters(),
            $getArguments,
            ['page', 'limit']
        );

        if (isset($getArguments['search_type']) && $getArguments['search_type'] === 'advanced') {
            $search = new AdvancedSearch($getArguments);
            $conditions = $search->buildWhereClause($request, $sessionData['user_role_id'], $sessionData['user_id'], $sessionData['org_id']);
        } else {
            $search = new Search($getArguments);
        }

        $field = $properties->getDateFieldName($getArguments);
        $search->fromDate($field, $whr);
        $search->toDate($field, $whr);

        $this->addDateRangeFilter($whr, $getArguments, 'advpickupfrom_date', 'advpickupto_date', 'pickup_datetime');
        $this->addDateRangeFilter($whr, $getArguments, 'advdeliveryfrom_date', 'advdeliveryto_date', 'delivery_datetime');

        $whr = array_merge($whr, $conditions);

        $status_search = $getArguments['status'] ?? $getArguments['order_status'] ?? '';

        $searchids = $getArguments['bookingid'] ?? [];
        if (empty($searchids) && !empty($getArguments['container_no'])) {
            $searchids = $this->getContainerNum($sessionData['user_id'], $sessionData['country_user_ids'], $sessionData['be_value']);
        } elseif (!empty($searchids) && !empty($getArguments['container_no'])) {
            $container_num_arr = $this->getContainerNum($sessionData['user_id'], $sessionData['country_user_ids'], $sessionData['be_value']);
            $searchids = array_intersect($container_num_arr, $searchids);
        }

        if (!empty($getArguments['order_reftype'])) {
            $advancedids = $this->getRefNum($sessionData['user_id'], $getArguments);
            $searchids = !empty($advancedids) ? $advancedids : [0];
        }

        if (!empty($getArguments['salog_ref'])) {
            $advancedids = $this->getSalogRefNum($sessionData['user_id'], $getArguments, $sessionData['org_id']);
            $searchids = !empty($advancedids) ? $advancedids : [0];
        }

        $wildcard_search = $getArguments['wildcard_order_id'] ?? '';
        if (!empty($getArguments['order_id'])) {
            $searchids = [$getArguments['order_id']];
        }

        if (!empty($getArguments['order_references'])) {
            $referenceResults = $this->getOrderIdsByReferenceValues($getArguments['order_references']);
            $searchids = empty($referenceResults) ? [0] : $referenceResults;
        }

        $orderIdsMap = $properties->getOrderIdsMapping(
            $wildcard_search,
            $searchids,
            $sessionData['org_id'],
            $sessionData['user_id'],
            $sessionData['country_user_ids']
        );

        $subcusts = $sessionData['sub_cust'];
        if ($sessionData['user_role_id'] == '4' && $subcusts && !empty($subcusts)) {
            $subcusts[] = $sessionData['cust_id'];
        }

        $orderdataQuery = $this->indexQuery(
            $sessionData['user_id'],
            $searchids,
            $status_search,
            $sessionData['cust_id'],
            $sessionData['country_user_ids'],
            $whr,
            $subcusts,
            $orderIdsMap
        );

        DB::statement('SET SESSION group_concat_max_len = 1000000');

        $limit = $request->query('limit', 10);
        $limit = $limit === 'All' ? 5000 : (int)$limit;
        $page = (int)$request->query('page', 1);

        $pagination = new Pagination($orderdataQuery, $limit, $page, $getArguments);
        $paginationResponse = $pagination->paginate();

        if ($paginationResponse->hasItems() && !$request->query('debug')) {
            $this->processOrderItems($paginationResponse, $properties, $sessionData);
        }

        $data = array_merge($data, [
            'list_type' => $list_type ?? 0,
            'excel_uploaddata' => $excel_uploaddata,
            'bill_type' => 'ShipmentX',
            'charge_list_type' => $charge_list_type ?? 0,
            'chexcel_uploaddata' => $chexcel_uploaddata,
            'cllist_type' => $cllist_type ?? 0,
            'knlogin_uploaddata' => $knlogin_uploaddata,
            'knlogin_list_type' => $knlogin_list_type ?? 0,
            'clexcel_uploaddata' => $clexcel_uploaddata,
            'bill_types' => [],
            'ref_names_arr' => $this->getRefNums(),
            'user_currency' => $user->usr_tzone['currency'] ?? 'USD',
            'currencies' => $this->getCurrencies(),
            'pagination' => $paginationResponse,
        ]);

        return view('orders.order', $data);
    }

    protected function getCurrencies()
    {
        return CountryMaster::where('status', 1)
            ->pluck('currency')
            ->toArray();
    }

    protected function getRefNums()
    {
        return ReferenceMaster::select('name')
            ->distinct()
            ->where('status', 1)
            ->orderBy('name', 'ASC')
            ->pluck('name')
            ->toArray();
    }

    protected function addDateRangeFilter(&$whr, $getArguments, $fromKey, $toKey, $dateField)
    {
        if (!empty($getArguments[$fromKey])) {
            $whr["DATE($dateField) >="] = date('Y-m-d', strtotime($getArguments[$fromKey]));
        }

        if (!empty($getArguments[$toKey])) {
            $whr["DATE($dateField) <="] = date('Y-m-d', strtotime($getArguments[$toKey]));
        }
    }

    protected function getContainerNum($user_id, $country_user_ids, $be_value)
    {
        $post = request()->all();
        $res = [];

        if (isset($post['container_no']) && $post['container_no'] !== '') {
            $ref_val = trim($post['container_no']);
            $refid = $be_value === 'INCL' ? 'AWB' : 'CTR';

            $result = OrderReference::select('order_references.order_id')
                ->join('orders', 'order_references.order_id', '=', 'orders.id')
                ->where('order_references.reference_id', $refid)
                ->where('order_references.status', 1)
                ->where('orders.status', '!=', 0)
                ->where('orders.user_id', $user_id)
                ->where('orders.be_value', $be_value)
                ->where('order_references.ref_value', 'like', "%{$ref_val}%")
                ->when($country_user_ids, function ($query) use ($country_user_ids) {
                    return $query->whereIn('orders.user_id', $country_user_ids);
                })
                ->get();

            if ($result->isNotEmpty()) {
                $res = $result->pluck('order_id')->toArray();
            }
        }

        return $res;
    }

    protected function getRefNum($user_id, $post)
    {
        $res = [];

        if (isset($post['order_reftype']) && $post['order_reftype'] !== '' && isset($post['ref_val']) && $post['ref_val'] !== '') {
            $ref_id = trim($post['order_reftype']);
            $ref_val = trim($post['ref_val']);

            $result = OrderReference::select('order_references.order_id')
                ->join('orders', 'order_references.order_id', '=', 'orders.id')
                ->where('order_references.reference_id', $ref_id)
                ->where('order_references.status', 1)
                ->where('orders.status', '!=', 0)
                ->where('orders.user_id', $user_id)
                ->where('order_references.ref_value', 'like', "%{$ref_val}%")
                ->get();

            if ($result->isNotEmpty()) {
                $res = $result->pluck('order_id')->toArray();
            }
        }

        return $res;
    }

    protected function getSalogRefNum(string $userId, array $post, string $orgId): array
    {
        $res = [];
        $referenceId = 'XSR';
        $referenceVal = trim($post['salog_ref']);

        $result = OrderReference::select('order_references.order_id')
            ->join('orders', 'order_references.order_id', '=', 'orders.id')
            ->where('order_references.reference_id', $referenceId)
            ->where('order_references.status', 1)
            ->where('orders.status', '!=', 0)
            ->where('orders.user_id', $userId)
            ->where('orders.org_id', $orgId)
            ->where('order_references.ref_value', 'like', "%{$referenceVal}%")
            ->get();

        if ($result->isNotEmpty()) {
            $res = $result->pluck('order_id')->toArray();
        }

        return $res;
    }

    protected function getOrderIdsByReferenceValues(array $referenceValues): array
    {
        if (empty($referenceValues)) {
            return [];
        }

        $result = OrderReference::select('order_references.order_id')
            ->join('orders', 'order_references.order_id', '=', 'orders.id')
            ->whereIn('order_references.reference_id', ['DQ', 'XSR', 'Journey ID'])
            ->whereIn('order_references.ref_value', $referenceValues)
            ->where('order_references.status', 1)
            ->where('orders.status', '!=', 0)
            ->get();

        return $result->isNotEmpty() ? $result->pluck('order_id')->toArray() : [];
    }

    protected function indexQuery($userid, $searchids, $searchsts, $custid, $countryuids, $whr, $subcusts, $mapping = null)
    {
        $orderIds = $mapping && !empty($mapping) ? $mapping : $searchids;

        $tbReveneusJoin = Revenue::select('order_id', DB::raw("json_agg(json_build_object(
            'type', type,
            'invoice_status', invoice_status,
            'recipient_name', recipient_name,
            'amount', concat(currency, ' ', amount),
            'debtor_jfr', debtor_jfr
        ) ORDER BY type DESC) as invoice_details"))
            ->where('status', 1)
            ->groupBy('order_id');

        $query = Order::select([
            'users.name as username',
            'orders.pickup_partyid',
            DB::raw("(CASE
                WHEN orders.vendor_id > 0 THEN vendors.name
                WHEN orders.vendor_id = 0 AND orders.shift_id > 0 THEN (SELECT name FROM vendors WHERE id = (SELECT vendor_id FROM shifts WHERE id = orders.shift_id LIMIT 1))
                ELSE ''
            END) AS vendor_name"),
            DB::raw('invoice_inner.invoice_details as invoice_details'),
            'order_details.order_status as order_detail_status',
            'orders.id',
            'orders.order_id',
            'orders.pickup_datetime',
            'orders.delivery_datetime',
            'orders.drop_endtime',
            'orders.pickup_company as pickup',
            'orders.delivery_company as delivery',
            'orders.delivery_city',
            'orders.weight',
            'orders.gross_weight_uom',
            'orders.chargeable_weight',
            'orders.chargeable_weight_uom',
            'orders.transport_mode',
            'orders.vendor_id',
            'orders.org_id',
            'orders.be_value',
            'orders.shipment_id',
            'orders.status',
            'orders.order_status',
            'orders.Stoppage',
            'orders.trip_sts',
            'orders.shift_id',
            'orders.trip_id',
            'orders.shipmentid as shipmentid',
            'orders.createdon',
            'orders.updatedon',
            'orders.created_source',
            'orders.parent_id',
            'orders.category_id',
            'order_details.delivery_note',
            'order_details.order_type',
            'order_details.cost_center_id',
            'stop_status.createdon as delivered_time',
            DB::raw("(SELECT json_agg(json_build_object(reference_id, ref_value))
                FROM order_references
                WHERE reference_id IN ('DQ','CTR','MN','LPC','XSR','POS','ORD','PO','AWB','PQ','Journey ID','Request Source', 'Job ID')
                AND status = 1
                AND order_id = orders.id) as reference_value"),
            'customers.name as customer_name',
            DB::raw("(CASE WHEN (SELECT count(*) FROM pod_uploads WHERE order_id = orders.id) > 0 THEN 'YES' ELSE 'NO' END) as pod"),
            'order_creation_source_masters.source_name',
            DB::raw("array_agg(cargo_details.weight_unit) as weight_unit"),
            'cargo_details.secondweight_uom',
            'cargo_details.secondvolume_uom',
            DB::raw("SUM(CASE WHEN order_cargodetails.status = '1' THEN order_cargodetails.second_weight ELSE 0 END) as second_weight"),
            DB::raw("SUM(CASE WHEN order_cargodetails.status = '1' THEN order_cargodetails.second_volume ELSE 0 END) as second_volume"),
            DB::raw("TRUNCATE(SUM(CASE WHEN order_cargodetails.status = '1' THEN
                (CASE WHEN cargo_details.weight_unit IN ('G', 'Gms', 'gms', 'grm') THEN order_cargodetails.weight / 1000
                    ELSE (CASE WHEN cargo_details.weight_unit IN ('Kg', 'kg') THEN order_cargodetails.weight
                        ELSE (CASE WHEN cargo_details.weight_unit IN ('Tons', 'tons') THEN order_cargodetails.weight * 1000
                            ELSE CASE WHEN orders.org_id = 'AUKN' THEN order_cargodetails.weight ELSE 0 END
                        END)
                    END)
                END)
                ELSE 0 END), 3) as totwg"),
            DB::raw("SUM(CASE WHEN order_cargodetails.status = '1' THEN order_cargodetails.volume ELSE 0 END) as totvol"),
            DB::raw("SUM(CASE WHEN order_cargodetails.status = '1' THEN order_cargodetails.quantity ELSE 0 END) as totqty"),
            DB::raw("SUM(CASE WHEN order_cargodetails.status = '1' THEN order_cargodetails.scanned_quantity ELSE 0 END) as tot_scanned_qty"),
            'order_details.num_of_pallets',
        ])
            ->from('orders')
            ->leftJoin('order_details', function ($join) {
                $join->on('orders.id', '=', 'order_details.order_row_id')
                    ->on('orders.order_id', '=', 'order_details.order_id');
            })
            ->leftJoin('users', 'users.id', '=', 'orders.user_id')
            ->leftJoin('order_cargodetails', 'orders.id', '=', 'order_cargodetails.order_id')
            ->leftJoin('cargo_details', 'order_cargodetails.cargo_id', '=', 'cargo_details.id')
            ->leftJoin('stop_status', function ($join) {
                $join->on('orders.id', '=', 'stop_status.order_id')
                    ->where('stop_status.status_code', '3000')
                    ->where('stop_status.status', '1');
            })
            ->leftJoin('vendors', 'vendors.id', '=', 'orders.vendor_id')
            ->leftJoin('customers', 'customers.id', '=', 'orders.customer_id')
            ->leftJoin('order_creation_source_masters', 'order_creation_source_masters.source_id', '=', 'orders.created_source')
            ->leftJoinSub($tbReveneusJoin, 'invoice_inner', 'orders.id', '=', 'invoice_inner.order_id');

        if ($searchsts === 'SCANNED') {
            $query->leftJoin('order_status', function ($join) {
                $join->on('orders.id', '=', 'order_status.order_id')
                    ->where('order_status.status_code', '2491');
            });
        }

        if (!in_array($searchsts, ['CLOSED', 'SALOG CLOSED'])) {
            $query->where('orders.status', '!=', 0);
        }

        if (!empty($orderIds)) {
            $query->whereIn('orders.order_id', $orderIds);
            unset($whr['orders.order_id']);
        }

        if (!empty($countryuids)) {
            $query->whereIn('orders.user_id', $countryuids);
        } else {
            $query->where('orders.user_id', $userid);
        }

        if ($searchsts === 'PENDING' || $searchsts === 'SALOG PENDING') {
            if ($sessionData['org_id'] === 'AUKN') {
                $query->whereRaw('(orders.id NOT IN (SELECT order_id FROM order_status WHERE order_id = orders.id AND status_code = \'2491\'))');
            }
            $query->whereRaw('(orders.trip_id = 0 AND (SELECT COUNT(trip_id) FROM employees WHERE order_id = orders.id) = 0)')
                ->where('orders.trip_id', 0)
                ->where('orders.trip_sts', 0)
                ->where('orders.status', '!=', 3);
        }

        if (in_array($searchsts, ['SALOG PENDING', 'SALOG ACTIVE', 'SALOG CLOSED', 'SALOG TO BE BILLED'])) {
            $query->where('orders.created_source', '5');
        }

        if ($searchsts === 'ACTIVE' || $searchsts === 'SALOG ACTIVE') {
            $query->where('orders.trip_id', '!=', 0)
                ->whereRaw('(((SELECT COUNT(trip_id) FROM employees WHERE order_id = orders.id) > 0) OR (orders.trip_id != 0 AND (SELECT COUNT(trip_id) FROM employees WHERE order_id = orders.id) = 0))')
                ->where('orders.trip_sts', 0)
                ->where('orders.status', '!=', 3);
        } elseif ($searchsts === 'CLOSED' || $searchsts === 'SALOG CLOSED') {
            $query->whereRaw('(((SELECT COUNT(trip_id) FROM employees WHERE order_id = orders.id) > 0) OR (orders.trip_id != 0 AND (SELECT COUNT(trip_id) FROM employees WHERE order_id = orders.id) = 0))')
                ->where('orders.trip_sts', 1)
                ->where('orders.status', '!=', 3);
        } elseif ($searchsts === 'SALOG TO BE BILLED') {
            $salogorders = $this->getSalogToBeBilled($countryuids, $userid, $subcusts, $custid);
            if (!empty($salogorders)) {
                $query->whereIn('orders.order_id', $salogorders);
            } else {
                $query->where('orders.id', 0);
            }
            $query->where('orders.status', '!=', 3);
        } elseif ($searchsts === 'SALOG CANCELLED' || $searchsts === 'CANCELLED') {
            $query->where('orders.status', 3);
        }

        if (!empty($subcusts)) {
            $query->whereIn('orders.customer_id', $subcusts);
        } elseif (!empty($custid)) {
            $query->where('orders.customer_id', $custid);
        }

        if (isset($whr['orders.customer_id'])) {
            $query->whereIn('orders.customer_id', explode(',', $whr['orders.customer_id']));
            unset($whr['orders.customer_id']);
        }

        if (isset($whr['orders.delivery_company'])) {
            $query->where('orders.delivery_company', 'like', "%{$whr['orders.delivery_company']}%");
            unset($whr['orders.delivery_company']);
        }

        if (isset($whr['orders.pickup_company'])) {
            $query->where('orders.pickup_company', 'like', "%{$whr['orders.pickup_company']}%");
            unset($whr['orders.pickup_company']);
        }

        $query->where($whr)
            ->groupBy('orders.id')
            ->orderBy('orders.id', 'DESC');

        return $query;
    }

    protected function getSalogToBeBilled($countryuids, $userid, $subcusts, $custid)
    {
        // Implement SALOG to be billed logic
        return [];
    }

    protected function processOrderItems(PaginationResponse $paginationResponse, Properties $properties, array $sessionData)
    {
        $shiftIds = array_column($paginationResponse->items(), 'shift_id');
        $tripAcceptancesByShiftIds = [];

        if (!empty($shiftIds)) {
            $stopStatusResults = StopStatus::selectRaw('COUNT(*) as cnt, shipment_id')
                ->whereIn('shipment_id', $shiftIds)
                ->where('status_code', '0212')
                ->where('status_id', 10)
                ->where('shipment_id', '>', 0)
                ->groupBy('shipment_id')
                ->get();

            foreach ($stopStatusResults as $stopStatus) {
                $tripAcceptancesByShiftIds[$stopStatus->shipment_id] = $stopStatus->cnt;
            }
        }

        $order = [];
        foreach ($paginationResponse->items() as $index => $res) {
            $orderData = $this->processSingleOrderItem($res, $properties, $sessionData, $tripAcceptancesByShiftIds);
            $order[] = $orderData;

            // Australia ASN check
            if ($sessionData['org_id'] === 'AUKN') {
                $asnData = $this->emptyCheckAsnOrder($res['id']);
                $order[$index]['alert_msg'] = $asnData['keys'] ?? 'No missing fields';
                $order[$index]['check_val'] = $asnData['check_val'] ?? 0;
            }
        }

        $paginationResponse->updateItems($order);
    }
    protected function getSalogToBeBilled($countryUids, $userId, $subCusts, $custId)
    {
        $query = Order::select('orders.order_id')
            ->join('revenues', 'orders.id', '=', 'revenues.order_id')
            ->where('orders.status', '!=', 0)
            ->where('orders.created_source', '5')
            ->where('revenues.recipient_role', 'LIKE', 'Internal BU')
            ->where('revenues.status', 1)
            ->where('revenues.invoice_status', '<', '3');

        if ($userId != '0') {
            if (!empty($countryUids)) {
                $query->whereIn('orders.user_id', $countryUids);
            } else {
                $query->where('orders.user_id', $userId);
            }
        }

        if (!empty($subCusts)) {
            $query->whereIn('orders.customer_id', $subCusts);
        } elseif (!empty($custId)) {
            $query->where('orders.customer_id', $custId);
        }

        $results = $query->get();

        return $results->isNotEmpty() ? $results->pluck('order_id')->toArray() : [];
    }

    protected function processSingleOrderItem($res, Properties $properties, array $sessionData, array $tripAcceptances)
    {
        // Process dates with timezone
        $dates = $this->processOrderDates($res, $sessionData['curtz']);

        // Get reference data
        $referenceData = $properties->getReferences($res);

        // Get status information
        $orderStatus = $properties->getStatusName($res);
        $otherStatus = $properties->getOtherStatus($res);

        // Get invoice information
        $invoiceArray = $properties->getInvoices($res);

        // Vendor/carrier name logic
        $vendorName = $res['customer_name'];
        $carrierName = $res['shift_id'] > 0 ? $res['vendor_name'] : '';

        // VRO orders check
        $vroId = 0;
        if ($res['id'] > 0) {
            $vroOrder = VroOrder::select('id')
                ->where('status', 1)
                ->whereRaw('? = ANY(string_to_array(order_ids, \',\')::integer[])', [$res['id']])
                ->first();

            $vroId = $vroOrder ? $vroOrder->id : 0;
        }

        // Return formatted order data
        return [
            'can_cancel' => !($tripAcceptances[$res['shift_id']] ?? 0),
            'order_row_id' => $res['id'],
            'order_id' => $res['order_id'],
            'trip_id' => $res['trip_id'] ?? 0,
            'shift_id' => $res['shift_id'] ?? 0,
            'username' => $res['username'],
            'delivery_note' => $referenceData['delivery_note'],
            'journey_id' => $referenceData['journey_id'],
            'job_id' => $referenceData['job_id'],
            'transport_direction' => $referenceData['transport_direction'],
            'pickup' => $res['pickup'],
            'delivery' => $res['delivery'],
            'trip_no' => $res['shipmentid'] == '0' ? '' : $res['shipmentid'],
            'order_status' => $orderStatus,
            'transport_mode' => $res['transport_mode'],
            'createdon' => $res['createdon'],
            'total_packages' => round(floatval($res['totqty'])),
            'weight' => $this->calculateWeight($res['totwg'], $this->getWeightHigherUnit($res['weight_unit'] ?? '')),
            'gross_weight' => $res['weight'],
            'gross_weight_uom' => $res['gross_weight_uom'],
            'chargeable_weight' => $res['chargeable_weight'],
            'chargeable_weight_uom' => $res['chargeable_weight_uom'],
            'volume' => $res['totvol'],
            'second_weight' => ($res['second_weight'] ?? 0) . ' ' . ($res['secondweight_uom'] ?? ''),
            'second_volume' => ($res['second_volume'] ?? 0) . ' ' . ($res['secondvolume_uom'] ?? ''),
            'org_id' => $res['org_id'],
            'be_value' => $res['be_value'],
            'otherstatus' => $otherStatus,
            'Stoppage' => $res['Stoppage'],
            'delivery_date' => $dates['early_delivery'],
            'pickup_date' => $dates['early_pickup'],
            'html' => $invoiceArray['html'] ?? '',
            'container_no' => $referenceData['container_no'],
            'purchase_order' => $referenceData['purchase_order'],
            'vendor_name' => $vendorName,
            'vendor_id' => $res['vendor_id'] ?? '',
            'manifestno' => $referenceData['manifestno'],
            'num_of_pallets' => $res['num_of_pallets'],
            'tot_scanned_qty' => $res['tot_scanned_qty'],
            'deliverycity' => $res['delivery_city'],
            'final_delivery' => $dates['final_delivery'],
            'loadplanconnote' => $referenceData['loadplanconnote'],
            'created_source' => $res['created_source'],
            'salog_ref' => $referenceData['salog_ref'],
            'pq_ref' => $referenceData['pq_ref'],
            'pos_ref' => $referenceData['pos_ref'],
            'weight_unit' => $this->getWeightHigherUnit($res['weight_unit'] ?? ''),
            'order_type' => $res['order_type'],
            'customer_name' => $res['customer_name'],
            'carrier_name' => $carrierName,
            'POD' => $res['pod'],
            'JFR' => $invoiceArray['finaljfr'] ?? '',
            'updatedDate' => $dates['updatedDate'],
            'source_name' => $res['source_name'],
            'category_id' => $res['category_id'],
            'vro_id' => $vroId,
        ];
    }

    protected function processOrderDates($res, $timezone)
    {
        $chkDate = Carbon::createFromFormat('Y-m-d H:i:s', '2020-07-01 00:00:00');
        $createdOn = Carbon::parse($res['createdon'] ?? '0000-00-00');

        $dates = [
            'early_pickup' => $res['pickup_datetime'],
            'early_delivery' => $res['delivery_datetime'],
            'final_delivery' => $res['delivered_time'],
            'updatedDate' => $res['updatedon'],
        ];


        if ($createdOn->greaterThan($chkDate)) {
            foreach (['early_pickup', 'early_delivery'] as $key) {
                if (!empty($dates[$key]) && $dates[$key] != '0000-00-00 00:00:00') {
                    $dates[$key] = Carbon::parse($dates[$key], 'UTC')
                        ->setTimezone($timezone)
                        ->format('Y-m-d H:i:s');
                }
            }
        }

        // Process final delivery date
        if (!empty($dates['final_delivery']) && $dates['final_delivery'] != '0000-00-00 00:00:00' && $dates['final_delivery'] != '0') {
            $dates['final_delivery'] = Carbon::parse($dates['final_delivery'], 'UTC')
                ->setTimezone($timezone)
                ->format('Y-m-d h:i A');
        }

        // Process updated date
        $dates['updatedDate'] = Carbon::parse($res['updatedon'], 'UTC')
            ->setTimezone($timezone)
            ->format('Y-m-d H:i:s');

        return $dates;
    }

    protected function calculateWeight($weight, string $weightUnit)
    {
        if ($weightUnit === 'tons') {
            return $weight / 1000;
        } elseif ($weightUnit === 'g') {
            return $weight * 1000;
        }

        return $weight;
    }

    protected function getWeightHigherUnit($weightUnit): string
    {
        $weightUnitArray = explode(',', $weightUnit);

        if (array_intersect(['Tons', 'tons'], $weightUnitArray)) {
            return 'tons';
        } elseif (array_intersect(['Kg', 'kg'], $weightUnitArray)) {
            return 'kg';
        } elseif (array_intersect(['G', 'Gms', 'gms', 'grm'], $weightUnitArray)) {
            return 'g';
        }

        return 'kg';
    }

    protected function emptyCheckAsnOrder($id)
    {
        $where = ['status' => 1, 'id' => $id];
        $whr = ['status' => 1, 'order_id' => $id];
        $keys = "missing fields:\n";
        $checkVal = 0;

        // Get order data
        $orderArray = $this->getAuAsnOrderData($where);

        // Get cargo data
        $cargoArray = $this->getAsnCargoDetails($whr);

        if (!empty($orderArray)) {
            // Get order array empty values
            $getOrderEm = array_filter($orderArray, fn($val) => empty($val));
            if (!empty($getOrderEm)) {
                $getOrderEmKeys = array_keys($getOrderEm);
                $keys .= implode("\n", $getOrderEmKeys) . "\n";
                $checkVal = 1;
            }
        }

        // Cargo empty field keys
        $cargoKeys = $this->cargoFieldsCheck($cargoArray);
        if (!empty($cargoKeys)) {
            $keys .= $cargoKeys;
            $checkVal = 1;
        }

        return ['keys' => $keys, 'check_val' => $checkVal];
    }

    protected function getAuAsnOrderData($where)
    {
        $result = Order::select([
            'id',
            'order_id',
            'pickup_company as Shipper Name',
            'delivery_company as Consignee Name',
            'pickup_country as Shipper Country',
            'delivery_country as Consignee Country',
            'pickup_city as Shipper City',
            'delivery_city as Consignee City',
            'pickup_pincode as Shipper Zipcode',
            'delivery_pincode as Consignee Zipcode',
            'pickup_address1 as Shipper Address',
            'delivery_address1 as Consignee Address',
            'pickup_address2 as Shipper State',
            'delivery_address2 as Consignee State',
        ])
            ->where($where)
            ->first();

        return $result ? $result->toArray() : [];
    }

    protected function getAsnCargoDetails($whr)
    {
        $result = OrderCargodetail::select(['length', 'width', 'height', 'weight', 'volume', 'quantity'])
            ->where($whr)
            ->get();

        return $result->toArray();
    }

    protected function cargoFieldsCheck($cargoArray)
    {
        $keys = '';
        $cargoEmptyVal = [];

        if (!empty($cargoArray)) {
            foreach ($cargoArray as $cargo) {
                $emptyFields = array_filter($cargo, fn($val) => empty($val) || $val <= 0);
                if (!empty($emptyFields)) {
                    $cargoEmptyVal[] = array_keys($emptyFields);
                }
            }

            if (!empty($cargoEmptyVal)) {
                $singleArray = array_merge(...$cargoEmptyVal);
                $cargoResult = array_unique($singleArray);
                $keys .= implode("\n", $cargoResult);
            }
        }

        return $keys;
    }

    protected function getDefaultFilters(): array
    {
        return [];
    }

    public function neworder(Request $request)
    {

        // if (Auth::user()->business_type === 'Carrier') {
        //     return response()->json([
        //         'status' => 'error',
        //         'message' => 'Unauthorized access for Carrier',
        //         'data' => ['status' => 0]
        //     ], 403);
        // }
        $post = $request->all() ?? [];
        $user = Auth::user();
        $user_id = $user->id ?? $post['user_id'] ?? 0;
        $org_id = $user->org_id ?? $post['org_id'] ?? 0;
        $be_value = $user->be_value ?? $post['be_value'] ?? 0;

        $currencies = $user->usr_tzone['currency'] ?? [];
        $country_masters = CountryMaster::where('status', 1)->pluck('currency')->toArray();
        $currencies = array_unique(array_merge($currencies, $country_masters));

        $transportMode = new TransportMode();
        $transport = $transportMode->getTransportMode($request->input('order_date', ''), $request->input('less_date', ''), $org_id, $be_value);

        $pickup_details = [];
        $cust_id = $user->cust_id ?? null;
        if ($cust_id) {
            $pickupResponse = $this->getPickupDetails($cust_id);
            $pickup_details = $pickupResponse->getData()->data; // Extract data from JSON response
        }

        // Get order types
        $ordertypes = [];
        if ($cust_id) {
            $ordertypes = OrderType::where('customer_id', $cust_id)
                ->where('org_id', $org_id)
                ->where('status', 1)
                ->select('id', 'type_name')
                ->groupBy('type_name')
                ->get()
                ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                ->toArray();

            if (empty($ordertypes)) {
                $ordertypes = OrderType::where('org_id', $org_id)
                    ->where('status', 1)
                    ->select('id', 'type_name')
                    ->groupBy('type_name')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();

                if (empty($ordertypes)) {
                    $ordertypes = OrderType::where('org_id', 'SGKN')
                        ->where('status', 1)
                        ->select('id', 'type_name')
                        ->groupBy('type_name')
                        ->get()
                        ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                        ->toArray();
                }
            }
        } else {
            $branch_exists = OrderType::where('status', 1)
                ->where('be_value', $be_value)
                ->where('org_id', $org_id)
                ->exists();

            if ($branch_exists) {
                $ordertypes = OrderType::where('status', 1)->where('org_id', $org_id)
                    ->select('id', 'type_name')
                    ->groupBy('type_name', 'id')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();
            } else {
                $ordertypes = OrderType::where('status', 1)
                    ->where('org_id', $org_id)
                    ->select('id', 'type_name')
                    ->groupBy('type_name', 'id')
                    ->get()
                    ->map(fn($order) => ['type_id' => $order->id, 'type_name' => $order->type_name])
                    ->toArray();
            }
        }

        $roles = SxPartyTypes::where('org_id', $org_id)
            ->where('status', 1)
            ->select('id', 'type_name')
            ->groupBy('id', 'type_name')
            ->get()
            ->map(fn($role) => ['id' => $role->id, 'name' => $role->name])
            ->toArray();

        // Get charge codes
        $chargecodes = ChargeCode::where('status', 1)
            ->select('id', 'charge_code')
            ->get()
            ->map(fn($charge) => ['charge_id' => $charge->id, 'charge_code' => $charge->charge_code])
            ->toArray();

        // Get VAT categories
        $vatcategory = VatCategory::where('org_id', $org_id)
            ->where('status', 1)
            ->select('id', 'description', 'vat_category', 'vat_percentage')
            ->get()
            ->map(fn($vat) => [
                'id' => $vat->id,
                'val' => "{$vat->id}_{$vat->vat_category}",
                'desc' => "{$vat->description} ({$vat->vat_category}-{$vat->vat_percentage})",
            ])
            ->toArray();

        // Get cost centers
        $costcenter = CostCenter::where('org_id', $org_id)
            ->where('status', 1)
            ->select('id', 'type_name')
            ->groupBy('type_name', 'id')
            ->get()
            ->map(fn($cost) => ['type_id' => $cost->id, 'type_name' => $cost->type_name])
            ->toArray();

        // Prepare response data
        $data = [
            'currencies' => $currencies,
            'transport' => $transport,
            'org_id' => $org_id,
            'be_value' => $be_value,
            'chargecodes' => $chargecodes,
            'pickup_details' => $pickup_details,
            'ordertypes' => $ordertypes,
            'costcenter' => $costcenter,
            'roles' => $roles,
            'vatcategory' => $vatcategory,
            'marks_numbers_column' => true,
        ];

        return response()->json([
            'status' => 'success',
            'message' => 'Order data retrieved successfully',
            'data' => $data
        ], 200);
    }

    public function getPickupDetails($id)
    {
        $pickup = SxPartyMembers::where('status', 1)
            ->where('id', $id)
            ->select('id', 'name', 'address', 'pincode', 'code', 'country')
            ->first();

        return response()->json([
            'status' => 'success',
            'message' => 'Pickup details retrieved successfully',
            'data' => $pickup ? [
                'id' => $pickup->id,
                'name' => $pickup->name,
                'party_id' => $pickup->code,
                'address' => $pickup->address,
                'pincode' => $pickup->pincode,
                'country' => $pickup->country,
            ] : []
        ], 200);
    }

    public function insertorder(Request $request)
    {
        $cdate = now()->format('Y-m-d H:i:s');
        $user = Auth::user();
        $user_id = $user->id ?? $request->input('user_id', 0);
        $org_id = $user->org_id ?? $request->input('org_id', 0);
        $be_value = $user->be_value ?? $request->input('be_value', 0);
        $curtz = $user->usr_tzone['timezone'] ?? 'UTC';

        // Validate request data
        // $validated = $request->validate([
        //     'product' => 'nullable|string|max:255',
        //     'service' => 'nullable|string|max:50',
        //     'order_shipper_id' => 'nullable|string|max:50',
        //     'delivery_terms' => 'nullable|string|max:50',
        //     'incoterm' => 'nullable|string|max:50',
        //     'delivery_note' => 'nullable|string|max:50',
        //     'container_num' => 'nullable|string|max:50',
        //     'purchase_order' => 'nullable|string|max:50',
        //     'notify_party' => 'nullable|string|max:50',
        //     'currency' => 'nullable|string|max:3',
        //     'goods_value' => 'nullable|numeric|min:0',
        //     'external_order_id' => 'nullable|string|max:50',
        //     'shipment_type' => 'nullable|integer',
        //     'region' => 'nullable|string|max:50',
        //     'p_latitude' => 'nullable|numeric',
        //     'p_longitude' => 'nullable|numeric',
        //     'd_latitude' => 'nullable|numeric',
        //     'd_longitude' => 'nullable|numeric',
        //     'order_party_row_id' => 'nullable|string|max:50',
        //     'order_inv_row_id' => 'nullable|string|max:50',
        //     'order_cargo_id' => 'nullable|string|max:50',
        //     'order_pickup_id' => 'nullable|string|max:50',
        //     'order_drop_id' => 'nullable|string|max:50',
        //     'early_pickup' => 'nullable|date_format:Y-m-d H:i:s',
        //     'late_pickup' => 'nullable|date_format:Y-m-d H:i:s',
        //     'early_delivery' => 'nullable|date_format:Y-m-d H:i:s',
        //     'late_delivery' => 'nullable|date_format:Y-m-d H:i:s',
        //     'modeof_trasnport' => 'nullable|string|max:50|default:LTL',
        //     'order_type' => 'nullable|string|max:50',
        //     'cost_center' => 'nullable|string|max:50',
        //     'rev_row_id' => 'nullable|string|max:50',
        //     'ordcost_row_id' => 'nullable|string|max:50',
        //     'customer_id' => 'nullable|string|max:50',
        //     'driver_pickup_instructions' => 'nullable|string',
        //     'driver_delivery_instructions' => 'nullable|string',
        //     'multiple_marks_numbers' => 'nullable|string',
        //     'docs_sent_datetime' => 'nullable|date_format:Y-m-d H:i:s',
        //     'docs_received_datetime' => 'nullable|date_format:Y-m-d H:i:s',
        //     'third_party_post' => 'nullable|array',
        // ]);

        // Extract validated data
        $product = $request->input('product', '');
        $service = $request->input('service', '');
        $order_shipper_id = $request->input('order_shipper_id', '');
        $delivery_terms = $request->input('delivery_terms', '');
        $incoterm = $request->input('incoterm', '');
        $shipment_id = $request->input('delivery_note', 'SX' . time());
        $container_no = $request->input('container_num', '');
        $porder = $request->input('purchase_order', '');
        $notify_party = $request->input('notify_party', '');
        $currency = $request->input('currency', '');
        $goods_value = $request->input('goods_value', 0.00);
        $external_order_id = $request->input('external_order_id', '');
        $shipment_type = $request->input('shipment_type', 0);
        $region = $request->input('region', 0);
        $p_latitude = $request->input('p_latitude', '');
        $p_longitude = $request->input('p_longitude', '');
        $d_latitude = $request->input('d_latitude', '');
        $d_longitude = $request->input('d_longitude', '');
        $party_row_id = $request->input('order_party_row_id', '0');
        $order_inv_row_id = $request->input('order_inv_row_id', '0');
        $order_cargo_id = $request->input('order_cargo_id', '');
        $pickup = $request->input('order_pickup_id', '0');
        $delivery = $request->input('order_drop_id', '');
        $early_pickup = $request->input('early_pickup', '');
        $late_pickup = $request->input('late_pickup', '');
        $early_delivery = $request->input('early_delivery', '');
        $late_delivery = $request->input('late_delivery', '');
        $modeof_trasnport = $request->input('modeof_trasnport', 'LTL');
        $order_type = $request->input('order_type', '');
        $cost_center = $request->input('cost_center', null);
        $rev_row_id = $request->input('rev_row_id', '');
        $ordcost_row_id = $request->input('ordcost_row_id', '');
        $customer_code = $request->input('customer_id', '');
        $driver_pickup_instructions = $request->input('driver_pickup_instructions', '');
        $driver_delivery_instructions = $request->input('driver_delivery_instructions', '');
        $multiple_marks_numbers = $request->input('multiple_marks_numbers', '');
        $multiple_marks_numbers = str_replace(["\r\n", "\r", "\n"], ", ", $multiple_marks_numbers);
        $docs_sent_datetime = $request->input('docs_sent_datetime', '');
        $docs_received_datetime = $request->input('docs_received_datetime', '');
        $third_party_post = $request->input('third_party_post', []);
        $third_party_post_str = !empty($third_party_post) ? implode(',', $third_party_post) : '';

        // Process pickup and delivery times
        $pickup_times = $this->processOrderDatetimes($early_pickup, $late_pickup);
        $e_pickup = $this->getDateTimeByTimezone('UTC', $pickup_times['early'], $curtz)['datetime'];
        $l_pickup = $this->getDateTimeByTimezone('UTC', $pickup_times['late'], $curtz)['datetime'];

        $delivery_times = $this->processOrderDatetimes($early_delivery, $late_delivery);
        $e_delivery = $this->getDateTimeByTimezone('UTC', $delivery_times['early'], $curtz)['datetime'];
        $l_delivery = $this->getDateTimeByTimezone('UTC', $delivery_times['late'], $curtz)['datetime'];

        // Get party details
        $drop_id = $pickup_custid = 0;
        $pickup_name = $pickup_country = $pickup_street = $pickup_pincode = $pickup_city = $pickup_state = '';
        $drop_name = $drop_country = $drop_street = $drop_pincode = $drop_city = $drop_state = '';
        $pickup_latitude = $pickup_longitude = $drop_latitude = $drop_longitude = $pickup_address = $drop_address = '';

        $drop_details = $this->getPartyDetailsOptimized($delivery);
        if ($drop_details) {
            $drop_id = $drop_details->customeridentifier ?? $drop_details->code;
            $drop_name = $drop_details->name;
            $drop_state = $drop_details->state;
            $drop_country = $drop_details->country;
            $drop_street = $drop_details->street;
            $drop_pincode = $drop_details->pincode;
            $drop_city = $drop_details->city;
            $drop_latitude = $drop_details->latitude;
            $drop_longitude = $drop_details->longitude;
            $drop_mobile = $drop_details->mobile;
        }

        $shipper_details = $this->getPartyDetailsOptimized($order_shipper_id);
        if ($shipper_details) {
            $pickup_custid = $shipper_details->customeridentifier ?? $shipper_details->code;
            $pickup_name = $shipper_details->name;
            $pickup_state = $shipper_details->state;
            $pickup_country = $shipper_details->country;
            $pickup_street = $shipper_details->street;
            $pickup_pincode = $shipper_details->pincode;
            $pickup_city = $shipper_details->city;
            $pickup_latitude = $shipper_details->latitude;
            $pickup_longitude = $shipper_details->longitude;
            $pickup_mobile = $shipper_details->mobile;
        }

        // Determine latitude and longitude
        $lat1 = $lng1 = $lat2 = $lng2 = '';
        if ($pickup_latitude && $pickup_longitude && $drop_latitude && $drop_longitude) {
            $lat1 = $pickup_latitude;
            $lng1 = $pickup_longitude;
            $lat2 = $drop_latitude;
            $lng2 = $drop_longitude;
        } elseif ($p_latitude && $p_longitude && $d_latitude && $d_longitude) {
            $lat1 = $p_latitude;
            $lng1 = $p_longitude;
            $lat2 = $d_latitude;
            $lng2 = $d_longitude;
        } else {
            $add1 = implode(',', array_filter([$pickup_street, $pickup_city, $pickup_country, $pickup_pincode]));
            $add2 = implode(',', array_filter([$drop_street, $drop_city, $drop_country, $drop_pincode]));
            $data1 = $this->getLatLngsByPlace($add1);
            $lat1 = $data1[0] ?? '';
            $lng1 = $data1[1] ?? '';
            $data2 = $this->getLatLngsByPlace($add2);
            $lat2 = $data2[0] ?? '';
            $lng2 = $data2[1] ?? '';
        }

        // Get transport mode
        $transport_mode = TransportMode::where('code', $modeof_trasnport)
            ->select('id', 'name')
            ->first();
        $tid = $transport_mode->id ?? 0;
        $tname = $transport_mode->name ?? '';

        // Determine customer_id
        $customer_id = $pickup ? (int)$pickup : 0;
        if (!$customer_id && $customer_code) {
            $customer = SxPartyMembers::where('code', $customer_code)
                ->where('user_id', $user_id)
                ->where('status', 1)
                ->select('id')
                ->first();
            $customer_id = $customer->id ?? 0;
        }

        // Get timezone-converted dates
        $logdate = $this->getDateTimeByTimezone('UTC', now()->format('Y-m-d H:i:s'), $curtz)['datetime'];
        $docs_sent_datetime = $docs_sent_datetime && $docs_sent_datetime !== '0000-00-00 00:00:00'
            ? $this->getDateTimeByTimezone('UTC', $docs_sent_datetime, $curtz)['datetime']
            : '';
        $docs_received_datetime = $docs_received_datetime && $docs_received_datetime !== '0000-00-00 00:00:00'
            ? $this->getDateTimeByTimezone('UTC', $docs_received_datetime, $curtz)['datetime']
            : '';

        // Child ID from session
        $childid = $user->childid ?? 0;

        // Created source logic
        $created_source = '4';
        // Add logic for be_value == 'INCL' if needed
        // if ($be_value == 'INCL') { ... }

        // Insert order
        $orderinfo = [
            'shipment_id' => 0,
            'customer_id' => $customer_id,
            'product' => $product,
            'pickup_datetime' => $e_pickup,
            'delivery_datetime' => $e_delivery,
            'pickup_endtime' => $l_pickup,
            'drop_endtime' => $l_delivery,
            'goods_value' => $goods_value,
            'currency' => $currency,
            'org_id' => $org_id,
            'be_value' => $be_value,
            'created_at' => $logdate,
            'drop_custid' => $drop_id,
            'drop_partyid' => $drop_id,
            'user_id' => $user_id,
            'sub_uid' => $childid,
            'pickup_custid' => $pickup_custid,
            'pickup_partyid' => $pickup_custid,
            'pickup_country' => $pickup_country,
            'pickup_city' => $pickup_city,
            'pickup_pincode' => $pickup_pincode,
            'pickup_company' => $pickup_name,
            'pickup_address1' => $pickup_street,
            'pickup_address2' => $pickup_state,
            'delivery_country' => $drop_country,
            'delivery_city' => $drop_city,
            'delivery_pincode' => $drop_pincode,
            'delivery_company' => $drop_name,
            'delivery_address1' => $drop_street,
            'delivery_address2' => $drop_state,
            'is_created' => '1',
            'plat' => $lat1,
            'plng' => $lng1,
            'dlat' => $lat2,
            'dlng' => $lng2,
            'transport_mode' => $modeof_trasnport,
            'created_source' => $created_source,
            'external_order_id' => $external_order_id,
            'shipment_type' => $shipment_type,
            'region' => $region,
            'third_party_post' => $third_party_post_str,
        ];

        $order = Order::create($orderinfo);
        $order_id = $order->id;

        // Generate booking ID
        $user_data = User::where('id', $user_id)->select('country_code', 'default_org_id')->first();
        $country_code = $user_data->country_code ?? '';
        $booking_id = $this->generateBookingId([
            'user_id' => $user_id,
            'order_id' => $order_id,
            'country_code' => $country_code,
            'org_id' => $org_id,
        ]);

        // Update order with booking_id
        $order->update(['order_id' => $booking_id]);

        // Insert order details
        $details = [
            'service' => $service,
            'delivery_term' => $delivery_terms,
            'incoterm' => $incoterm,
            'notify_party' => $notify_party,
            'order_row_id' => $order_id,
            'order_id' => $booking_id,
            'created_at' => $logdate,
            'shipper_id' => $order_shipper_id,
            'order_type' => $order_type,
            'docs_received_datetime' => $docs_received_datetime ?: null,
            'docs_sent_datetime' => $docs_sent_datetime ?: null,
            'temperature_control' => '0',
            'valorance_insurance' => '0',
            'high_cargo_value' => '0',
            'customs_required' => '0',
            'user_id' => $user_id,
            'be_value' => $be_value,
            'order_id_ref' => $order_id, // Renamed to avoid conflict with order_id
        ];

        OrderDetail::create($details);

        $this->insertOrdersRefFileLineIdentifier([
            'pickupCity' => $pickup_city,
            'pickupState' => $pickup_state,
            'pickupCountry' => $pickup_country,
            'dropCity' => $drop_city,
            'dropState' => $drop_state,
            'dropCountry' => $drop_country,
            'org_id' => $org_id,
            'be_value' => $be_value,
            'orderRowId' => $order_id,
            'date' => $logdate,
        ]);

        $shipper_address = [
            'order_id' => $order_id,
            'be_value' => $be_value,
            'user_id' => $user_id,
            'party_master_id' => $order_shipper_id,
            'location_id' => $pickup_city,
            'street' => $pickup_street,
            'state' => $pickup_state,
            'address' => $pickup_address,
            'pincode' => $pickup_pincode,
            'country' => $pickup_country,
            'status' => 1,
            'created_at' => $cdate,
        ];
        $existing_shipper_address = OrderpartyAddress::where([
            'order_id' => $order_id,
            'party_master_id' => $order_shipper_id,
            'status' => 1,
        ])->first();

        if ($existing_shipper_address) {
            $existing_shipper_address->update($shipper_address);
            $shipperadd_id = $existing_shipper_address->id;
        } else {
            $shipper_address = OrderpartyAddress::create($shipper_address);
            $shipperadd_id = $shipper_address->id;
        }

        $delivery_address = [
            'order_id' => $order_id,
            'be_value' => $be_value,
            'user_id' => $user_id,
            'party_master_id' => $delivery,
            'location_id' => $drop_city,
            'street' => $drop_street,
            'state' => $drop_state,
            'address' => $drop_address,
            'pincode' => $drop_pincode,
            'country' => $drop_country,
            'status' => 1,
            'created_at' => $cdate,
        ];

        $existing_delivery_address = OrderpartyAddress::where([
            'order_id' => $order_id,
            'party_master_id' => $delivery,
            'status' => 1,
        ])->first();

        if ($existing_delivery_address) {
            $existing_delivery_address->update($delivery_address);
            $dropadd_id = $existing_delivery_address->id;
        } else {
            $delivery_address = OrderpartyAddress::create($delivery_address);
            $dropadd_id = $delivery_address->id;
        }

        // Handle cargo details
        $cargo_forship = [];
        if ($order_cargo_id) {
            $cargo_ids = array_unique(array_filter(explode(',', $order_cargo_id)));
            foreach ($cargo_ids as $cargo_id) {
                $cargo_details = CargoDetail::where('id', $cargo_id)->first();
                if ($cargo_details) {
                    $length = $cargo_details->length ?? 0;
                    $width = $cargo_details->width ?? 0;
                    $height = $cargo_details->height ?? 0;
                    $weight = $cargo_details->weight ?? 0;
                    $volume = $cargo_details->volume ?? 0;
                    $quantity = $cargo_details->quantity ?? 1;
                    $cargo_type = htmlspecialchars(str_replace(["'", "\""], '', $cargo_details->cargo_type ?? ''));
                    $description = htmlspecialchars(str_replace(["'", "\""], '', $cargo_details->goods_description ?? ''));
                    $volumetric_weight = $cargo_details->volumetric_weight ?? 0;
                    $ldm = $cargo_details->ldm ?? 0;
                    $second_weight = $cargo_details->second_weight ?? 0;
                    $second_volume = $cargo_details->second_volume ?? 0;
                    $item_id = $cargo_details->item_id ?? 0;
                    $marks_numbers = $cargo_details->marks_numbers ?? '';

                    $cargo_forship[] = $cargo_type;

                    $handling_unit = ShipunitType::where('unit_name', $cargo_type)
                        ->where('status', true)
                        ->value('id');

                    if (!$handling_unit) {
                        $handling_unit_data = [
                            'order_id' => $order_id,
                            'be_value' => $be_value,
                            'user_id' => $user_id,
                            'unit_name' => $cargo_type,
                            'unit_code' => $cargo_type,
                            'description' => $cargo_type,
                            'status' => 1,
                            'created_at' => $cdate,
                        ];
                        $handling_unit_model = ShipunitType::create($handling_unit_data);
                        $handling_unit = $handling_unit_model->id;
                    }

                    $qr_code = '';
                    if ($item_id > 0) {
                        $item = Item::where('id', $item_id)->value('item_number');
                        $qr_code = $item ?? '';
                    }

                    $cargo = [
                        'order_id' => $order_id,
                        'be_value' => $be_value,
                        'user_id' => $user_id,
                        'cargo_id' => $cargo_id,
                        'handling_unit' => $handling_unit,
                        'length' => $length,
                        'width' => $width,
                        'height' => $height,
                        'weight' => $weight,
                        'volumetric_weight' => $volumetric_weight,
                        'volweight_uom' => 'kg',
                        'ldm' => $ldm,
                        'volume' => $volume,
                        'second_volume' => $second_volume,
                        'second_weight' => $second_weight,
                        'quantity' => $quantity,
                        'quantity_type' => $cargo_type,
                        'cargo_content' => $description,
                        'qr_code' => $qr_code,
                        'marks_numbers' => $marks_numbers,
                        'status' => 1,
                        'created_at' => $cdate,
                    ];

                    OrderCargoDetail::create($cargo);
                }
            }
        }

        $unitspec = !empty($cargo_forship) ? implode(',', $cargo_forship) : '1';

        $totals = OrderCargoDetail::where('order_id', $order_id)
            ->where('status', true)
            ->selectRaw('COALESCE(SUM(weight), 0) as total_weight, COALESCE(SUM(volume), 0) as total_volume, COALESCE(SUM(quantity), 0) as total_quantity')
            ->first();

        $order->update([
            'volume' => $totals->total_volume,
            'weight' => $totals->total_weight,
            'quantity' => $totals->total_quantity,
        ]);

        // Insert order references
        if ($porder) {
            OrderReference::create([
                'order_id' => $order_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'reference_id' => 'PO',
                'ref_value' => $porder,
                'created_at' => $cdate,
            ]);
        }

        if ($driver_pickup_instructions) {
            OrderReference::create([
                'order_id' => $order_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'reference_id' => 'ORD_PIKINST',
                'ref_value' => $driver_pickup_instructions,
                'created_at' => $cdate,
            ]);
        }

        if ($driver_delivery_instructions) {
            OrderReference::create([
                'order_id' => $order_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'reference_id' => 'ORD_DLVINST',
                'ref_value' => $driver_delivery_instructions,
                'created_at' => $cdate,
            ]);
        }

        if ($multiple_marks_numbers) {
            OrderReference::create([
                'order_id' => $order_id,
                'be_value' => $be_value,
                'user_id' => $user_id,
                'reference_id' => 'MARKS_NUMBERS',
                'ref_value' => $multiple_marks_numbers,
                'created_at' => $cdate,
            ]);
        }

        // Handle order parties
        $ids = $party_row_id !== '0' ? array_filter(explode(',', $party_row_id)) : [];
        $inv_ids = $order_inv_row_id !== '0' ? array_filter(explode(',', $order_inv_row_id)) : [];
        $ids = array_merge($ids, $inv_ids);

        foreach ($ids as $id) {
            if ($id) {
                $party_type = SxPartyMembers::where('id', $id)->value('party_type') ?? 1;
                $existing_party = OrderParty::where([
                    'order_id' => $order_id,
                    'party_id' => $id,
                    'party_type' => $party_type,
                    'status' => 1,
                ])->first();

                if (!$existing_party) {
                    OrderParty::create([
                        'order_id' => $order_id,
                        'be_value' => $be_value,
                        'user_id' => $user_id,
                        'order_number' => $booking_id,
                        'party_id' => $id,
                        'party_type' => $party_type,
                        'status' => 1,
                        'created_at' => $cdate,
                    ]);
                }
            }
        }

        // Sub customer parties
        $sub_cut_parties = [$order_shipper_id, $delivery, '0', '0'];
        $this->subcustpartiesinsert($order_id, $booking_id, $sub_cut_parties, $org_id, $be_value, $user_id, $cdate);

        // Handle revenue updates
        $rev_ids = $rev_row_id && $rev_row_id !== '0' ? array_filter(explode(',', $rev_row_id)) : [];
        if ($ordcost_row_id) {
            $cost_ids = array_filter(explode(',', $ordcost_row_id));
            $rev_ids = array_merge($rev_ids, $cost_ids);
        }
        if ($rev_ids) {
            Revenue::whereIn('id', $rev_ids)->update(['order_id' => $order_id]);
        }

        // Customer details
        $customer_details = SxPartyMembers::where('id', $customer_id)
            ->select('name', 'mobile', 'email')
            ->first();
        $customer_email = $customer_details->email ?? '';
        $customer_phone = $customer_details->mobile ?? '';

        // Geocode and shipment
        $pickupinfo = [
            'country' => trim($pickup_country),
            'order_country' => trim($pickup_country),
            'order_city' => trim($pickup_city),
            'order_zipcode' => trim($pickup_pincode),
            'state' => trim($pickup_state),
            'city' => trim($pickup_city),
            'region' => trim($pickup_street),
            'zipcode' => trim($pickup_pincode),
            'stoptype' => 'P',
        ];

        $dropinfo = [
            'country' => trim($drop_country),
            'order_country' => trim($drop_country),
            'order_city' => trim($drop_city),
            'order_zipcode' => trim($drop_pincode),
            'state' => trim($drop_state),
            'city' => trim($drop_city),
            'region' => trim($drop_street),
            'zipcode' => trim($drop_pincode),
            'stoptype' => 'D',
        ];

        $pickupgeocode = $this->checkgeocode($pickupinfo);
        $dropgeocode = $this->checkgeocode($dropinfo);

        if ($pickupgeocode && $dropgeocode) {
            $pickupgeocode['stoptype'] = 'P';
            $dropgeocode['stoptype'] = 'D';
            $pickupgeocode['order_country'] = trim($pickup_country);
            $pickupgeocode['order_city'] = trim($pickup_city);
            $pickupgeocode['order_zipcode'] = trim($pickup_pincode);
            $dropgeocode['order_country'] = trim($drop_country);
            $dropgeocode['order_city'] = trim($drop_city);
            $dropgeocode['order_zipcode'] = trim($drop_pincode);
            $pickupgeocode['cargo'] = $cargo ?? [];
            $dropgeocode['cargo'] = $cargo ?? [];
            $pickuproute = $this->getcust_routeautomate($customer_id, $pickupgeocode);
            $droproute = $this->getcust_routeautomate($customer_id, $dropgeocode);

            if ($pickuproute && $droproute) {
                $orderinfo = [
                    'id' => $order_id,
                    'order_id' => $booking_id,
                    'shipment_name' => 'BOXES',
                    'customer_phone' => $customer_phone,
                    'customer_email' => $customer_email,
                    'volume' => $totals->total_volume,
                    'weight' => $totals->total_weight,
                    'quantity' => $totals->total_quantity,
                ];
                $this->createshipmentbyorder($pickuproute, $orderinfo);
            } else {
                $pickupinfo['cargo'] = $cargo ?? [];
                $dropinfo['cargo'] = $cargo ?? [];
                $pickuproute1 = $this->getcust_routeautomate($customer_id, $pickupinfo);
                $droproute1 = $this->getcust_routeautomate($customer_id, $dropinfo);
                if ($pickuproute1 && $droproute1) {
                    $orderinfo = [
                        'id' => $order_id,
                        'order_id' => $booking_id,
                        'shipment_name' => 'BOXES',
                        'customer_phone' => $customer_phone,
                        'customer_email' => $customer_email,
                        'volume' => $totals->total_volume,
                        'weight' => $totals->total_weight,
                        'quantity' => $totals->total_quantity,
                    ];
                    $this->createshipmentbyorder($pickuproute1, $orderinfo);
                }
            }
        } else {
            $pickupinfo['cargo'] = $cargo ?? [];
            $dropinfo['cargo'] = $cargo ?? [];
            $pickuproute1 = $this->getcust_routeautomate($customer_id, $pickupinfo);
            $droproute1 = $this->getcust_routeautomate($customer_id, $dropinfo);
            if ($pickuproute1 && $droproute1) {
                $orderinfo = [
                    'id' => $order_id,
                    'order_id' => $booking_id,
                    'shipment_name' => 'BOXES',
                    'customer_phone' => $customer_phone,
                    'customer_email' => $customer_email,
                    'volume' => $totals->total_volume,
                    'weight' => $totals->total_weight,
                    'quantity' => $totals->total_quantity,
                ];
                $this->createshipmentbyorder($pickuproute1, $orderinfo);
            }
        }


        // Rate management
        $pref_arr = [
            'pickup' => strtoupper($pickup_country),
            'pickup_state' => strtoupper($pickup_state),
            'pickup_city' => strtoupper($pickup_city),
            'pickup_pincode' => $pickup_pincode,
            'drop' => strtoupper($drop_country),
            'drop_state' => strtoupper($drop_state),
            'drop_city' => strtoupper($drop_city),
            'drop_pincode' => $drop_pincode,
            'customer_id' => $customer_code,
            'service' => $service,
            'product' => $product,
            'user_id' => $user_id,
            'org_id' => $org_id,
            'order_type' => $order_type,
            'order_id' => $order_id,
            'customer_row_id' => $customer_id,
        ];

        $this->addrecodfororderinsertion($pref_arr);

        return response()->json([
            'status' => 'success',
            'message' => 'Order created successfully',
            'data' => ['status' => 1, 'order_id' => $order_id, 'booking_id' => $booking_id]
        ], 200);
    }

    private function processOrderDatetimes($early_time, $late_time)
    {
        $early_formatted = !empty($early_time) && $early_time !== '0000-00-00 00:00:00'
            ? date('Y-m-d H:i:s', strtotime($early_time))
            : now()->format('Y-m-d H:i:s');

        $late_formatted = !empty($late_time) && $late_time !== '0000-00-00 00:00:00'
            ? date('Y-m-d H:i:s', strtotime($late_time))
            : now()->addHour()->format('Y-m-d H:i:s');

        return [
            'early' => $early_formatted,
            'late' => $late_formatted
        ];
    }

    protected function checkgeocode($location)
    {
        // Normalize country codes
        if (strtolower($location['country']) === 'russia' || in_array($location['country'], ['РОССИЯ', 'Россия'])) {
            $location['country'] = 'RU';
        }
        if (strtolower($location['country']) === 'australia' || strtoupper($location['country']) === 'AU') {
            $location['country'] = 'AU';
        }

        $geocode = SxGeocode::where([
            'country' => $location['country'],
            'postal_code' => $location['zipcode'],
            'status' => 1,
        ])->select('postal_code', 'district', 'city', 'province', 'country', 'region')->first();

        if ($geocode) {
            return [
                'country' => $geocode->country,
                'state' => $geocode->province,
                'city' => $geocode->city,
                'region' => $geocode->region,
                'zipcode' => $geocode->postal_code,
            ];
        }

        $geocode = SxGeocode::where([
            'country' => $location['country'],
            'city' => $location['city'],
            'status' => 1,
        ])->select('postal_code', 'district', 'city', 'province', 'country', 'region')->first();

        return $geocode ? [
            'country' => $geocode->country,
            'state' => $geocode->province,
            'city' => $geocode->city,
            'region' => $geocode->region,
            'zipcode' => $geocode->postal_code,
        ] : [];
    }

    private function getPartyDetailsOptimized($party_id, $fields = ['name', 'code', 'location_id as city', 'street', 'state', 'address', 'country', 'pincode', 'latitude', 'longitude', 'mobile'])
    {
        if (empty($party_id) || $party_id == "0") {
            return null;
        }

        return SxPartyMembers::where('id', $party_id)
            ->where('status', 1)
            ->select($fields)
            ->first();
    }

    private function getDateTimeByTimezone($uzone, $dt, $dzone)
    {
        if ($dt) {
            $res['date'] = date('Y-m-d', strtotime($dt));
            $res['time'] = date('H:i:s', strtotime($dt));
            $res['datetime'] = date('Y-m-d H:i:s', strtotime($dt));

            if ($uzone && $dzone && $uzone !== $dzone) {
                $userTimezone = new DateTimeZone($uzone);
                $dbTimezone = new DateTimeZone($dzone);
                $currDate = new DateTime($res['datetime'], $dbTimezone);
                $currDate->setTimezone($userTimezone);
                $res['datetime'] = $currDate->format('Y-m-d H:i:s');
                $res['date'] = $currDate->format('Y-m-d');
                $res['time'] = $currDate->format('H:i:s');
            }
        } else {
            $res['date'] = now()->format('Y-m-d');
            $res['time'] = now()->format('H:i:s');
            $res['datetime'] = now()->format('Y-m-d H:i:s');
        }

        return $res;
    }

    private function getLatLngsByPlace($address)
    {
        $address = trim($address);
        $cacheKey = 'latlong:' . hash('sha256', $address);

        // Check cache
        $cachedResults = Cache::get($cacheKey);
        if ($cachedResults) {
            return $cachedResults;
        }

        // Process address
        $addressArray = explode(',', $address);
        $arrayCount = count($addressArray);
        $arrayCountry = trim($addressArray[$arrayCount - 2] ?? '');

        if (strlen($arrayCountry) < 4 && $arrayCountry !== '') {
            $country = CountryMaster::where('status', 1)
                ->where(function ($query) use ($arrayCountry) {
                    $query->where('country_name', $arrayCountry)
                        ->orWhere('country_code', $arrayCountry);
                })
                ->value('country_name');

            if ($country) {
                $addressArray[$arrayCount - 2] = $country;
                $address = implode(',', $addressArray);
            }
        }

        // Check database
        $databaseResults = LocationData::where('source', $address)
            ->select('name1', 'lat', 'lng', 'country')
            ->first();

        if ($databaseResults) {
            $results = [
                $databaseResults->lat,
                $databaseResults->lng,
                $databaseResults->name1,
                $databaseResults->country,
            ];
            Cache::put($cacheKey, $results, now()->addHour());
            return $results;
        }

        // Google Maps API
        $apiKey = config('services.google_maps.api_key');
        if (!$apiKey) {
            return ['', '', '', ''];
        }

        $response = Http::get('https://maps.googleapis.com/maps/api/geocode/json', [
            'address' => $address,
            'key' => $apiKey,
        ]);

        $apiResults = $response->json();
        if (empty($apiResults) || $apiResults['status'] !== 'OK') {
            return ['', '', '', ''];
        }

        $googleLat = $apiResults['results'][0]['geometry']['location']['lat'];
        $googleLng = $apiResults['results'][0]['geometry']['location']['lng'];
        $googleFormattedAddress = $apiResults['results'][0]['formatted_address'];
        $googleShortAddress = '';
        $country = '';

        foreach ($apiResults['results'][0]['address_components'] as $component) {
            if (in_array('route', $component['types'])) {
                $googleShortAddress = $component['long_name'];
            } elseif (in_array('country', $component['types'])) {
                $country = $component['short_name'];
            }
        }

        // Store in database
        LocationData::create([
            'location_name' => '',
            'name1' => $googleFormattedAddress,
            'name2' => $googleShortAddress,
            'country' => $country,
            'lat' => (string)$googleLat,
            'lng' => (string)$googleLng,
            'source' => $address,
        ]);

        $results = [$googleLat, $googleLng, $googleFormattedAddress, $country];
        Cache::put($cacheKey, $results, now()->addHour());

        return $results;
    }

    private function generateBookingId($orderinfo)
    {
        $country_code = $orderinfo['country_code'] ?? '';
        $org_id = $orderinfo['org_id'] ?? '';
        $order_id = $orderinfo['order_id'] ?? 0;
        $user_id = $orderinfo['user_id'] ?? 0;

        $tz = $this->getUserTimeZone($country_code);
        $phone_code = $tz['phone_code'] ?? '';
        $cdate = new DateTime('now', new DateTimeZone('UTC'));
        $year = $cdate->format('y');
        $week = $cdate->format('W');

        if ($org_id === 'KNAU') {
            $codeyear = $phone_code . $week;
        } else {
            $codeyear = $phone_code . $year;
        }

        $companyCondition = match ($org_id) {
            'NZKN', 'NZPG' => "(org_id LIKE '%NZPG%' OR org_id LIKE '%NZKN%')",
            'THKN', 'THCL' => "(org_id LIKE '%THCL%' OR org_id LIKE '%THKN%')",
            default => "org_id = '$org_id'",
        };

        $whr = "id < '$order_id' AND (order_id LIKE '$codeyear%' AND $companyCondition)";
        $previd = Order::select('order_id')->whereRaw($whr)->orderBy('id', 'DESC')->first();

        if ($previd) {
            $week_orderno = $previd->order_id;
            $cn_length = strlen($phone_code);
            $wcount = $org_id === 'KNAU' ? $cn_length + 2 : $cn_length + 2;
            $ycount = $wcount + 2;
            $prev_weekno = mb_substr($week_orderno, $cn_length, 2);
            $prev_ordno = mb_substr($week_orderno, $ycount, 5);
            $id = ltrim($prev_ordno, '0');

            if ($prev_weekno < $week) {
                $id = '0001';
            } else {
                $i_id = (int)$id + 1;
                $id = str_pad($i_id, 4, '0', STR_PAD_LEFT);
            }

            $booking_id = $org_id === 'KNAU'
                ? $phone_code . $week . $year . $id
                : $phone_code . $year . $week . $id;

            $chk = Order::where('order_id', $booking_id)->exists();
            if ($chk) {
                $iid = (int)$id + 1;
                $ii_d = str_pad($iid, 4, '0', STR_PAD_LEFT);
                $booking_id = $org_id === 'KNAU' || $org_id === 'UKKN-CG'
                    ? $phone_code . $week . $year . $ii_d
                    : $phone_code . $year . $week . $ii_d;
            }
        } else {
            $id = '0001';
            $booking_id = $org_id === 'KNAU'
                ? $phone_code . $week . $year . $id
                : $phone_code . $year . $week . $id;
        }

        // Save unique order ID
        try {
            UniqueOrderId::create(['order_id' => $booking_id]);
        } catch (Exception $e) {
            $booking_id = $this->getUniqueOrderId($booking_id);
        }

        return $booking_id;
    }

    private function getUserTimeZone($country_code)
    {
        // Placeholder: Implement timezone lookup based on country_code
        return ['phone_code' => $country_code, 'timezone' => 'UTC'];
    }

    protected function insertOrdersRefFileLineIdentifier(array $info): void
    {
        $getRefData = OrderFileLineIdentifier::where([
            'source_city' => $info['pickupCity'],
            'source_suburb' => $info['pickupState'],
            'source_country' => $info['pickupCountry'],
            'destination_city' => $info['dropCity'],
            'destination_suburb' => $info['dropState'],
            'destination_country' => $info['dropCountry'],
            'org_id' => $info['org_id'],
            'status' => 1,
        ])->first(['ref_value']);

        if ($getRefData) {
            $checkReference = OrderReference::where([
                'order_id' => $info['orderRowId'],
                'reference_id' => 'FI',
            ])->first(['id']);

            if (!$checkReference) {
                OrderReference::create([
                    'order_id' => $info['orderRowId'],
                    'reference_id' => 'FI',
                    'ref_value' => $getRefData->ref_value,
                    'status' => 1,
                    'created_at' => $info['date'],
                ]);
            } else {
                $checkReference->update([
                    'order_id' => $info['orderRowId'],
                    'reference_id' => 'FI',
                    'ref_value' => $getRefData->ref_value,
                ]);
            }
        }
    }

    private function getUniqueOrderId($orderId, $tries = 0): ?string
    {
        try {
            UniqueOrderId::create(['order_id' => $orderId]);
            return $orderId;
        } catch (Exception $e) {
            if ($tries >= 2) {
                throw new Exception('Maximum retries for generating a unique orderId reached');
            }
            $tries++;
            $newOrderId = $this->nextUniqueOrderId($orderId);
            return $this->getUniqueOrderId($newOrderId, $tries);
        }
    }

    private function nextUniqueOrderId($orderId): string
    {
        $idPart = (int)substr($orderId, -4) + 1;
        $id = str_pad($idPart, 4, '0', STR_PAD_LEFT);
        return substr($orderId, 0, -4) . $id;
    }

    protected function subcustpartiesinsert($order_id, $booking_id, $data, $org_id, $be_value, $user_id, $cdate): void
    {
        $names = ['shipper', 'consignee', 'pickup', 'delivery'];
        foreach ($data as $i => $party_id) {
            if ($party_id && $i < 4) {
                $party_type_data = SxPartyTypes::where([
                    'type_name' => $names[$i],
                    'status' => 1,
                    'org_id' => $org_id
                ])->first();

                $party_type = $party_type_data ? $party_type_data->id : SxPartyTypes::create([
                    'type_name' => $names[$i],
                    'description' => $names[$i],
                    'org_id' => $org_id,
                    'be_value' => $be_value,
                    'status' => 1,
                ])->id;

                $existing_parties = OrderParty::where([
                    'order_id' => $order_id,
                    'status' => 1,
                ])->get();

                foreach ($existing_parties as $existing_party) {
                    $existing_party_type = SxPartyTypes::where('id', $existing_party->party_type)->value('type_name');
                    if ($existing_party_type && strtoupper($existing_party_type) === strtoupper($names[$i])) {
                        if ($existing_party->party_id != $party_id) {
                            $existing_party->update(['status' => false]);
                        }
                    }
                }

                $existing_party = OrderParty::where([
                    'order_id' => $order_id,
                    'party_id' => $party_id,
                    'party_type' => $party_type,
                    'status' => 1,
                ])->first();

                if (!$existing_party) {
                    $check_party = OrderParty::where([
                        'order_id' => $order_id,
                        'party_id' => $party_id,
                        'status' => 1,
                    ])->first();

                    if ($check_party) {
                        $check_party_type_name = SxPartyTypes::where('id', $check_party->party_type)->value('type_name');
                        if (!$check_party_type_name || strtoupper($check_party_type_name) !== strtoupper($names[$i])) {
                            OrderParty::create([
                                'order_id' => $order_id,
                                'be_value' => $be_value,
                                'user_id' => $user_id,
                                'order_number' => $booking_id,
                                'party_id' => $party_id,
                                'party_type' => $party_type,
                                'status' => 1,
                                'created_at' => $cdate,
                            ]);
                        }
                    } else {
                        OrderParty::create([
                            'order_id' => $order_id,
                            'be_value' => $be_value,
                            'user_id' => $user_id,
                            'order_number' => $booking_id,
                            'party_id' => $party_id,
                            'party_type' => $party_type,
                            'status' => 1,
                            'created_at' => $cdate,
                        ]);
                    }
                }
            }
        }
    }

    protected function getcust_routeautomate($usr, $location)
    {
        $order_id = isset($location['cargo']['order_id']) ? $location['cargo']['order_id'] : 0;

        $cargo_info = OrderCargodetail::where([
            'order_id' => $order_id,
            'status' => 1,
        ])->select([
            'id',
            'order_id',
            'cargo_id',
            'handling_unit',
            'length',
            'width',
            'height',
            'weight',
            'second_weight',
            'volumetric_weight',
            'volweight_uom',
            'ldm',
            'volume',
            'second_volume',
            'quantity',
            'scanned_quantity',
            'quantity_type',
            'cargo_content',
        ])->get();

        $weight = 0;
        $second_weight = 0;
        $volume = 0;
        $second_volume = 0;

        foreach ($cargo_info as $row) {
            $weight += $row->weight;
            $second_weight += $row->second_weight ?? 0;
            $volume += $row->volume;
            $second_volume += $row->second_volume ?? 0;
        }

        $cargo_details = [
            'weight' => $weight,
            'second_weight' => $second_weight,
            'volume' => $volume,
            'second_volume' => $second_volume,
        ];

        $res = [];
        $ship_identifier = $cons_identifier = $notify_identifier = '';
        $stoptype = $location['stoptype'];

        $routes = RoutingAuto::where([
            'cust_id' => $usr,
            'triggered_type' => '2',
            'status' => 1,
        ])->select([
            'pick_type',
            'pick_val',
            'deli_type',
            'deli_val',
            'ship_identifier',
            'cons_identifier',
            'notify_identifier',
            'carrier_id',
            'vehicle_type',
            'vehicle_id',
            'driver_id',
            'template_id',
            'min_weight',
            'max_weight',
            'min_weight_uom',
            'max_weight_uom',
            'min_volume',
            'max_volume',
            'min_volume_uom',
            'max_volume_uom',
            'min_weight_ac',
            'max_weight_ac',
            'min_weight_uom_ac',
            'max_weight_uom_ac',
            'min_volume_ac',
            'max_volume_ac',
            'min_volume_uom_ac',
            'max_volume_uom_ac',
        ])->get();

        foreach ($routes as $rw) {
            $ship_identifier = $rw->ship_identifier;
            $cons_identifier = $rw->cons_identifier;
            $notify_identifier = $rw->notify_identifier;

            $constraints = ($rw->min_weight == 0.00 && $rw->max_weight == 0.00 &&
                $rw->min_weight_ac == 0.00 && $rw->max_weight_ac == 0.00 &&
                $rw->min_volume == 0.00 && $rw->max_volume == 0.00 &&
                $rw->min_volume_ac == 0.00 && $rw->max_volume_ac == 0.00) ? 'yes' : 'no';

            $loc_type = $stoptype == 'P' ? 'pick_type' : 'deli_type';
            $loc_val = $stoptype == 'P' ? 'pick_val' : 'deli_val';

            if ($rw->$loc_type == 'Country') {
                if (!empty($location['country']) && strtoupper($location['country']) == strtoupper($rw->$loc_val)) {
                    $checkauto = $this->checkcargofir_autorout($cargo_details, $rw);
                    if ($checkauto == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    }
                }
                if (empty($res) && isset($location['order_country']) && !empty($location['order_country']) && strtoupper($location['order_country']) == strtoupper($rw->$loc_val)) {
                    $checkauto = $this->checkcargofir_autorout($cargo_details, $rw);
                    if ($checkauto == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    }
                }
            } elseif ($rw->$loc_type == 'State') {
                if (!empty($location['state']) && strtoupper($location['state']) == strtoupper($rw->$loc_val)) {
                    $checkauto = $this->checkcargofir_autorout($cargo_details, $rw);
                    if ($checkauto == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    }
                    break;
                }
            } elseif ($rw->$loc_type == 'City') {
                if (!empty($location['city']) && strtoupper($location['city']) == strtoupper($rw->$loc_val)) {
                    $checkauto = $this->checkcargofir_autorout($cargo_details, $rw);
                    if ($checkauto == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    }
                }
            } elseif ($rw->$loc_type == 'Zipcode') {
                if (!empty($location['zipcode']) && strtoupper($location['zipcode']) == strtoupper($rw->$loc_val)) {
                    $checkauto = $this->checkcargofir_autorout($cargo_details, $rw);
                    if ($checkauto == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                    }
                    break;
                }
            } elseif ($rw->$loc_type == 'Location ID' || $rw->$loc_type == 'Region') {
                $x = 0;
                if (!empty($location['region']) && strtoupper($location['region']) == strtoupper($rw->$loc_val)) {
                    $checkauto = $this->checkcargofir_autorout($cargo_details, $rw);
                    if ($checkauto == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                        $x = 1;
                    } elseif ($constraints == 'yes') {
                        $res = [
                            'carrier_id' => $rw->carrier_id,
                            'vehicle_type' => $rw->vehicle_type,
                            'vehicle_id' => $rw->vehicle_id,
                            'driver_id' => $rw->driver_id,
                            'template_id' => $rw->template_id,
                        ];
                        $x = 1;
                    }
                }
                if ($x == 0 && isset($location['order_city'])) {
                    $getregion_city = SxGeocode::where([
                        'city' => $location['order_city'],
                        'region' => strtoupper($rw->$loc_val),
                        'status' => 1,
                    ])->select('id')->exists();
                    if ($getregion_city) {
                        $checkauto = $this->checkcargofir_autorout($cargo_details, $rw);
                        if ($checkauto == 'yes') {
                            $res = [
                                'carrier_id' => $rw->carrier_id,
                                'vehicle_type' => $rw->vehicle_type,
                                'vehicle_id' => $rw->vehicle_id,
                                'driver_id' => $rw->driver_id,
                                'template_id' => $rw->template_id,
                            ];
                            $x = 1;
                        } elseif ($constraints == 'yes') {
                            $res = [
                                'carrier_id' => $rw->carrier_id,
                                'vehicle_type' => $rw->vehicle_type,
                                'vehicle_id' => $rw->vehicle_id,
                                'driver_id' => $rw->driver_id,
                                'template_id' => $rw->template_id,
                            ];
                            $x = 1;
                        }
                    }
                }
                if ($x == 0 && isset($location['order_zipcode'])) {
                    $getregion_zipcode = SxGeocode::where([
                        'postal_code' => $location['order_zipcode'],
                        'region' => strtoupper($rw->$loc_val),
                        'status' => 1,
                    ])->select('id')->exists();
                    if ($getregion_zipcode) {
                        $checkauto = $this->checkcargofir_autorout($cargo_details, $rw);
                        if ($checkauto == 'yes') {
                            $res = [
                                'carrier_id' => $rw->carrier_id,
                                'vehicle_type' => $rw->vehicle_type,
                                'vehicle_id' => $rw->vehicle_id,
                                'driver_id' => $rw->driver_id,
                                'template_id' => $rw->template_id,
                            ];
                            $x = 1;
                        } elseif ($constraints == 'yes') {
                            $res = [
                                'carrier_id' => $rw->carrier_id,
                                'vehicle_type' => $rw->vehicle_type,
                                'vehicle_id' => $rw->vehicle_id,
                                'driver_id' => $rw->driver_id,
                                'template_id' => $rw->template_id,
                            ];
                            $x = 1;
                        }
                    }
                }
            }
            if (array_filter([$ship_identifier, $cons_identifier, $notify_identifier])) {
                if (!$this->checkIdentifire($location, $ship_identifier, $cons_identifier, $notify_identifier)) {
                    return [];
                }
            }
            if (!empty($res)) {
                break;
            }
        }

        return $res;
    }

    protected function checkIdentifire(array $orderDetails, string $shipperIdentifier, string $consigneeIdentifier, string $notifyIdentifier): bool
    {
        $parties = OrderParty::select([
            'order_parties.id',
            'order_parties.order_id',
            'order_parties.party_id',
            'order_parties.party_type',
            'sx_party_types.type_name as name',
            'sx_party_members.code',
        ])
            ->join('sx_party_types', 'order_parties.party_type', '=', 'sx_party_types.id')
            ->join('sx_party_members', 'order_parties.party_id', '=', 'sx_party_members.id')
            ->where([
                'order_parties.order_id' => $orderDetails['cargo']['order_id'],
                'order_parties.status' => true,
            ])
            ->get();

        if ($parties->isEmpty()) {
            return false;
        }

        $shipperCode = $consigneeCode = $notifyCode = '';
        foreach ($parties as $row) {
            switch (strtolower($row->name)) {
                case 'customer':
                    $customerCode = $row->code;
                    break;
                case 'shipper':
                    $shipperCode = $row->code;
                    break;
                case 'consignee':
                    $consigneeCode = $row->code;
                    break;
                case 'notify_party':
                    $notifyCode = $row->code;
                    break;
            }
        }

        if ($shipperIdentifier && $consigneeIdentifier && $notifyIdentifier) {
            return $shipperIdentifier === $shipperCode && $consigneeIdentifier === $consigneeCode && $notifyIdentifier === $notifyCode;
        }
        if ($shipperIdentifier && $consigneeIdentifier && empty($notifyIdentifier)) {
            return $shipperIdentifier === $shipperCode && $consigneeIdentifier === $consigneeCode;
        }
        if ($shipperIdentifier && empty($consigneeIdentifier) && empty($notifyIdentifier)) {
            return $shipperIdentifier === $shipperCode;
        }
        if (empty($shipperIdentifier) && $consigneeIdentifier && $notifyIdentifier) {
            return $consigneeIdentifier === $consigneeCode && $notifyIdentifier === $notifyCode;
        }
        if (empty($shipperIdentifier) && empty($consigneeIdentifier) && $notifyIdentifier) {
            return $notifyIdentifier === $notifyCode;
        }
        if (empty($shipperIdentifier) && $consigneeIdentifier && empty($notifyIdentifier)) {
            return $consigneeIdentifier === $consigneeCode;
        }
        if ($shipperIdentifier && empty($consigneeIdentifier) && $notifyIdentifier) {
            return $shipperIdentifier === $shipperCode && $notifyIdentifier === $notifyCode;
        }

        return false;
    }

    protected function checkcargofir_autorout($data, $rw)
    {
        $min_weight = $rw->min_weight;
        $max_weight = $rw->max_weight;
        $min_weight_ac = $rw->min_weight_ac;
        $max_weight_ac = $rw->max_weight_ac;
        $min_volume = $rw->min_volume;
        $max_volume = $rw->max_volume;
        $min_volume_ac = $rw->min_volume_ac;
        $max_volume_ac = $rw->max_volume_ac;

        $weightc = $this->checkwwhite($data, $rw);
        $weightc_ac = $this->checkwwhite_ac($data, $rw);
        $volumec = $this->checkvolume($data, $rw);
        $volumec_ac = $this->checkvolume_sc($data, $rw);

        if ($weightc == 'yes' && $weightc_ac == 'yes' && $volumec == 'yes' && $volumec_ac == 'yes') {
            return 'yes';
        }
        if ($volumec_ac == 'yes' && $volumec == 'yes' && $weightc_ac == 'yes' && $weightc != 'false') {
            return 'yes';
        }
        if ($volumec_ac == 'yes' && $volumec == 'yes' && $weightc == 'yes' && $weightc_ac != 'false') {
            return 'yes';
        }
        if ($volumec_ac == 'yes' && $volumec == 'yes' && $weightc != 'false' && $weightc_ac != 'false') {
            return 'yes';
        }
        if ($volumec_ac == 'yes' && $weightc_ac == 'yes' && $weightc == 'yes' && $volumec != 'false') {
            return 'yes';
        }
        if ($volumec_ac == 'yes' && $weightc_ac == 'yes' && $volumec != 'false' && $weightc != 'false') {
            return 'yes';
        }
        if ($volumec_ac == 'yes' && $weightc == 'yes' && $weightc_ac != 'false' && $volumec != 'false') {
            return 'yes';
        }
        if ($volumec_ac == 'yes' && $weightc != 'false' && $weightc_ac != 'false' && $volumec != 'false') {
            return 'yes';
        }
        if ($volumec == 'yes' && $weightc_ac == 'yes' && $weightc == 'yes' && $volumec_ac != 'false') {
            return 'yes';
        }
        if ($volumec == 'yes' && $weightc_ac == 'yes' && $volumec_ac != 'false' && $weightc != 'false') {
            return 'yes';
        }
        if ($volumec == 'yes' && $weightc == 'yes' && $weightc_ac != 'false' && $volumec_ac != 'false') {
            return 'yes';
        }
        if ($volumec == 'yes' && $weightc != 'false' && $weightc_ac != 'false' && $volumec_ac != 'false') {
            return 'yes';
        }
        if ($weightc_ac == 'yes' && $weightc == 'yes' && $volumec != 'false' && $volumec_ac != 'false') {
            return 'yes';
        }
        if ($weightc_ac == 'yes' && $weightc != 'false' && $volumec != 'false' && $volumec_ac != 'false') {
            return 'yes';
        }
        if ($weightc == 'yes' && $weightc_ac != 'false' && $volumec != 'false' && $volumec_ac != 'false') {
            return 'yes';
        }

        return 'no';
    }

    protected function checkwwhite($data, $rw)
    {
        if ($rw->min_weight >= 0 && $rw->max_weight >= 0) {
            if ($data['second_weight'] >= $rw->min_weight && $data['second_weight'] <= $rw->max_weight) {
                return 'yes';
            } else {
                return 'false';
            }
        }
        return 'no';
    }

    protected function checkwwhite_ac($data, $rw)
    {
        if ($rw->min_weight_ac >= 0 && $rw->max_weight_ac >= 0) {
            if ($data['weight'] >= $rw->min_weight_ac && $data['weight'] <= $rw->max_weight_ac) {
                return 'yes';
            } else {
                return 'false';
            }
        }
        return 'no';
    }

    protected function checkvolume($data, $rw)
    {
        if ($rw->min_volume >= 0 && $rw->max_volume >= 0) {
            if ($data['second_volume'] >= $rw->min_volume && $data['second_volume'] <= $rw->max_volume) {
                return 'yes';
            } else {
                return 'false';
            }
        }
        return 'no';
    }

    protected function checkvolume_sc($data, $rw)
    {
        if ($rw->min_volume_ac >= 0 && $rw->max_volume_ac >= 0) {
            if ($data['volume'] >= $rw->min_volume_ac && $data['volume'] <= $rw->max_volume_ac) {
                return 'yes';
            } else {
                return 'false';
            }
        }
        return 'no';
    }

    /**
     * Log user activity
     */
    private function logActivity(array $data)
    {
        try {
            \App\Models\UserActivity::create([
                'user_id' => $data['user_id'],
                'description' => $data['description'],
                'created_at' => $data['created_at'],
                'status' => 1
            ]);
            Log::info('Activity logged: ' . $data['description']);
        } catch (\Exception $e) {
            Log::error('Failed to log activity: ' . $e->getMessage());
        }
    }

    public function orderTypeIndex(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $validator = Validator::make($request->all(), [
            'org_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        $org_id = $request->input('org_id');
        $where = ['order_types.org_id' => $org_id, 'order_types.status' => 1];

        if ($org_id !== 'RUKN') {
            $be_value = $request->input('be_value');
            if (!$be_value) {
                return response()->json([
                    'status' => 'error',
                    'errors' => ['be_value' => 'Business entity value is required']
                ], 422);
            }
            $where['order_types.be_value'] = $be_value;
        }

        $search_params = $request->all();
        unset($search_params['org_id']);
        unset($search_params['be_value']);

        $search_where = [];
        if (!empty($search_params['type_name'])) {
            $search_where['order_types.type_name'] = ['LIKE', '%' . $search_params['type_name'] . '%'];
        }
        if (!empty($search_params['department_code'])) {
            $search_where['order_types.department_code'] = $search_params['department_code'];
        }
        if (!empty($search_params['customer_name'])) {
            $search_where['sx_users.employee_name'] = ['LIKE', '%' . $search_params['customer_name'] . '%'];
        }
        if (isset($search_params['status']) && $search_params['status'] !== '') {
            $search_where['order_types.status'] = $search_params['status'];
            unset($where['order_types.status']);
        }

        $query = OrderType::select('order_types.*', 'sx_users.employee_name as customer_name')
            ->leftJoin('sx_users', 'order_types.customer_id', '=', 'sx_users.id')
            ->where($where);

        foreach ($search_where as $key => $value) {
            if (is_array($value)) {
                $query->where($key, $value[0], $value[1]);
            } else {
                $query->where($key, $value);
            }
        }

        $order_types = $query->get()->map(function ($row) {
            return [
                'id' => $row->id,
                'type_name' => $row->type_name,
                'org_id' => $row->org_id,
                'be_value' => $row->be_value,
                'department_code' => $row->department_code,
                'customer_name' => $row->customer_name,
                'description' => $row->description,
                'status' => $row->status,
                'ordtype_code' => $row->ordtype_code
            ];
        });

        return response()->json([
            'status' => 'success',
            'data' => $order_types
        ]);
    }

    public function orderTypeShow(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        if (!$id) {
            return response()->json([
                'status' => 'error',
                'errors' => ['id' => 'The ID is required']
            ], 422);
        }

        $org_id = $request->input('org_id');
        $where = ['order_types.id' => $id, 'order_types.org_id' => $org_id];

        if ($org_id !== 'RUKN') {
            $be_value = $request->input('be_value');
            if (!$be_value) {
                return response()->json([
                    'status' => 'error',
                    'errors' => ['be_value' => 'Business entity value is required']
                ], 422);
            }
            $where['order_types.be_value'] = $be_value;
        }

        $order_type = OrderType::select('order_types.*', 'sx_users.employee_name as customer_name')
            ->leftJoin('sx_users', 'order_types.customer_id', '=', 'sx_users.id')
            ->where($where)
            ->first();

        if (!$order_type) {
            return response()->json([
                'status' => 'error',
                'message' => 'Order type not found'
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'data' => [
                'id' => $order_type->id,
                'type_name' => $order_type->type_name,
                'org_id' => $order_type->org_id,
                'be_value' => $order_type->be_value,
                'department_code' => $order_type->department_code,
                'customer_name' => $order_type->customer_name,
                'description' => $order_type->description,
                'status' => $order_type->status,
                'ordtype_code' => $order_type->ordtype_code,
                'customer_id' => $order_type->customer_id
            ]
        ]);
    }

    /**
     * Show the form for creating a new order type
     */
    public function orderTypeAdd(Request $request)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $org_id = $user->org_id;
        $be_value = $user->be_value;
        $uid = $user->id;
        $cust_id = $user->cust_id ?? 0;

        $data = [
            'org_id' => $org_id,
            'be_value' => $be_value,
            'customers' => SxPartyMembers::select('id', 'name', 'code')
                ->where(['org_id' => $org_id, 'be_value' => $be_value, 'status' => 1])
                ->orderBy('name')
                ->get(),
            'countries' => CountryMaster::select('country_code', 'country_name')
                ->where('status', 1)
                ->orderBy('country_name')
                ->get(),
            'business_entities' => DB::table('sx_business_entity_value')
                ->select('entity_id')
                ->where(['org_id' => $org_id, 'status' => 1])
                ->orderBy('entity_id')
                ->get()
        ];

        return response()->json([
            'status' => 'success',
            'data' => $data
        ]);
    }

    public function orderTypeCreate(Request $request)
    {
        Log::debug('OrderTypeController::store called with data: ' . json_encode($request->all()));

        $user = Auth::user();
        if (!$user) {
            Log::error('Unauthorized access attempt');
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $rules = [
            'type_name' => 'required|min:1',
            'org_id' => 'required|integer',
        ];

        if ($request->input('org_id') !== 'RUKN') {
            $rules['be_value'] = 'required|integer';
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            Log::error('Validation errors: ' . json_encode($validator->errors()));
            return response()->json([
                'status' => 'error',
                'errors' => $validator->errors()
            ], 422);
        }

        $data = $request->all();
        $data['customer_id'] = $request->input('customer_id', 0);
        $data['ordtype_code'] = !empty($data['type_name']) ? substr($data['type_name'], 0, 1) : '';
        $data['status'] = $request->input('status', 1);
        $data['created_at'] = now();

        try {
            $order_type = OrderType::create($data);
            return response()->json([
                'status' => 'success',
                'message' => 'Order type created successfully'
            ], 201);
        } catch (\Exception $e) {
            Log::error('Database error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Show the form for editing the specified order type
     */
    public function orderTypeEdit(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        $org_id = $user->org_id;
        $uid = $user->id;

        try {
            $orderType = OrderType::where(['id' => $id, 'org_id' => $org_id, 'status' => 1])->first();
            if (!$orderType) {
                return response()->json([
                    'status' => 'error',
                    'message' => 'Order type not found'
                ], 404);
            }

            $data = [
                'id' => $orderType->id,
                'org_id' => $orderType->org_id,
                'be_value' => $orderType->be_value,
                'type_name' => $orderType->type_name,
                'description' => $orderType->description,
                'ordtype_code' => $orderType->ordtype_code,
                'customer_id' => $orderType->customer_id,
                'customers' => SxPartyMembers::select('id', 'name', 'code')
                    ->where(['org_id' => $org_id, 'be_value' => $orderType->be_value, 'status' => 1])
                    ->orderBy('name')
                    ->get(),
                'countries' => CountryMaster::select('country_code', 'country_name')
                    ->where('status', 1)
                    ->orderBy('country_name')
                    ->get(),
                'business_entities' => DB::table('sx_business_entity_value')
                    ->select('entity_id')
                    ->where(['org_id' => $org_id, 'status' => 1])
                    ->orderBy('entity_id')
                    ->get()
            ];

            return response()->json([
                'status' => 'success',
                'data' => $data
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve order type edit data: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to retrieve order type edit data: ' . $e->getMessage()
            ], 500);
        }
    }

    public function orderTypeUpdate(Request $request, $id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        if (!$id) {
            return response()->json([
                'status' => 'error',
                'errors' => ['id' => 'The ID is required']
            ], 422);
        }

        $order_type = OrderType::find($id);
        if (!$order_type) {
            return response()->json([
                'status' => 'error',
                'message' => 'Order type not found'
            ], 404);
        }

        $data = $request->all();
        unset($data['org_id']);
        unset($data['be_value']);
        $data['customer_id'] = $request->input('customer_id', 0);
        $data['ordtype_code'] = !empty($data['type_name']) ? substr($data['type_name'], 0, 1) : '';
        $data['updated_at'] = now();

        try {
            $order_type->update($data);
            return response()->json([
                'status' => 'success',
                'message' => 'Order type updated successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Database error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ], 500);
        }
    }

    public function orderTypeDestroy($id)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json([
                'status' => 'error',
                'message' => 'Invalid or missing token'
            ], 401);
        }

        if (!$id) {
            return response()->json([
                'status' => 'error',
                'errors' => ['id' => 'The ID is required']
            ], 422);
        }

        $order_type = OrderType::find($id);
        if (!$order_type) {
            return response()->json([
                'status' => 'error',
                'message' => 'Order type not found'
            ], 404);
        }

        try {
            $order_type->update(['status' => 0]);
            Log::info('Order type deleted successfully for user_id: ' . $user->id);
            return response()->json([
                'status' => 'success',
                'message' => 'Order type deleted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Database error: ' . $e->getMessage());
            return response()->json([
                'status' => 'error',
                'message' => 'Database error: ' . $e->getMessage()
            ], 500);
        }
    }
}
