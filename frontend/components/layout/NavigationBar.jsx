import { useMenu } from "@/hooks/useMenu";
import RecursiveMenu from "./RecursiveMenu";
import { NavigationMenu, NavigationMenuList } from "@/components/ui/navigation-menu";
import { formatMenuData } from "@/utils/formatMenuData";

export default function NavigationBar() {
  const { data, isLoading } = useMenu();
  if (isLoading) return <div>Loading...</div>;
  const menus = data ? formatMenuData(data.menus) : [];
  return (
    <NavigationMenu viewport={false}>
      <NavigationMenuList>
        <RecursiveMenu items={menus} />
      </NavigationMenuList>
    </NavigationMenu>
  );
}
