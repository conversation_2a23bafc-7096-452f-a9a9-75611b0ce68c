"use client";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuSeparator,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  User,
  Settings,
  LogOut,
  Building2,
 ChevronRight,
  Crown
} from "lucide-react";
import useAuthStore from "@/stores/auth";
import { useRouter } from "next/navigation";

export default function ProfileMenu() {
  const user = useAuthStore((s) => s.user);
  const logout = useAuthStore((s) => s.logout);
  const router = useRouter();

  const handleLogout = () => {
    logout();
    router.push("/login");
  };

  const getUserInitials = () => {
    if (!user?.emp_name) return "N/A";
    return user.emp_name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="h-10 w-10 rounded-full p-0 border-2 border-slate-200 hover:border-slate-300 transition-colors duration-200"
          aria-label="Open user profile menu"
        >
          <Avatar className="h-8 w-8">
            <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold text-sm">
              {getUserInitials()}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-80 p-0" aria-label="User profile menu">
        {/* User Profile Header */}
        <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-slate-200">
          <div className="flex items-center space-x-3">
            <Avatar className="h-12 w-12 border-2 border-white shadow-sm">
              <AvatarFallback className="bg-gradient-to-br from-blue-500 to-purple-600 text-white font-semibold">
                {getUserInitials()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2">
                <div className="font-semibold text-slate-900 truncate">
                  {user?.emp_name || "N/A"}
                </div>
                {user?.is_super_user && (
                  <Crown className="w-4 h-4 text-yellow-500" aria-label="Super user" />
                )}
              </div>
              <div className="text-sm text-slate-600 truncate">
                {user?.email_id || user?.username || "N/A"}
              </div>
              {user?.emp_id && (
                <div className="text-xs text-slate-500 mt-1">
                  ID: {user.emp_id}
                </div>
              )}
              {user?.companyname && (
                <div className="flex items-center mt-1">
                  <Building2 className="w-3 h-3 text-slate-500 mr-1" aria-hidden="true" />
                  <span className="text-xs text-slate-500 truncate">
                    {user.companyname}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="p-2">
          <DropdownMenuItem asChild>
            <Button
              asChild
              variant="ghost"
              className="w-full justify-between px-3 py-2 rounded-md hover:bg-slate-50 h-auto"
            >
              <Link href="/profile">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-blue-100 rounded-md">
                    <User className="w-4 h-4 text-blue-600" aria-hidden="true" />
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-slate-900">Profile</div>
                    <div className="text-xs text-slate-500">View and edit your profile</div>
                  </div>
                </div>
                <ChevronRight className="w-4 h-4 text-slate-400" aria-hidden="true" />
              </Link>
            </Button>
          </DropdownMenuItem>

          <DropdownMenuItem asChild>
            <Button
              asChild
              variant="ghost"
              className="w-full justify-between px-3 py-2 rounded-md hover:bg-slate-50 h-auto"
            >
              <Link href="/settings">
                <div className="flex items-center space-x-3">
                  <div className="p-1.5 bg-green-100 rounded-md">
                    <Settings className="w-4 h-4 text-green-600" aria-hidden="true" />
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-slate-900">Settings</div>
                    <div className="text-xs text-slate-500">Manage your preferences</div>
                  </div>
                </div>
                <ChevronRight className="w-4 h-4 text-slate-400" aria-hidden="true" />
              </Link>
            </Button>
          </DropdownMenuItem>
        </div>

        <DropdownMenuSeparator />

        {/* Logout Section */}
        <div className="p-2">
          <DropdownMenuItem asChild>
            <Button
              variant="ghost"
              className="w-full justify-between px-3 py-2 rounded-md hover:bg-red-50 h-auto text-red-600 hover:text-red-700"
              onClick={handleLogout}
            >
              <div className="flex items-center space-x-3">
                <div className="p-1.5 bg-red-100 rounded-md">
                  <LogOut className="w-4 h-4 text-red-600" aria-hidden="true" />
                </div>
                <div className="text-left">
                  <div className="font-medium">Sign Out</div>
                  <div className="text-xs text-red-500">Log out of your account</div>
                </div>
              </div>
            </Button>
          </DropdownMenuItem>
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
