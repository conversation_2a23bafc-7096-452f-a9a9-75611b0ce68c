"use client";
import Logo from "@/components/base/Logo";
import NavigationBar from "./NavigationBar";
import ProfileMenu from "./ProfileMenu";
import { Bell } from 'lucide-react';

export default function TopNavBar({ menus }) {
  return (
    <header className="px-6 w-full flex items-center h-16 sticky top-0 left-0 bg-background shadow-sm mb-4">
      <div className="flex-shrink-0 mr-8">
        <Logo className="h-8" />
      </div>
      <NavigationBar menus={menus} />
      <div className="flex-shrink-0 flex items-center gap-x-4 ml-auto">
        <Bell className="w-6 h-6" />
        <ProfileMenu />
      </div>
    </header>
  );
}
