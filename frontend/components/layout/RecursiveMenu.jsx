import * as Icons from "lucide-react";
import Link from "next/link";
import {
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuSub,
  NavigationMenuSubTrigger,
} from "@/components/ui/navigation-menu";

export default function RecursiveMenu({ items, parentPath = "" }) {
  return items.map((item, idx) => {
    const Icon = item.icon ? Icons[item.icon] : null;
    const hasChildren = Array.isArray(item.children) && item.children.length > 0;

    // 1st level
    if (hasChildren && parentPath === "") {
      return (
        <NavigationMenuItem key={item.id + idx}>
          <NavigationMenuTrigger className="gap-x-1">
            {Icon && <Icon className="w-5 h-5" />}
            {item.id}
          </NavigationMenuTrigger>
          <NavigationMenuContent className="overflow-visible" style={{ overflow: "visible" }}>
            <NavigationMenuSub>
              <NavigationMenuList className="grid grid-cols-[1fr_1fr] gap-x-3">
                <RecursiveMenu items={item.children} parentPath={item.id} />
              </NavigationMenuList>
            </NavigationMenuSub>
          </NavigationMenuContent>
        </NavigationMenuItem>
      );
    }

    // 2nd/3rd level
    if (hasChildren) {
      return (
        <NavigationMenuItem key={item.id + idx}>
          <NavigationMenuSubTrigger className="px-2 w-full text-left justify-start">
            {item.id}
          </NavigationMenuSubTrigger>
          <NavigationMenuContent className="left-[65%] z-10 !shadow-lg" style={{ top: "0" }}>
            <ul className="grid w-fit">
              <RecursiveMenu items={item.children} parentPath={item.id} />
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      );
    }

    // Leaf node (direct links)
    return (
      <NavigationMenuItem key={item.id + idx}>
        <NavigationMenuLink asChild className="w-[16ch] items-start content-start">
          <Link href={item.url}>{item.id}</Link>
        </NavigationMenuLink>
      </NavigationMenuItem>
    );
  });
}
