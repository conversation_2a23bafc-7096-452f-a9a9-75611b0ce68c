# POST /api/notification-management/get-organizations

## Description
Retrieves a list of organizations for autocomplete functionality based on search query.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/notification-management/get-organizations`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "query": "RamCo"
}
```

## Success Response

```json
[
    "44",
    "45",
    "46"
]
```

### Empty Response (No matches)
```json
[]
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/get-organizations' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "query": "RamCo"
}'
```

### cURL Example with Organization ID:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/get-organizations' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "query": "44"
}'
```

### cURL Example with Empty Query:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/get-organizations' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "query": ""
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Server Error Response Example:
```json
[]
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Returns an array of organization IDs that match the search query.
- Search is performed on both organization_name and org_id fields.
- Only active organizations (status = 1) are returned.
- The search is case-insensitive and uses LIKE operator.
- Returns empty array if no matches are found.
- This endpoint is typically used for autocomplete functionality in forms.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Search functionality works correctly
- `[Pass]` Empty query returns all active organizations

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified search functionality works correctly
- Tested with various search queries
- Confirmed autocomplete functionality works properly

--- 