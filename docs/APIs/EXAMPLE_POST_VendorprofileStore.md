# POST /api/vendor-profiles

## Description
Creates a new vendor profile for the authenticated user.

## Authorization
- Required: Yes (Bearer token from login API)

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/vendor-profiles`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "vend_profile_id": 1,
    "profile_name": "Sample profile list sx",
    "description": "Profile description sx",
    "org_id": 1,
    "be_value": 0,
    "user_id": 1,
    "status": 1
}
```

## Success Response

```json
{
    "status": "success",
    "message": "Vendor profile created successfully.",
    "data": {
        "vend_profile_id": "VN4425280002",
        "name": "Sample profile list sx",
        "description": "Profile description sx",
        "org_id": 44,
        "be_value": 1,
        "user_id": 1,
        "status": 1,
        "updated_at": "2025-07-11T10:14:13.000000Z",
        "created_at": "2025-07-11T10:14:13.000000Z",
        "id": 2
    }
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/vendor-profiles' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "vend_profile_id": 1,
    "profile_name": "Sample profile list sx",
    "description": "Profile description sx",
    "org_id": 1,
    "be_value": 0,
    "user_id": 1,
    "status": 1
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Validation Error Response Example:
```json
{
    "status": "error",
    "message": "Validation failed.",
    "errors": {
        "profile_name": [
            "The profile name field is required."
        ]
    }
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Optional request parameters should be handled gracefully if omitted.
- Vendor profile ID is auto-generated in format: VN + country_code + year + week + sequence.
- org_id and be_value are automatically set based on user context.
- vendor_ids is optional and can be empty string.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Vendor profile ID generation works correctly
- `[Pass]` Vendor profile list updates work correctly

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 