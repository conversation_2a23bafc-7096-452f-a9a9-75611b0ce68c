# POST /api/notification-management/check-combination

## Description
Checks if a notification management combination already exists in the database.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/notification-management/check-combination`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "user_id": 1,
    "org_id": "44",
    "be_value": "1",
    "product": "Electronics",
    "inco_term": "FOB",
    "service": "Express",
    "order_type": "Import",
    "from_country": "USA",
    "to_country": "India",
    "notification_id": "NOTIFY001"
}
```

## Success Response

### When combination exists:
```json
"existed"
```

### When combination does not exist:
```json
"Not existed"
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/check-combination' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "user_id": 1,
    "org_id": "44",
    "be_value": "1",
    "product": "Electronics",
    "inco_term": "FOB",
    "service": "Express",
    "order_type": "Import",
    "from_country": "USA",
    "to_country": "India"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Validation Error Response Example:
```json
{
    "status": "error",
    "message": "Validation failed.",
    "errors": {
        "user_id": ["The user id field is required."],
        "org_id": ["The org id field is required."],
        "product": ["The product field is required."],
        "inco_term": ["The inco term field is required."],
        "service": ["The service field is required."],
        "order_type": ["The order type field is required."],
        "from_country": ["The from country field is required."],
        "to_country": ["The to country field is required."]
    }
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Returns "existed" if a matching combination is found.
- Returns "Not existed" if no matching combination is found.
- The notification_id parameter is used to exclude the current record from the check (useful during updates).
- All required fields must be provided for the check to work.
- This endpoint is typically used before creating or updating records to prevent duplicates.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Combination check works correctly for existing records
- `[Pass]` Combination check works correctly for non-existing records

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified combination checking works correctly
- Tested with various field combinations
- Confirmed duplicate prevention functionality

--- 