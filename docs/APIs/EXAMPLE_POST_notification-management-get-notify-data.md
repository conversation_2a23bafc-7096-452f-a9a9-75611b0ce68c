# POST /api/notification-management/get-notify-data

## Description
Retrieves notification management data based on specified filters and criteria.

## Authorization
- Required: Yes
- Type: JWT <PERSON>er Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/notification-management/get-notify-data`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "user_id": 1,
    "org_id": "44",
    "be_value": "1",
    "product": "Electronics",
    "inco_term": "FOB",
    "service": "Express",
    "order_type": "Import",
    "from_country": "USA",
    "to_country": "India",
    "notification_id": "NOTIFY001"
}
```

## Success Response

```json
[
    {
        "id": 1,
        "notification_id": "NOTIFY001",
        "user_id": 1,
        "org_id": "44",
        "be_value": "1",
        "product": "Electronics",
        "inco_term": "FOB",
        "service": "Express",
        "order_type": "Import",
        "from_country": "USA",
        "to_country": "India",
        "party_id": 0,
        "partycontact_id": 0,
        "all_note": false,
        "customer_id": 1,
        "party_type": "shipper",
        "party_type_id": 1,
        "booking_create": true,
        "booking_edit": false,
        "booking_delete": false,
        "trip_create": true,
        "trip_edit": true,
        "trip_delete": false,
        "driver_accept": true,
        "route_deviate": false,
        "speed": true,
        "temperature": false,
        "pickup_note": true,
        "delivery_note": true,
        "pod_note": false,
        "sms_note": true,
        "email_note": true,
        "whatsapp_note": false,
        "createdby": 1,
        "updatedby": 1,
        "status": 1,
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z"
    },
    {
        "id": 2,
        "notification_id": "NOTIFY001",
        "user_id": 1,
        "org_id": "44",
        "be_value": "1",
        "product": "Electronics",
        "inco_term": "FOB",
        "service": "Express",
        "order_type": "Import",
        "from_country": "USA",
        "to_country": "India",
        "party_id": 0,
        "partycontact_id": 0,
        "all_note": false,
        "customer_id": 1,
        "party_type": "consignee",
        "party_type_id": 2,
        "booking_create": false,
        "booking_edit": true,
        "booking_delete": false,
        "trip_create": false,
        "trip_edit": true,
        "trip_delete": false,
        "driver_accept": false,
        "route_deviate": true,
        "speed": false,
        "temperature": true,
        "pickup_note": false,
        "delivery_note": true,
        "pod_note": true,
        "sms_note": false,
        "email_note": true,
        "whatsapp_note": true,
        "createdby": 1,
        "updatedby": 1,
        "status": 1,
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z"
    }
]
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/get-notify-data' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "user_id": 1,
    "org_id": "44",
    "be_value": "1",
    "product": "Electronics",
    "inco_term": "FOB",
    "service": "Express",
    "order_type": "Import",
    "from_country": "USA",
    "to_country": "India"
}'
```

### cURL Example with Notification ID Filter:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/get-notify-data' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "user_id": 1,
    "org_id": "44",
    "be_value": "1",
    "product": "Electronics",
    "inco_term": "FOB",
    "service": "Express",
    "order_type": "Import",
    "from_country": "USA",
    "to_country": "India",
    "notification_id": "NOTIFY001"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Validation Error Response Example:
```json
{
    "status": "error",
    "message": "Validation failed.",
    "errors": {
        "user_id": ["The user id field is required."],
        "org_id": ["The org id field is required."],
        "product": ["The product field is required."],
        "inco_term": ["The inco term field is required."],
        "service": ["The service field is required."],
        "order_type": ["The order type field is required."],
        "from_country": ["The from country field is required."],
        "to_country": ["The to country field is required."]
    }
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Returns an array of notification records that match the specified criteria.
- All required fields must be provided for the search to work.
- Notification ID is optional and filters by specific notification.
- The response includes all notification settings and permissions for each record.
- Records are returned in the order they were created.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Data retrieval works correctly with filters
- `[Pass]` Notification ID filtering works properly

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified data retrieval works correctly with various filters
- Tested with different notification IDs
- Confirmed filtering functionality works properly

--- 