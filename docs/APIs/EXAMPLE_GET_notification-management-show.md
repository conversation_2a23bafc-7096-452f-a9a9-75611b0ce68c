# GET /api/notification-management/{id}

## Description
Retrieves a specific notification management record by its ID with all associated data and relationships.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET  
- Endpoint: `http://localhost:8000/api/notification-management/{id}`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

## Request Body: None

## Success Response

```json
{
    "status": "success",
    "message": "Notification management record retrieved successfully.",
    "data": {
        "id": 1,
        "notification_id": "NOTIFY001",
        "user_id": 1,
        "org_id": "44",
        "be_value": "1",
        "product": "Electronics",
        "inco_term": "FOB",
        "service": "Express",
        "order_type": "Import",
        "from_country": "USA",
        "to_country": "India",
        "party_id": 0,
        "partycontact_id": 0,
        "all_note": false,
        "customer_id": 0,
        "party_type": "shipper",
        "party_type_id": 1,
        "booking_create": true,
        "booking_edit": false,
        "booking_delete": false,
        "trip_create": true,
        "trip_edit": true,
        "trip_delete": false,
        "driver_accept": true,
        "route_deviate": false,
        "speed": true,
        "temperature": false,
        "pickup_note": true,
        "delivery_note": true,
        "pod_note": false,
        "sms_note": true,
        "email_note": true,
        "whatsapp_note": false,
        "createdby": 1,
        "updatedby": 1,
        "status": 1,
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z",
        "customer": null,
        "party": null
    }
}
```

### cURL Example:
```sh
curl --location --request GET 'http://localhost:8000/api/notification-management/1' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Not Found Response Example:
```json
{
    "status": "error",
    "message": "Notification management record not found."
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Only active records (status = 1) are returned.
- Soft-deleted records (status = 0) will return 404.
- The response includes all notification settings and boolean flags.
- Customer and party relationships are included when available.
- Debug information is logged for troubleshooting.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Non-existent ID returns 404 Not Found
- `[Pass]` Soft-deleted record returns 404 Not Found

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified record retrieval by ID works correctly
- Tested with various record IDs (existing and non-existing)
- Confirmed soft delete filtering works properly

--- 