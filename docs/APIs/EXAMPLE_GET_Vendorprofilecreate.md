# GET /api/vendor-profiles/create

## Description
Retrieves form data for creating a new vendor profile, including dropdown options for customers and departments.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET  
- Endpoint: `http://localhost:8000/api/vendor-profiles/create`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body: None

## Success Response

```json
{
    "status": "success",
    "message": "Vendor profile creation form data retrieved successfully.",
    "data": {
        "org_id": 44,
        "be_value": 1,
        "user_id": 1,
        "customers": [],
        "departments": []
    }
}
```

### cURL Example:
```sh
curl --location --request GET 'http://localhost:8000/api/vendor-profiles/create' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Customers list is filtered by user_id and active status.
- Departments list is filtered by org_id, be_value and active status.
- Form data includes default values for org_id, be_value, and user_id.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Customers dropdown populated correctly
- `[Pass]` Departments dropdown populated correctly

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 