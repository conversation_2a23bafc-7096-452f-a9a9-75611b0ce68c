# POST /api/notification-management/get-business-entities

## Description
Retrieves a list of business entities for autocomplete functionality based on search query and organization ID.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/notification-management/get-business-entities`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "query": "Electronics",
    "org_id": "44"
}
```

## Success Response

```json
[
    "1",
    "2",
    "3"
]
```

### Empty Response (No matches)
```json
[]
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/get-business-entities' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "query": "Electronics",
    "org_id": "44"
}'
```

### cURL Example with Business Entity Value:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/get-business-entities' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "query": "1",
    "org_id": "44"
}'
```

### cURL Example with Empty Query:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management/get-business-entities' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "query": "",
    "org_id": "44"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Server Error Response Example:
```json
[]
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Returns an array of business entity values that match the search query.
- Search is performed on both business_entity_name and be_value fields.
- Only active business entities (status = 1) are returned.
- Business entities are filtered by the specified organization ID.
- The search is case-insensitive and uses LIKE operator.
- Returns empty array if no matches are found.
- This endpoint is typically used for autocomplete functionality in forms.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Search functionality works correctly
- `[Pass]` Empty query returns all business entities for organization

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified search functionality works correctly
- Tested with various search queries
- Confirmed autocomplete functionality works properly

--- 