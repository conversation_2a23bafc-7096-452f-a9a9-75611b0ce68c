# POST /api/vendor-profiles/get-vendor-profile-id

## Description
Retrieves vendor profile IDs for autocomplete functionality based on a search term.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/vendor-profiles/get-vendor-profile-id`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "profile_id1": "VN44"
}
```

### Request Body Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| profile_id1 | string | No | Search term for vendor profile ID | "VN44" |

## Success Response

```json
[
    "VN4425280001",
    "VN4425280002",
    "VN4425280003"
]
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/vendor-profiles/get-vendor-profile-id' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "profile_id1": "VN44"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Search is case-insensitive and uses LIKE operator.
- Only active vendor profiles (status = 1) are returned.
- Search is restricted to vendor profiles owned by the authenticated user.
- Returns an array of matching vendor profile IDs.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return array of matching profile IDs
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Search functionality works correctly
- `[Pass]` Empty search returns all user's profile IDs

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 