# DELETE /api/vendor-profiles/{id}

## Description
Soft deletes a vendor profile by setting its status to 0 (inactive).

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: DELETE  
- Endpoint: `http://localhost:8000/api/vendor-profiles/{id}`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Path Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| id | integer | Yes | Vendor profile ID | 1 |

### Request Body: None

## Success Response

```json
{
    "status": "success",
    "message": "Vendor profile deleted successfully."
}
```

### cURL Example:
```sh
curl --location --request DELETE 'http://localhost:8000/api/vendor-profiles/1' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Not Found Response Example:
```json
{
    "status": "error",
    "message": "Vendor profile not found."
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- This is a soft delete operation - the record is not physically removed from the database.
- Only vendor profiles owned by the authenticated user can be deleted.
- Access is restricted based on org_id and be_value.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Non-existent ID returns 404 Not Found
- `[Pass]` Access control works correctly
- `[Pass]` Soft delete works correctly (status set to 0)

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 