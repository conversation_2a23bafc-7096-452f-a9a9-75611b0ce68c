# GET /api/vendor-profiles

## Description
Fetches a list of vendor profiles for the authenticated user.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET  
- Endpoint: `http://localhost:8000/api/vendor-profiles`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

## Request Body: None

## Success Response

```json
{
    "status": "success",
    "message": "Vendor profiles retrieved successfully.",
    "data": [
        {
            "id": 1,
            "vend_profile_id": "VN4425280001",
            "name": "Sample profile list sx",
            "description": "Profile description sx",
            "status": 1,
            "org_id": 44,
            "be_value": 1,
            "user_id": 1,
            "created_at": "2025-07-11T09:37:32.000000Z",
            "updated_at": "2025-07-11T09:51:15.000000Z"
        }
    ]
}
```

### cURL Example:
```sh
curl --location --request GET 'http://localhost:8000/api/vendor-profiles' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

### cURL Example with Search:
```sh
curl --location --request GET 'http://localhost:8000/api/vendor-profiles?searchsubmit=Search&name1=Example&fromdate=2025-01-01&todate=2025-12-31' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Optional request parameters should be handled gracefully if omitted.
- Search functionality is triggered by setting `searchsubmit=Search`.
- Date filters use Y-m-d format.
- Status filter: 1 for active, 0 for inactive.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Search filters work correctly
- `[Pass]` Date range filtering works properly

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 