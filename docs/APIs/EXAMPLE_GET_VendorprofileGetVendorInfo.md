# GET /api/vendor-profiles/get-vendor-info/{id}

## Description
Retrieves vendor information by vendor ID.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET  
- Endpoint: `http://localhost:8000/api/vendor-profiles/get-vendor-info/{id}`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body: None

## Success Response

```json
{
    "id": 1,
    "name": "Vendor A",
    "mobile": "1234567890",
    "address": "123 Main Street",
    "pincode": "12345",
    "code": "VN001"
}
```

### cURL Example:
```sh
curl --location --request GET 'http://localhost:8000/api/vendor-profiles/get-vendor-info/1' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Not Found Response Example:
```json
[]
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Returns vendor information from the party members table.
- Returns empty object if vendor not found.
- ID must be greater than 0.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return vendor information
- `[Pass]` Invalid or missing inputs should return empty object
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Non-existent ID returns empty object
- `[Pass]` ID less than or equal to 0 returns empty object

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 