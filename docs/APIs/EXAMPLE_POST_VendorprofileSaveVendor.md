# POST /api/vendor-profiles/save-vendor/{id?}

## Description
Saves or updates a vendor profile list item.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/vendor-profiles/save-vendor/{id?}`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Path Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| id | integer | No | Vendor profile list ID for update | 1 |

### Request Body

```json
{
    "code": "VN001",
    "party_id": 1,
    "venprid": 1
}
```

## Success Response

```json
{
    "id": 1
}
```

### cURL Example for Create:
```sh
curl --location --request POST 'http://localhost:8000/api/vendor-profiles/save-vendor' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "code": "VN001",
    "party_id": 1,
    "venprid": 1
}'
```

### cURL Example for Update:
```sh
curl --location --request POST 'http://localhost:8000/api/vendor-profiles/save-vendor/1' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "code": "VN001",
    "party_id": 1
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Validation Error Response Example:
```json
{
    "status": "error",
    "message": "Validation failed.",
    "errors": {
        "code": [
            "The code field is required."
        ],
        "party_id": [
            "The party id field is required."
        ]
    }
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- For new records (no ID in path), venprid is required.
- For updates (ID in path), venprid is not used.
- Timestamps are automatically set for created_at and updated_at.
- Status is automatically set to 1 for new records.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Create new vendor profile list item works correctly
- `[Pass]` Update existing vendor profile list item works correctly

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 