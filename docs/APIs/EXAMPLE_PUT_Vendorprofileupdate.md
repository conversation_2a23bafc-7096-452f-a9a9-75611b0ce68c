# PUT /api/vendor-profiles/{id}

## Description
Updates an existing vendor profile for the authenticated user.

## Authorization
- Required: Yes (Bearer token from login API)

## Request

- Method: PUT  
- Endpoint: `http://localhost:8000/api/vendor-profiles/{id}`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |


### Request Body

```json
{
    "vend_profile_id": 1,
    "profile_name": "Sample profile list sx rt",
    "description": "Profile description sx rt",
    "org_id": 1,
    "be_value": 0,
    "user_id": 1,
    "status": 1
}
```

## Success Response

```json
{
    "status": "success",
    "message": "Vendor profile updated successfully.",
    "data": {
        "id": 1,
        "vend_profile_id": "VN4425280001",
        "name": "Sample profile list sx rt",
        "description": "Profile description sx rt",
        "org_id": 44,
        "be_value": 1,
        "user_id": 1,
        "status": 1,
        "created_at": "2025-07-11T09:37:32.000000Z",
        "updated_at": "2025-07-11T10:17:41.000000Z"
    }
}
```

### cURL Example:
```sh
curl --location --request PUT 'http://localhost:8000/api/vendor-profiles/1' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "vend_profile_id": 1,
    "profile_name": "Sample profile list sx rt",
    "description": "Profile description sx rt",
    "org_id": 1,
    "be_value": 0,
    "user_id": 1,
    "status": 1
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Validation Error Response Example:
```json
{
    "status": "error",
    "message": "Validation failed.",
    "errors": {
        "profile_name": [
            "The profile name field is required."
        ]
    }
}
```

### Not Found Response Example:
```json
{
    "status": "error",
    "message": "Vendor profile not found."
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Optional request parameters should be handled gracefully if omitted.
- org_id and be_value are automatically set based on user context.
- Only vendor profiles owned by the authenticated user can be updated.
- Status values: 1 for active, 0 for inactive.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Non-existent ID returns 404 Not Found
- `[Pass]` Access control works correctly
- `[Pass]` Profile data is updated correctly

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 