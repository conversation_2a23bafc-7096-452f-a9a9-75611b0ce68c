# POST /api/login

## Description
Fetches the role-based login structure for the authenticated user.

## Authorization
- Required: No

## Request

- Method: POST
- Endpoint: `{base_url}/api/login`
- Headers:

| Key             | Value                        | Required |
|----------------|------------------------------|----------|
| Accept-Language| `en` / `th` / other supported | Optional |
| Content-Type   | application/json              | Yes      |

- Request Body:
{ 
  "username": "<EMAIL>", 
  "password": "RAMA001" 
}

## Success Response

```json
{
    "user": {
        "user_id": 1,
        "emp_id": "RAMA001",
        "emp_name": "RAMA001",
        "org_id": 1,
        "org_name": "Ram<PERSON><PERSON>",
        "companyname": "Ram<PERSON>o",
        "org_logo": "ramco.jpg",
        "email_id": "kamb<PERSON><PERSON><EMAIL>",
        "user_orgs": {
            "id": 1,
            "org_id": 1,
            "structure_id": 1,
            "entity_id": 1,
            "entity_value_id": 1,
            "roles": "[\"1\"]",
            "created_by": 0,
            "updated_by": 0,
            "deleted_at": null,
            "user_id": 1,
            "status": 1,
            "created_at": null,
            "updated_at": null
        },
        "usertype": "user",
        "default_org_privilege_ids": [
            "1"
        ],
        "default_org_structures": [
            {
                "id": 1
            }
        ],
        "is_super_user": false,
        "part_type": "",
        "site_lang": "english",
        "map_type": 1,
        "role_id": 1,
        "previleges": [
            "1"
        ],
        "distance_unit": "km"
    },
    "access_token": "<your_token_here>",
    "refresh_token": null,
    "token_type": "Bearer",
    "expires_in": 3600
}
```

### cURL Example:
```sh
curl --location --request POST 'http://backend/api/login' \
--header 'Content-Type: application/json' \
--data-raw '{ "username": "<EMAIL>", "password": "RAMA001" }'


```

---

## Error Responses

| Code | Message               | Description                          |
|------|-----------------------|--------------------------------------|
| 401  | Unauthorized          | Invalid or missing JWT token         |
| 400  | Bad Request           | Invalid or missing request payload   |

---

## Notes
- The Accept-Language header determines the language of error messages, defaulting to en if unsupported.
- The JWT token is issued with issuer (iss) as shipmentx.com and audience (aud) as tms.xai.com.
- Token includes user ID (sub), email ( and distance_id), and role ID (role_id) in the payload.
- The token expiration time (exp) is set to one hour from issuance (iat).
- The response is not cached to ensure fresh authentication data.

## Tests

### Functional Tests
- ``[Pass]`` Valid request payload returns 200 status code and valid token
- `[Pass]` Invalid/missing payload returns 400/422 status code
- `[Pass]` Invalid email or password returns 401 status code

### CORS & Header Behavior
- `[]` Access-Control-Allow-Origin header present in response
- `[Pass]` Preflight OPTIONS request responded with 204 and correct headers
- `[Fail]` Accept-Language set to 'th' → returns translated login
- `[]` Unsupported Accept-Language → defaults to 'en'

### Manual Verification
- `[]` Tested in Postman with valid credentials
- `[]` Response structure validated with token, status, and user details
- `[]` Edge case: Invalid account type → returns appropriate error message

### Notes
- Tests performed using Postman with environment variables.
- JWT payload manually inspected for correct issuer, audience, and claims
- Edge cases tested: empty email, incorrect password, invalid account_type
