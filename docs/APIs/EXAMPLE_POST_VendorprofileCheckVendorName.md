# POST /api/vendor-profiles/check-vendor-name

## Description
Checks if a vendor profile name is unique for the authenticated user.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/vendor-profiles/check-vendor-name`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "name": "Example Vendor",
    "id": "",
    "org_id": "44",
    "be_value": "1"
}
```

### Request Body Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| name | string | Yes | Vendor profile name to check | "Example Vendor" |
| id | string | No | Vendor profile ID for update (exclude from check) | "1" |
| org_id | string | Yes | Organization ID | "44" |
| be_value | string | Yes | Business entity value | "1" |

## Success Response

### Name Available (Unique):
```json
"2"
```

### Name Already Exists:
```json
"1"
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/vendor-profiles/check-vendor-name' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "name": "Example Vendor",
    "id": "",
    "org_id": "44",
    "be_value": "1"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Returns "2" if name is available (unique).
- Returns "1" if name already exists.
- Check excludes the current record when updating (if id is provided).
- Only active vendor profiles (status = 1) are considered.
- Check is case-sensitive and exact match.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return "2" for unique names
- `[Pass]` Valid inputs should return "1" for existing names
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Update scenario excludes current record from check

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 