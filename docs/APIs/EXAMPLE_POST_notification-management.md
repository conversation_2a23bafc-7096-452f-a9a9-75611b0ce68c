# POST /api/notification-management

## Description
Creates a new notification management record with comprehensive notification settings for different business scenarios.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/notification-management`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "user_id": 1,
    "org_id": "44",
    "be_value": "1",
    "product": "Electronics",
    "inco_term": "FOB",
    "service": "Express",
    "order_type": "Import",
    "from_country": "USA",
    "to_country": "India",
    "party_id": 0,
    "partycontact_id": 0,
    "all_note": false,
    "customer_id": 0,
    "party_type": "shipper",
    "party_type_id": 1,
    "booking_create": true,
    "booking_edit": false,
    "booking_delete": false,
    "trip_create": true,
    "trip_edit": true,
    "trip_delete": false,
    "driver_accept": true,
    "route_deviate": false,
    "speed": true,
    "temperature": false,
    "pickup_note": true,
    "delivery_note": true,
    "pod_note": false,
    "sms_note": true,
    "email_note": true,
    "whatsapp_note": false
}
```

## Success Response

```json
{
    "status": "success",
    "message": "Notification management record created successfully.",
    "data": {
        "id": 1,
        "notification_id": "NOTIFY001",
        "user_id": 1,
        "org_id": "44",
        "be_value": "1",
        "product": "Electronics",
        "inco_term": "FOB",
        "service": "Express",
        "order_type": "Import",
        "from_country": "USA",
        "to_country": "India",
        "party_id": 0,
        "partycontact_id": 0,
        "all_note": false,
        "customer_id": 0,
        "party_type": "shipper",
        "party_type_id": 1,
        "booking_create": true,
        "booking_edit": false,
        "booking_delete": false,
        "trip_create": true,
        "trip_edit": true,
        "trip_delete": false,
        "driver_accept": true,
        "route_deviate": false,
        "speed": true,
        "temperature": false,
        "pickup_note": true,
        "delivery_note": true,
        "pod_note": false,
        "sms_note": true,
        "email_note": true,
        "whatsapp_note": false,
        "createdby": 1,
        "updatedby": 1,
        "status": 1,
        "created_at": "2024-01-15T10:30:00.000000Z",
        "updated_at": "2024-01-15T10:30:00.000000Z"
    }
}
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "user_id": 1,
    "org_id": "44",
    "be_value": "1",
    "product": "Electronics",
    "inco_term": "FOB",
    "service": "Express",
    "order_type": "Import",
    "from_country": "USA",
    "to_country": "India",
    "party_type": "shipper",
    "party_type_id": 1,
    "booking_create": true,
    "trip_create": true,
    "trip_edit": true,
    "driver_accept": true,
    "speed": true,
    "pickup_note": true,
    "delivery_note": true,
    "sms_note": true,
    "email_note": true
}'
```

### cURL Example with Minimal Data:
```sh
curl --location --request POST 'http://localhost:8000/api/notification-management' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "user_id": 1,
    "org_id": "44",
    "be_value": "1",
    "product": "Electronics",
    "inco_term": "FOB",
    "service": "Express",
    "order_type": "Import",
    "from_country": "USA",
    "to_country": "India"
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Validation Error Response Example:
```json
{
    "status": "error",
    "message": "Validation failed.",
    "errors": {
        "user_id": ["The user id field is required."],
        "org_id": ["The org id field is required."],
        "product": ["The product field is required."]
    }
}
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- Optional request parameters should be handled gracefully if omitted.
- The notification_id is automatically generated if not provided.
- All boolean fields default to false except booking_create, trip_create, trip_edit, driver_accept, speed, pickup_note, delivery_note, sms_note, and email_note which default to true.
- Records are soft-deleted (status = 0) rather than physically removed.
- The combination of user_id, org_id, be_value, product, inco_term, service, order_type, from_country, and to_country must be unique.
- All input fields are returned in the response for verification.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return `status: success` with correct payload
- `[Pass]` Invalid or missing inputs should return appropriate HTTP error
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Notification ID auto-generation works correctly
- `[Pass]` Duplicate combination validation works properly

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified all required fields are properly validated
- Tested notification_id auto-generation
- Confirmed soft delete functionality works correctly

--- 