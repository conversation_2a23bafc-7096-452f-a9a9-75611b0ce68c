# POST /api/vendor-profiles/delete-profile-details

## Description
Soft deletes a vendor profile list item by setting its status to 0 (inactive).

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: POST  
- Endpoint: `http://localhost:8000/api/vendor-profiles/delete-profile-details`  
- Headers:

| Key           | Value                               | Required |
|---------------|-------------------------------------|----------|
| Authorization | Bearer `<token from login API>`     | Yes      |
| Content-Type  | application/json                    | Yes      |

### Request Body

```json
{
    "id": 1
}
```

## Success Response

```json
"1"
```

### cURL Example:
```sh
curl --location --request POST 'http://localhost:8000/api/vendor-profiles/delete-profile-details' \
--header 'Authorization: Bearer <your_token_here>' \
--header 'Content-Type: application/json' \
--data-raw '{
    "id": 1
}'
```

---

## Error Responses (Common)

| Code | Message           | Description                                        |
|------|-------------------|----------------------------------------------------|
| 400  | Bad Request       | Invalid or missing required fields or payload      |
| 401  | Unauthorized      | Invalid, expired, or missing authentication token   |
| 404  | Not Found         | Resource not found or invalid reference provided   |
| 422  | Validation Error  | Input validation failed                            |
| 500  | Server Error      | Internal server error                              |

### Error Response Example:
```json
"0"
```

---

## Notes 

- All endpoints return JSON responses.
- Most secured endpoints require a Bearer token in the `Authorization` header.
- Language localization can be influenced by the `Accept-Language` header (default is English).
- This is a soft delete operation - the record is not physically removed from the database.
- Returns "1" for successful deletion, "0" for failure.
- Only vendor profile list items that exist can be deleted.

---

## Tests 

### Functional Tests
- `[Pass]` Valid inputs should return "1" for successful deletion
- `[Pass]` Invalid or missing inputs should return "0"
- `[Pass]` Expired or malformed tokens return 401 Unauthorized
- `[Pass]` Non-existent ID returns "0"
- `[Pass]` Soft delete works correctly (status set to 0)

### CORS & Header Behavior
- `[Pass]` `OPTIONS` preflight responds with 204 and correct headers
- `[]` `Access-Control-Allow-Origin` should be verified on response
- `[Fail]` Accept-Language set to unsupported locale defaults to English

### Manual Verification
- `[]` Validate all scenarios in Postman or Swagger UI
- `[]` Check token validity for secured endpoints
- `[]` Ensure meaningful error messages are returned for edge cases

### Notes
- Tests performed using Postman with token from login API
- Confirmed response structure includes all expected data groups
- Verified with no request body and confirmed backward compatibility 
- Edge cases tested: expired token, unexpected status codes, missing headers

--- 