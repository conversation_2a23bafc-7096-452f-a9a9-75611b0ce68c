# GET /api/menu

## Description
Fetches the role-based menu structure for the authenticated user.

## Authorization
- Required: Yes
- Type: JWT Bearer Token

## Request

- Method: GET
- Endpoint: `{base_url}/api/menu`
- Headers:

| Key             | Value                        | Required |
|----------------|------------------------------|----------|
| Authorization  | Bearer `<JWT Token>`         | Yes       |
| Accept-Language| `en` / `th` / other supported | Optional |
| Content-Type   | application/json              | Yes       |

_No request body is required._

## Success Response

```json
{
    "success": "success",
    "menus": [
        {
            "id": "Booking",
            "children": [
                {
                    "id": "Orders"
                },
                {
                    "id": "Bulk Update"
                },
                {
                    "id": "Docuemnt Control"
                },
                {
                    "id": "Bulk Update"
                },
                {
                    "id": "<PERSON>lletizer"
                },
                {
                    "id": "Claims"
                },
                {
                    "id": "Ports"
                }
            ]
        },
        {
            "id": "Masters",
            "children": [
                {
                    "id": "Communication Management"
                },
                {
                    "id": "Fleet",
                    "children": [
                        {
                            "id": "Drivers"
                        },
                        {
                            "id": "Vehicles"
                        },
                        {
                            "id": "Order Type"
                        },
                        {
                            "id": "Cost Center"
                        },
                        {
                            "id": "Vehicle Type"
                        }
                    ]
                },
                {
                    "id": "Business Partners"
                },
                {
                    "id": "Traffic Code"
                },
                {
                    "id": "Status Master"
                },
                {
                    "id": "Lane Master"
                },
                {
                    "id": "Profiles",
                    "children": [
                        {
                            "id": "Customer Profile"
                        },
                        {
                            "id": "Vendor Profile"
                        },
                        {
                            "id": "Vehicle Profile"
                        }
                    ]
                },
                {
                    "id": "Pincodes",
                    "children": [
                        {
                            "id": "Pincodes"
                        },
                        {
                            "id": "Preferred State"
                        },
                        {
                            "id": "Trip Allocation Ratio"
                        }
                    ]
                },
                {
                    "id": "Allocation Rules"
                },
                {
                    "id": "Shipment Types"
                },
                {
                    "id": "Regions"
                }
            ]
        },
        {
            "id": "Tender",
            "children": [
                {
                    "id": "Shipment Tender"
                },
                {
                    "id": "Carrier Rates"
                }
            ]
        }
    ]
}
```

### cURL Example:
```sh
curl --location --request GET 'http://backend/api/menu' \
--header 'Authorization: Bearer <your_token_here>'


```

---

## Error Responses

| Code | Message               | Description                          |
|------|-----------------------|--------------------------------------|
| 401  | Unauthorized          | Invalid or missing JWT token         |
| 500  | Internal Server Error | Failed to fetch menu data            |

---

## Notes
- Menu items and pages are translated based on the `Accept-Language` header.
- The result is cached using `Cache_ui` to optimize performance.
- JWT is validated using issuer, audience, and expiration time.
- If cached menu data exists, it is served directly from cache.

## Tests

### Functional Tests
- ``[Pass]`` Tested with valid request payload (status 200)
- `[Pass]` Tested with invalid/missing payload (status 400/422)
- `[Pass]` JWT token tested: valid, expired, invalid (401)

### CORS & Header Behavior
- `[]` Access-Control-Allow-Origin header present in response
- `[Pass]` Preflight OPTIONS request responded with 204 and correct headers
- `[Fail]` Accept-Language set to 'th' → returns translated menu
- `[]` Unsupported Accept-Language → defaults to 'en'

### Manual Verification
- `[]` Tested in Postman with Auth token
- `[]` Response matches expected structure with menus array and localized names
- `[]` Edge case: No menu items for role ID → returns empty array

### Notes
- Used Postman with environment token
- JWT tested via tampering (edited payload to simulate expiry)
- Verified cache is served after initial request
