POSTGRES_DB=shipmentx-tms
POSTGRES_USER=root
POSTGRES_PASSWORD=shipmentx@2025secure

PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin2025
PGADMIN_PORT=5050

APP_ENV=local
APP_DEBUG=true

DB_HOST=postgres

# Development mode (single-node, no ACL/TLS, UI exposed)
CONSUL_AGENT_TOKEN=77eff2ea-5958-cbe4-c7ea-de6c3c52ef3b
CONSUL_FLAGS="-server -client=0.0.0.0 -bootstrap-expect=1"
# CONSUL_IP=**********

REDIS_PORT=6379

# RabbitMQ Service Credentials
RABBITMQ_DEFAULT_USER=rabbit
RABBITMQ_DEFAULT_PASS=shRabbit2025
RABBITMQ_DEFAULT_VHOST=/