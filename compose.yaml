services:
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:80"
    env_file:
      - ./backend/.env
    volumes:
      # - backend_data:/var/www/html
      - ./backend/.env:/var/www/html/.env:ro
      - ./backend/storage/logs:/var/www/html/storage/logs
    environment:
      APP_ENV: ${APP_ENV}
      DB_HOST: ${DB_HOST}
    develop:
      watch:
        - path: ./backend
          target: /var/www/html
          action: sync
          ignore:
            - vendor
        - path: ./backend/composer.json
          target: /var/www/html/composer.json
          action: sync+exec
          exec:
            command: composer install --no-interaction --prefer-dist --optimize-autoloader
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - tms

  sx-admin:
    build:
      context: ./sx-admin
      dockerfile: Dockerfile
    ports:
      - "8010:80"
    environment:
      APP_ENV: ${APP_ENV}
      APP_DEBUG: ${APP_DEBUG}
      DB_HOST: ${DB_HOST}
      DB_PORT: 5432
      DB_DATABASE: ${POSTGRES_DB}
      DB_USERNAME: ${POSTGRES_USER}
      DB_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - ./sx-admin/application/logs:/var/www/html/application/logs
      - ./sx-admin/uploads:/var/www/html/uploads
    develop:
      watch:
        - path: ./sx-admin
          target: /var/www/html
          action: sync
          ignore:
            - vendor
            - uploads
            - application/logs
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - tms

  postgres:
    image: postgres:16.9-bullseye
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"]
      interval: 5s
      timeout: 3s
      retries: 5
    networks:
      - tms

  pgadmin:
    image: dpage/pgadmin4:9.4.0
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_DEFAULT_EMAIL}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_DEFAULT_PASSWORD}
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - tms
  
  consul:
    image: hashicorp/consul:1.21.2
    container_name: consul
    ports:
      - "8500:8500"   # UI/API
      - "8600:8600/udp" # DNS
    volumes:
      - consul-data:/consul/data
      - ./consul/config/agent.hcl:/consul/config/agent.hcl:ro
      # - ./consul/config/services:/consul/config/services:ro
      - ./consul/config/tms-backend.hcl:/consul/config/tms-backend.hcl:ro
    command: "agent -config-dir=/consul/config"
    networks:
      - tms

  redis:
    image: redis:8.0.2-alpine
    container_name: tms-redis
    ports:
      - "${REDIS_PORT}:6379"   # Optional: expose if needed for testing
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf"]
    restart: unless-stopped
    networks:
      - tms

  rabbitmq:
    image: rabbitmq:4.1.2-management
    container_name: rabbitmq
    restart: unless-stopped
    ports:
      - "5672:5672"  
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: ${RABBITMQ_DEFAULT_USER}
      RABBITMQ_DEFAULT_PASS: ${RABBITMQ_DEFAULT_PASS}
      RABBITMQ_DEFAULT_VHOST: ${RABBITMQ_DEFAULT_VHOST}
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "status"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - tms

volumes:
  pgdata:
  consul-data:
  redis_data:
  rabbitmq_data:
  # backend_data:
  # sxadmin_data:
  pgadmin_data:

networks:
  tms:
    driver: bridge

